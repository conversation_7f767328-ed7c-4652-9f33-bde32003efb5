{"version": 3, "file": "isUtf8.js", "sourceRoot": "", "sources": ["../../../src/buffers/utf8/isUtf8.ts"], "names": [], "mappings": ";;;AAKO,MAAM,MAAM,GAAG,CAAC,GAAe,EAAE,IAAY,EAAE,MAAc,EAAW,EAAE;IAC/E,MAAM,EAAE,GAAG,IAAI,GAAG,MAAM,CAAC;IACzB,OAAO,IAAI,GAAG,EAAE,EAAE,CAAC;QACjB,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACd,IAAI,EAAE,CAAC;YACP,SAAS;QACX,CAAC;QACD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,IAAI,CAAC,CAAC;gBACV,SAAS;YACX,CAAC;;gBAAM,OAAO,KAAK,CAAC;QACtB,CAAC;QACD,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACzB,IACE,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;YACtF,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EACxB,CAAC;YACD,IAAI,IAAI,CAAC,CAAC;YACV,SAAS;QACX,CAAC;QACD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACxG,IAAI,IAAI,CAAC,CAAC;YACV,SAAS;QACX,CAAC;QACD,IACE,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;YACvC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;YAC3C,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACxB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EACxB,CAAC;YACD,IAAI,IAAI,CAAC,CAAC;YACV,SAAS;QACX,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAvCW,QAAA,MAAM,UAuCjB"}