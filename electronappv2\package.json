{"name": "propertyclub-ai", "productName": "PropertyClub AI", "version": "1.0.0", "description": "AI-powered real estate assistant for Bangalore property market", "main": "dist/main/main/main.js", "scripts": {"start": "npm run build && electron-forge start", "dev": "concurrently \"npm run build:watch\" \"npm run serve:renderer\" \"npm run start:electron:dev\"", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "webpack --config webpack.renderer.config.js", "build:watch": "npm run build:main -- --watch", "serve:renderer": "webpack serve --config webpack.renderer.config.js", "start:electron": "electron-forge start", "start:electron:dev": "wait-on http://localhost:3000 && electron-forge start", "package": "npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "clean": "rimraf dist out .webpack"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@reduxjs/toolkit": "^2.0.0", "axios": "^1.6.0", "chart.js": "^4.5.0", "date-fns": "^3.0.0", "electron-squirrel-startup": "^1.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.300.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-redux": "^9.0.0", "react-router-dom": "^6.8.0", "recharts": "^2.8.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.6", "tailwindcss": "^3.4.0"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron/fuses": "^1.8.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@types/jest": "^29.5.0", "@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.0", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "37.2.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "postcss": "^8.4.0", "postcss-loader": "^7.3.0", "prettier": "^3.1.0", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "ts-jest": "^29.1.0", "ts-loader": "^9.5.0", "typescript": "^5.3.0", "wait-on": "^8.0.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^5.2.2"}}