"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentAnalysisService = void 0;
const geminiService_1 = require("./geminiService");
/**
 * Document Analysis Service
 * Provides document analysis capabilities for property documents, legal papers, and market reports
 */
class DocumentAnalysisService {
    constructor() {
        this.supportedFormats = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'];
        this.analysisCache = new Map();
    }
    /**
     * Initialize the document analysis service
     */
    async initialize() {
        try {
            console.log('Document analysis service initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize document analysis service:', error);
            throw error;
        }
    }
    /**
     * Analyze property document
     */
    async analyzePropertyDocument(filePath, documentType) {
        try {
            const cacheKey = `${filePath}_${documentType}`;
            const cached = this.analysisCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < 3600000) { // 1 hour cache
                return cached.data;
            }
            // Extract text from document
            const extractedText = await this.extractTextFromDocument(filePath);
            // Analyze based on document type
            let analysis;
            switch (documentType.toLowerCase()) {
                case 'sale_deed':
                    analysis = await this.analyzeSaleDeed(extractedText);
                    break;
                case 'title_deed':
                    analysis = await this.analyzeTitleDeed(extractedText);
                    break;
                case 'rera_certificate':
                    analysis = await this.analyzeReraCertificate(extractedText);
                    break;
                case 'building_plan':
                    analysis = await this.analyzeBuildingPlan(extractedText);
                    break;
                case 'property_valuation':
                    analysis = await this.analyzePropertyValuation(extractedText);
                    break;
                case 'legal_opinion':
                    analysis = await this.analyzeLegalOpinion(extractedText);
                    break;
                default:
                    analysis = await this.analyzeGenericDocument(extractedText, documentType);
            }
            const result = {
                documentType,
                filePath,
                analysis,
                extractedText: extractedText.substring(0, 1000), // First 1000 chars for reference
                confidence: analysis.confidence || 0.8,
                analyzedAt: new Date().toISOString()
            };
            this.analysisCache.set(cacheKey, { data: result, timestamp: Date.now() });
            return result;
        }
        catch (error) {
            console.error('Error analyzing property document:', error);
            throw error;
        }
    }
    /**
     * Analyze legal document
     */
    async analyzeLegalDocument(filePath) {
        try {
            const extractedText = await this.extractTextFromDocument(filePath);
            const analysis = await this.performLegalAnalysis(extractedText);
            return {
                documentType: 'legal',
                filePath,
                analysis,
                legalIssues: analysis.issues || [],
                recommendations: analysis.recommendations || [],
                riskLevel: analysis.riskLevel || 'medium',
                analyzedAt: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error analyzing legal document:', error);
            throw error;
        }
    }
    /**
     * Analyze market report
     */
    async analyzeMarketReport(filePath) {
        try {
            const extractedText = await this.extractTextFromDocument(filePath);
            const analysis = await this.performMarketAnalysis(extractedText);
            return {
                documentType: 'market_report',
                filePath,
                analysis,
                keyInsights: analysis.insights || [],
                marketTrends: analysis.trends || {},
                priceData: analysis.priceData || {},
                analyzedAt: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error analyzing market report:', error);
            throw error;
        }
    }
    /**
     * Extract key information from multiple documents
     */
    async extractKeyInformation(filePaths) {
        try {
            const analyses = await Promise.all(filePaths.map(async (filePath) => {
                const extractedText = await this.extractTextFromDocument(filePath);
                return {
                    filePath,
                    keyInfo: await this.extractKeyInfo(extractedText)
                };
            }));
            return {
                documents: analyses,
                summary: this.generateDocumentSummary(analyses),
                extractedAt: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error extracting key information:', error);
            throw error;
        }
    }
    /**
     * Validate document authenticity
     */
    async validateDocumentAuthenticity(filePath) {
        try {
            const extractedText = await this.extractTextFromDocument(filePath);
            const validation = await this.performAuthenticityCheck(extractedText);
            return {
                filePath,
                isAuthentic: validation.isAuthentic,
                confidence: validation.confidence,
                issues: validation.issues || [],
                recommendations: validation.recommendations || [],
                validatedAt: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error validating document authenticity:', error);
            throw error;
        }
    }
    /**
     * Compare multiple documents
     */
    async compareDocuments(filePaths) {
        try {
            const documents = await Promise.all(filePaths.map(async (filePath) => {
                const extractedText = await this.extractTextFromDocument(filePath);
                return {
                    filePath,
                    content: extractedText,
                    keyInfo: await this.extractKeyInfo(extractedText)
                };
            }));
            const comparison = await this.performDocumentComparison(documents);
            return {
                documents: filePaths,
                comparison,
                discrepancies: comparison.discrepancies || [],
                similarities: comparison.similarities || [],
                comparedAt: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error comparing documents:', error);
            throw error;
        }
    }
    /**
     * Extract text from document (mock implementation)
     */
    async extractTextFromDocument(filePath) {
        // In a real implementation, this would use OCR libraries like Tesseract.js
        // or PDF parsing libraries like pdf-parse
        const fileExtension = filePath.split('.').pop()?.toLowerCase();
        if (!this.supportedFormats.includes(fileExtension || '')) {
            throw new Error(`Unsupported file format: ${fileExtension}`);
        }
        // Mock extracted text based on file type
        switch (fileExtension) {
            case 'pdf':
                return this.getMockPdfText(filePath);
            case 'doc':
            case 'docx':
                return this.getMockDocText(filePath);
            case 'txt':
                return this.getMockTxtText(filePath);
            case 'jpg':
            case 'jpeg':
            case 'png':
                return this.getMockImageText(filePath);
            default:
                return 'Mock extracted text content';
        }
    }
    /**
     * Analyze sale deed document
     */
    async analyzeSaleDeed(text) {
        const prompt = `
    Analyze this sale deed document and extract the following information:
    - Property details (address, area, boundaries)
    - Seller and buyer information
    - Sale price and payment terms
    - Legal compliance and stamp duty
    - Any encumbrances or liens
    - Registration details
    
    Document text: ${text.substring(0, 2000)}
    
    Provide a structured analysis with confidence scores.
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            propertyDetails: this.extractPropertyDetails(text),
            parties: this.extractParties(text),
            financialDetails: this.extractFinancialDetails(text),
            legalCompliance: this.checkLegalCompliance(text),
            confidence: 0.85,
            aiAnalysis: response.response
        };
    }
    /**
     * Analyze title deed document
     */
    async analyzeTitleDeed(text) {
        const prompt = `
    Analyze this title deed document and extract:
    - Property ownership history
    - Title chain verification
    - Encumbrances and liens
    - Survey numbers and boundaries
    - Registration details
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            ownershipHistory: this.extractOwnershipHistory(text),
            titleChain: this.verifyTitleChain(text),
            encumbrances: this.extractEncumbrances(text),
            surveyDetails: this.extractSurveyDetails(text),
            confidence: 0.82,
            aiAnalysis: response.response
        };
    }
    /**
     * Analyze RERA certificate
     */
    async analyzeReraCertificate(text) {
        const prompt = `
    Analyze this RERA certificate and extract:
    - RERA registration number
    - Project details and developer information
    - Completion timeline
    - Approved plans and specifications
    - Compliance status
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            reraNumber: this.extractReraNumber(text),
            projectDetails: this.extractProjectDetails(text),
            developer: this.extractDeveloperInfo(text),
            timeline: this.extractTimeline(text),
            compliance: this.checkReraCompliance(text),
            confidence: 0.88,
            aiAnalysis: response.response
        };
    }
    /**
     * Analyze building plan
     */
    async analyzeBuildingPlan(text) {
        // For building plans, we'd typically analyze images/drawings
        // This is a simplified text-based analysis
        return {
            approvalDetails: this.extractApprovalDetails(text),
            specifications: this.extractBuildingSpecs(text),
            compliance: this.checkBuildingCompliance(text),
            confidence: 0.75
        };
    }
    /**
     * Analyze property valuation report
     */
    async analyzePropertyValuation(text) {
        const prompt = `
    Analyze this property valuation report and extract:
    - Valuation amount and methodology
    - Property specifications and condition
    - Market comparisons
    - Valuer credentials and date
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            valuationAmount: this.extractValuationAmount(text),
            methodology: this.extractValuationMethod(text),
            propertyCondition: this.extractPropertyCondition(text),
            marketComparisons: this.extractMarketComparisons(text),
            confidence: 0.83,
            aiAnalysis: response.response
        };
    }
    /**
     * Analyze legal opinion
     */
    async analyzeLegalOpinion(text) {
        const prompt = `
    Analyze this legal opinion document and extract:
    - Legal issues identified
    - Recommendations and advice
    - Risk assessment
    - Compliance status
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            legalIssues: this.extractLegalIssues(text),
            recommendations: this.extractRecommendations(text),
            riskAssessment: this.extractRiskAssessment(text),
            confidence: 0.80,
            aiAnalysis: response.response
        };
    }
    /**
     * Analyze generic document
     */
    async analyzeGenericDocument(text, documentType) {
        const prompt = `
    Analyze this ${documentType} document and provide:
    - Key information and highlights
    - Important dates and numbers
    - Parties involved
    - Any legal or financial implications
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            keyInformation: this.extractGenericKeyInfo(text),
            importantDates: this.extractDates(text),
            parties: this.extractParties(text),
            confidence: 0.70,
            aiAnalysis: response.response
        };
    }
    /**
     * Perform legal analysis
     */
    async performLegalAnalysis(text) {
        const prompt = `
    Perform a legal analysis of this document and identify:
    - Legal issues and concerns
    - Compliance with regulations
    - Risk factors
    - Recommendations for action
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            issues: this.identifyLegalIssues(text),
            compliance: this.checkLegalCompliance(text),
            riskLevel: this.assessLegalRisk(text),
            recommendations: this.generateLegalRecommendations(text),
            aiAnalysis: response.response
        };
    }
    /**
     * Perform market analysis
     */
    async performMarketAnalysis(text) {
        const prompt = `
    Analyze this market report and extract:
    - Market trends and insights
    - Price data and forecasts
    - Key market indicators
    - Investment recommendations
    
    Document text: ${text.substring(0, 2000)}
    `;
        const response = await geminiService_1.geminiService.query({ prompt });
        return {
            insights: this.extractMarketInsights(text),
            trends: this.extractMarketTrends(text),
            priceData: this.extractPriceData(text),
            indicators: this.extractMarketIndicators(text),
            aiAnalysis: response.response
        };
    }
    // Mock text extraction methods
    getMockPdfText(filePath) {
        return `Mock PDF content from ${filePath}. This would contain extracted text from a PDF document including property details, legal clauses, and other relevant information.`;
    }
    getMockDocText(filePath) {
        return `Mock DOC content from ${filePath}. This would contain text extracted from a Word document with property information and legal details.`;
    }
    getMockTxtText(filePath) {
        return `Mock TXT content from ${filePath}. This would contain plain text with property and legal information.`;
    }
    getMockImageText(filePath) {
        return `Mock OCR text from image ${filePath}. This would contain text extracted from an image using OCR technology.`;
    }
    // Mock extraction methods (in a real implementation, these would use NLP and pattern matching)
    extractPropertyDetails(text) {
        return {
            address: 'Mock Property Address',
            area: '1200 sq ft',
            boundaries: 'North: Road, South: Plot 123, East: Plot 125, West: Plot 121'
        };
    }
    extractParties(text) {
        return {
            seller: 'Mock Seller Name',
            buyer: 'Mock Buyer Name'
        };
    }
    extractFinancialDetails(text) {
        return {
            salePrice: 5000000,
            stampDuty: 250000,
            registrationFee: 25000
        };
    }
    checkLegalCompliance(text) {
        return {
            isCompliant: true,
            issues: [],
            recommendations: []
        };
    }
    // Additional mock methods would be implemented here...
    extractOwnershipHistory(text) { return {}; }
    verifyTitleChain(text) { return {}; }
    extractEncumbrances(text) { return []; }
    extractSurveyDetails(text) { return {}; }
    extractReraNumber(text) { return 'PRM/KA/RERA/1251/446/PR/010119/002058'; }
    extractProjectDetails(text) { return {}; }
    extractDeveloperInfo(text) { return {}; }
    extractTimeline(text) { return {}; }
    checkReraCompliance(text) { return {}; }
    extractApprovalDetails(text) { return {}; }
    extractBuildingSpecs(text) { return {}; }
    checkBuildingCompliance(text) { return {}; }
    extractValuationAmount(text) { return 5500000; }
    extractValuationMethod(text) { return 'Comparative Market Analysis'; }
    extractPropertyCondition(text) { return 'Good'; }
    extractMarketComparisons(text) { return []; }
    extractLegalIssues(text) { return []; }
    extractRecommendations(text) { return []; }
    extractRiskAssessment(text) { return {}; }
    extractGenericKeyInfo(text) { return {}; }
    extractDates(text) { return []; }
    identifyLegalIssues(text) { return []; }
    assessLegalRisk(text) { return 'low'; }
    generateLegalRecommendations(text) { return []; }
    extractMarketInsights(text) { return []; }
    extractMarketTrends(text) { return {}; }
    extractPriceData(text) { return {}; }
    extractMarketIndicators(text) { return {}; }
    extractKeyInfo(text) { return {}; }
    generateDocumentSummary(analyses) { return {}; }
    performAuthenticityCheck(text) { return { isAuthentic: true, confidence: 0.85 }; }
    performDocumentComparison(documents) { return {}; }
}
// Export singleton instance
exports.documentAnalysisService = new DocumentAnalysisService();
//# sourceMappingURL=documentAnalysisService.js.map