/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/renderer/index.tsx":
/*!********************************!*\
  !*** ./src/renderer/index.tsx ***!
  \********************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/ts-loader/index.js):\nError: TypeScript emitted no output for C:\\electronapp-v1\\electronappv2\\src\\renderer\\index.tsx.\n    at makeSourceMapAndFinish (C:\\electronapp-v1\\electronappv2\\node_modules\\ts-loader\\dist\\index.js:55:18)\n    at successLoader (C:\\electronapp-v1\\electronappv2\\node_modules\\ts-loader\\dist\\index.js:42:5)\n    at Object.loader (C:\\electronapp-v1\\electronappv2\\node_modules\\ts-loader\\dist\\index.js:23:5)");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module doesn't tell about it's top-level declarations so it can't be inlined
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./src/renderer/index.tsx"]();
/******/ 	
/******/ })()
;
//# sourceMappingURL=bundle.js.map