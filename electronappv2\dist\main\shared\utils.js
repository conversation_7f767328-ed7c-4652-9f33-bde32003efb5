"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDev = isDev;
exports.getAppVersion = getAppVersion;
exports.getAppName = getAppName;
exports.formatDate = formatDate;
exports.generateId = generateId;
exports.debounce = debounce;
exports.throttle = throttle;
const electron_1 = require("electron");
/**
 * Check if the application is running in development mode
 */
function isDev() {
    return process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
}
/**
 * Get the application version
 */
function getAppVersion() {
    return electron_1.app.getVersion();
}
/**
 * Get the application name
 */
function getAppName() {
    return electron_1.app.getName();
}
/**
 * Format date to readable string
 */
function formatDate(date) {
    return new Intl.DateTimeFormat('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    }).format(date);
}
/**
 * Generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(null, args), wait);
    };
}
/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func.apply(null, args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}
//# sourceMappingURL=utils.js.map