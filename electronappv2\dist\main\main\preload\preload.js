"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const constants_1 = require("../../shared/constants");
/**
 * Expose protected methods that allow the renderer process to use
 * the ipcRenderer without exposing the entire object
 */
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    // App info
    getAppVersion: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_APP_VERSION),
    getAppName: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_APP_NAME),
    // User management
    getUserProfile: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_USER_PROFILE),
    saveUserProfile: (profile) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.SAVE_USER_PROFILE, profile),
    deleteUserProfile: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.DELETE_USER_PROFILE),
    // Authentication
    loginUser: (credentials) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.LOGIN_USER, credentials),
    logoutUser: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.LOGOUT_USER),
    registerUser: (userData) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.REGISTER_USER, userData),
    // Chat functionality
    sendChatMessage: (message) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.SEND_CHAT_MESSAGE, message),
    getChatHistory: (userId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_CHAT_HISTORY, userId),
    clearChatHistory: (userId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.CLEAR_CHAT_HISTORY, userId),
    deleteChatMessage: (messageId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.DELETE_CHAT_MESSAGE, messageId),
    // Property search
    searchProperties: (query) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.SEARCH_PROPERTIES, query),
    getPropertyDetails: (propertyId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_PROPERTY_DETAILS, propertyId),
    getPropertySuggestions: (criteria) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_PROPERTY_SUGGESTIONS, criteria),
    getMarketTrends: (area) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_MARKET_TRENDS, area),
    // Saved properties
    saveProperty: (property) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.SAVE_PROPERTY, property),
    unsaveProperty: (propertyId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.UNSAVE_PROPERTY, propertyId),
    getSavedProperties: (userId) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_SAVED_PROPERTIES, userId),
    // Bangalore localities
    getLocalities: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_LOCALITIES),
    getLocalityInfo: (localityName) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_LOCALITY_INFO, localityName),
    getLocalityPrices: (localityName) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_LOCALITY_PRICES, localityName),
    // AI services
    queryGeminiAI: (prompt, context) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.QUERY_GEMINI_AI, prompt, context),
    getAISuggestions: (userInput) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_AI_SUGGESTIONS, userInput),
    analyzeProperty: (propertyData) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.ANALYZE_PROPERTY, propertyData),
    // Settings
    getSettings: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.GET_SETTINGS),
    saveSettings: (settings) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.SAVE_SETTINGS, settings),
    resetSettings: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.RESET_SETTINGS),
    // Database
    backupDatabase: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.BACKUP_DATABASE),
    restoreDatabase: (backupPath) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.RESTORE_DATABASE, backupPath),
    clearDatabase: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.CLEAR_DATABASE),
    // External APIs
    fetchExternalData: (apiName, params) => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.FETCH_EXTERNAL_DATA, apiName, params),
    updateMarketData: () => electron_1.ipcRenderer.invoke(constants_1.IpcChannels.UPDATE_MARKET_DATA),
    // API key management
    getApiKey: () => electron_1.ipcRenderer.invoke('get-api-key'),
    setApiKey: (apiKey) => electron_1.ipcRenderer.invoke('set-api-key', apiKey),
    removeApiKey: () => electron_1.ipcRenderer.invoke('remove-api-key'),
});
//# sourceMappingURL=preload.js.map