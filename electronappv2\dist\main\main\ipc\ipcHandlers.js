"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupIpcHandlers = setupIpcHandlers;
const electron_1 = require("electron");
const constants_1 = require("../../shared/constants");
const geminiService_1 = require("../../services/geminiService");
const settingsService_1 = require("../../services/settingsService");
const authService_1 = require("../../services/authService");
const knowledgeService_1 = require("../../services/knowledgeService");
const propertySearchService_1 = require("../../services/propertySearchService");
const marketAnalysisService_1 = require("../../services/marketAnalysisService");
const dataVisualizationService_1 = require("../../services/dataVisualizationService");
const externalApiService_1 = require("../../services/externalApiService");
const advancedAnalyticsService_1 = require("../../services/advancedAnalyticsService");
const services_1 = require("../../database/services");
const utils_1 = require("../../shared/utils");
/**
 * Set up all IPC handlers for communication between main and renderer processes
 */
function setupIpcHandlers() {
    // App info handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_APP_VERSION, () => {
        return require('../../../package.json').version;
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_APP_NAME, () => {
        return require('../../../package.json').productName;
    });
    // User management handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_USER_PROFILE, async () => {
        try {
            return authService_1.authService.getUserProfile();
        }
        catch (error) {
            console.error('Get profile error:', error);
            return null;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_USER_PROFILE, async (_, profile) => {
        try {
            await authService_1.authService.updateProfile(profile);
            return true;
        }
        catch (error) {
            console.error('Profile update error:', error);
            throw error;
        }
    });
    // User authentication handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.LOGIN_USER, async (_, credentials) => {
        try {
            const { email, name } = credentials;
            const user = await authService_1.authService.login(email, name);
            return user;
        }
        catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.LOGOUT_USER, async (_) => {
        try {
            authService_1.authService.logout();
            return true;
        }
        catch (error) {
            console.error('Logout error:', error);
            return false;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.REGISTER_USER, async (_, userData) => {
        try {
            const { name, email, preferences } = userData;
            const user = await authService_1.authService.register(name, email, preferences);
            return user;
        }
        catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    });
    // Chat handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SEND_CHAT_MESSAGE, async (_, message) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            if (!currentUser) {
                throw new Error('User not authenticated');
            }
            // Initialize Gemini service with API key if not already done
            if (!geminiService_1.geminiService.isConfigured()) {
                const apiKey = authService_1.authService.getApiKey() || await settingsService_1.settingsService.getApiKey();
                if (!apiKey) {
                    throw new Error('Google Gemini API key not configured. Please set it in Settings.');
                }
                geminiService_1.geminiService.setApiKey(apiKey);
            }
            // Send query to Gemini
            const geminiResponse = await geminiService_1.geminiService.query({
                prompt: message,
                context: {
                    userPreferences: currentUser.preferences
                },
            });
            // Save user message to database
            await services_1.chatService.saveMessage(currentUser.id, message, undefined, constants_1.MessageType.USER, 0);
            // Save AI response to database
            const messageId = await services_1.chatService.saveMessage(currentUser.id, geminiResponse.response, undefined, constants_1.MessageType.AI, geminiResponse.tokensUsed || 0);
            return {
                id: messageId.toString(),
                message: geminiResponse.response,
                timestamp: geminiResponse.timestamp,
                type: constants_1.MessageType.AI,
                tokensUsed: geminiResponse.tokensUsed,
                metadata: geminiResponse.metadata,
            };
        }
        catch (error) {
            console.error('Chat message error:', error);
            return {
                id: (0, utils_1.generateId)(),
                message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date().toISOString(),
                type: constants_1.MessageType.ERROR,
            };
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_CHAT_HISTORY, async (_, userId) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            const targetUserId = userId || currentUser?.id;
            if (!targetUserId) {
                throw new Error('No user ID provided and no authenticated user');
            }
            const messages = await services_1.chatService.getChatHistory(targetUserId, 50);
            return messages;
        }
        catch (error) {
            console.error('Get chat history error:', error);
            return [];
        }
    });
    // Knowledge base handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_LOCALITY_INFO, async (_, localityName) => {
        try {
            return await knowledgeService_1.knowledgeService.getLocalityInfo(localityName);
        }
        catch (error) {
            console.error('Get locality info error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SEARCH_LOCALITIES, async (_, query) => {
        try {
            return await knowledgeService_1.knowledgeService.searchLocalities(query);
        }
        catch (error) {
            console.error('Search localities error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_MARKET_ANALYSIS, async (_, criteria) => {
        try {
            const { locality, propertyType } = criteria;
            return await marketAnalysisService_1.marketAnalysisService.getMarketAnalysis(locality, propertyType);
        }
        catch (error) {
            console.error('Get market analysis error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.CALCULATE_ROI, async (_, roiData) => {
        try {
            const { purchasePrice, currentValue, rentalIncome, expenses, years } = roiData;
            return marketAnalysisService_1.marketAnalysisService.calculateROI(purchasePrice, currentValue, rentalIncome, expenses, years);
        }
        catch (error) {
            console.error('Calculate ROI error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PRICE_TRENDS, async (_, criteria) => {
        try {
            const { localities, propertyType } = criteria;
            return await marketAnalysisService_1.marketAnalysisService.getPriceTrends(localities, propertyType);
        }
        catch (error) {
            console.error('Get price trends error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_INVESTMENT_OPPORTUNITIES, async (_, criteria) => {
        try {
            return await marketAnalysisService_1.marketAnalysisService.getInvestmentOpportunities(criteria);
        }
        catch (error) {
            console.error('Get investment opportunities error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GENERATE_MARKET_REPORT, async (_, criteria) => {
        try {
            const { area, propertyTypes } = criteria;
            return await marketAnalysisService_1.marketAnalysisService.generateMarketReport(area, propertyTypes);
        }
        catch (error) {
            console.error('Generate market report error:', error);
            throw error;
        }
    });
    // Data visualization handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PRICE_TREND_CHART_DATA, async (_, criteria) => {
        try {
            const { localities, propertyType } = criteria;
            return await dataVisualizationService_1.dataVisualizationService.getPriceTrendChartData(localities, propertyType);
        }
        catch (error) {
            console.error('Get price trend chart data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_MARKET_ANALYSIS_CHART_DATA, async (_, criteria) => {
        try {
            const { locality, propertyType } = criteria;
            return await dataVisualizationService_1.dataVisualizationService.getMarketAnalysisChartData(locality, propertyType);
        }
        catch (error) {
            console.error('Get market analysis chart data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PROPERTY_DISTRIBUTION_DATA, async (_, localities) => {
        try {
            return await dataVisualizationService_1.dataVisualizationService.getPropertyDistributionData(localities);
        }
        catch (error) {
            console.error('Get property distribution data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_MAP_DATA, async (_, localities) => {
        try {
            return await dataVisualizationService_1.dataVisualizationService.getMapData(localities);
        }
        catch (error) {
            console.error('Get map data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_INVESTMENT_OPPORTUNITY_DATA, async (_, criteria) => {
        try {
            return await dataVisualizationService_1.dataVisualizationService.getInvestmentOpportunityData(criteria);
        }
        catch (error) {
            console.error('Get investment opportunity data error:', error);
            throw error;
        }
    });
    // External API handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.FETCH_EXTERNAL_PROPERTIES, async (_, criteria) => {
        try {
            return await externalApiService_1.externalApiService.fetchExternalProperties(criteria);
        }
        catch (error) {
            console.error('Fetch external properties error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.FETCH_MARKET_DATA, async (_, locality) => {
        try {
            return await externalApiService_1.externalApiService.fetchMarketData(locality);
        }
        catch (error) {
            console.error('Fetch market data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.FETCH_LOCALITY_INFO, async (_, locality) => {
        try {
            return await externalApiService_1.externalApiService.fetchLocalityInfo(locality);
        }
        catch (error) {
            console.error('Fetch locality info error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.FETCH_REAL_ESTATE_NEWS, async (_, locality) => {
        try {
            return await externalApiService_1.externalApiService.fetchRealEstateNews(locality);
        }
        catch (error) {
            console.error('Fetch real estate news error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.VALIDATE_PROPERTY_DATA, async (_, property) => {
        try {
            return await externalApiService_1.externalApiService.validatePropertyData(property);
        }
        catch (error) {
            console.error('Validate property data error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SET_EXTERNAL_API_KEY, async (_, data) => {
        try {
            const { service, apiKey } = data;
            externalApiService_1.externalApiService.setApiKey(service, apiKey);
            return true;
        }
        catch (error) {
            console.error('Set external API key error:', error);
            throw error;
        }
    });
    // Advanced analytics handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GENERATE_PRICE_PREDICTION, async (_, criteria) => {
        try {
            const { locality, propertyType, timeHorizon } = criteria;
            return await advancedAnalyticsService_1.advancedAnalyticsService.generatePricePrediction(locality, propertyType, timeHorizon);
        }
        catch (error) {
            console.error('Generate price prediction error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.CALCULATE_INVESTMENT_SCORE, async (_, criteria) => {
        try {
            return await advancedAnalyticsService_1.advancedAnalyticsService.calculateInvestmentScore(criteria);
        }
        catch (error) {
            console.error('Calculate investment score error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.PERFORM_TREND_ANALYSIS, async (_, criteria) => {
        try {
            const { localities, propertyType } = criteria;
            return await advancedAnalyticsService_1.advancedAnalyticsService.performTrendAnalysis(localities, propertyType);
        }
        catch (error) {
            console.error('Perform trend analysis error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GENERATE_MARKET_SENTIMENT, async (_, locality) => {
        try {
            return await advancedAnalyticsService_1.advancedAnalyticsService.generateMarketSentiment(locality);
        }
        catch (error) {
            console.error('Generate market sentiment error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.OPTIMIZE_PORTFOLIO, async (_, criteria) => {
        try {
            return await advancedAnalyticsService_1.advancedAnalyticsService.optimizePortfolio(criteria);
        }
        catch (error) {
            console.error('Optimize portfolio error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_INVESTMENT_RECOMMENDATIONS, async (_, criteria) => {
        try {
            const { budget, propertyType, purpose } = criteria;
            return knowledgeService_1.knowledgeService.getInvestmentRecommendations(budget, propertyType, purpose);
        }
        catch (error) {
            console.error('Get investment recommendations error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_COMPARATIVE_ANALYSIS, async (_, localities) => {
        try {
            const { locality1, locality2 } = localities;
            return knowledgeService_1.knowledgeService.getComparativeAnalysis(locality1, locality2);
        }
        catch (error) {
            console.error('Get comparative analysis error:', error);
            throw error;
        }
    });
    // Property search handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SEARCH_PROPERTIES, async (_, searchQuery) => {
        try {
            return await propertySearchService_1.propertySearchService.searchProperties(searchQuery);
        }
        catch (error) {
            console.error('Search properties error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PROPERTY_DETAILS, async (_, propertyId) => {
        try {
            return await propertySearchService_1.propertySearchService.getPropertyDetails(propertyId);
        }
        catch (error) {
            console.error('Get property details error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PROPERTY_SUGGESTIONS, async (_) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            if (!currentUser) {
                throw new Error('User not authenticated');
            }
            return await propertySearchService_1.propertySearchService.getPropertySuggestions(currentUser.preferences);
        }
        catch (error) {
            console.error('Get property suggestions error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_MARKET_TRENDS, async (_, criteria) => {
        try {
            const { locality, propertyType } = criteria || {};
            return await propertySearchService_1.propertySearchService.getMarketTrends(locality, propertyType);
        }
        catch (error) {
            console.error('Get market trends error:', error);
            throw error;
        }
    });
    // Saved properties handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_PROPERTY, async (_, propertyData) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            if (!currentUser) {
                throw new Error('User not authenticated');
            }
            const { property, notes } = propertyData;
            await services_1.propertyService.saveProperty(currentUser.id, property.id, property, notes);
            return true;
        }
        catch (error) {
            console.error('Save property error:', error);
            throw error;
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_SAVED_PROPERTIES, async (_, userId) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            const targetUserId = userId || currentUser?.id;
            if (!targetUserId) {
                throw new Error('No user ID provided and no authenticated user');
            }
            return await services_1.propertyService.getSavedProperties(targetUserId);
        }
        catch (error) {
            console.error('Get saved properties error:', error);
            return [];
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.UNSAVE_PROPERTY, async (_, propertyId) => {
        try {
            const currentUser = authService_1.authService.getCurrentUser();
            if (!currentUser) {
                throw new Error('User not authenticated');
            }
            await services_1.propertyService.removeProperty(currentUser.id, propertyId);
            return true;
        }
        catch (error) {
            console.error('Unsave property error:', error);
            throw error;
        }
    });
    // Settings handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_SETTINGS, async () => {
        try {
            return await settingsService_1.settingsService.getSettings();
        }
        catch (error) {
            console.error('Get settings error:', error);
            return settingsService_1.settingsService.getDefaultSettings();
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_SETTINGS, async (event, settings) => {
        try {
            await settingsService_1.settingsService.saveSettings(settings);
            return true;
        }
        catch (error) {
            console.error('Save settings error:', error);
            return false;
        }
    });
    // API key management
    electron_1.ipcMain.handle('get-api-key', async () => {
        try {
            return await settingsService_1.settingsService.getApiKey();
        }
        catch (error) {
            console.error('Get API key error:', error);
            return null;
        }
    });
    electron_1.ipcMain.handle('set-api-key', async (event, apiKey) => {
        try {
            await settingsService_1.settingsService.setApiKey(apiKey);
            geminiService_1.geminiService.setApiKey(apiKey);
            return true;
        }
        catch (error) {
            console.error('Set API key error:', error);
            return false;
        }
    });
    electron_1.ipcMain.handle('remove-api-key', async () => {
        try {
            await settingsService_1.settingsService.removeApiKey();
            geminiService_1.geminiService.setApiKey('');
            return true;
        }
        catch (error) {
            console.error('Remove API key error:', error);
            return false;
        }
    });
    console.log('IPC handlers initialized');
}
//# sourceMappingURL=ipcHandlers.js.map