"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupIpcHandlers = setupIpcHandlers;
const electron_1 = require("electron");
const constants_1 = require("../../shared/constants");
const geminiService_1 = require("../../services/geminiService");
const settingsService_1 = require("../../services/settingsService");
const database_1 = require("../../database/database");
const utils_1 = require("../../shared/utils");
/**
 * Set up all IPC handlers for communication between main and renderer processes
 */
function setupIpcHandlers() {
    // App info handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_APP_VERSION, () => {
        return require('../../../package.json').version;
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_APP_NAME, () => {
        return require('../../../package.json').productName;
    });
    // User management handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_USER_PROFILE, async () => {
        // TODO: Implement user profile retrieval
        return null;
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_USER_PROFILE, async (event, profile) => {
        // TODO: Implement user profile saving
        return true;
    });
    // Chat handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SEND_CHAT_MESSAGE, async (event, message, context) => {
        try {
            // Initialize Gemini service with API key if not already done
            if (!geminiService_1.geminiService.isConfigured()) {
                const apiKey = await settingsService_1.settingsService.getApiKey();
                if (!apiKey) {
                    throw new Error('Google Gemini API key not configured. Please set it in Settings.');
                }
                geminiService_1.geminiService.setApiKey(apiKey);
            }
            // Send query to Gemini
            const geminiResponse = await geminiService_1.geminiService.query({
                prompt: message,
                context: context || {},
            });
            // Save conversation to database
            const db = (0, database_1.getDatabase)();
            const messageId = (0, utils_1.generateId)();
            await db.run('INSERT INTO conversations (id, message, response, message_type, timestamp, tokens_used) VALUES (?, ?, ?, ?, ?, ?)', [messageId, message, geminiResponse.response, constants_1.MessageType.USER, new Date().toISOString(), geminiResponse.tokensUsed]);
            return {
                id: messageId,
                message: geminiResponse.response,
                timestamp: geminiResponse.timestamp,
                type: constants_1.MessageType.AI,
                tokensUsed: geminiResponse.tokensUsed,
                metadata: geminiResponse.metadata,
            };
        }
        catch (error) {
            console.error('Chat message error:', error);
            return {
                id: (0, utils_1.generateId)(),
                message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date().toISOString(),
                type: constants_1.MessageType.ERROR,
            };
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_CHAT_HISTORY, async (event, userId) => {
        try {
            const db = (0, database_1.getDatabase)();
            const rows = await db.all('SELECT * FROM conversations WHERE user_id = ? OR user_id IS NULL ORDER BY timestamp DESC LIMIT 100', [userId || null]);
            return rows.map(row => ({
                id: row.id,
                message: row.message,
                response: row.response,
                type: row.message_type,
                timestamp: row.timestamp,
                tokensUsed: row.tokens_used,
            }));
        }
        catch (error) {
            console.error('Get chat history error:', error);
            return [];
        }
    });
    // Property search handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SEARCH_PROPERTIES, async (event, searchQuery) => {
        // TODO: Implement property search
        return [];
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_PROPERTY_DETAILS, async (event, propertyId) => {
        // TODO: Implement property details retrieval
        return null;
    });
    // Saved properties handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_PROPERTY, async (event, property) => {
        // TODO: Implement property saving
        return true;
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_SAVED_PROPERTIES, async (event, userId) => {
        // TODO: Implement saved properties retrieval
        return [];
    });
    // Settings handlers
    electron_1.ipcMain.handle(constants_1.IpcChannels.GET_SETTINGS, async () => {
        try {
            return await settingsService_1.settingsService.getSettings();
        }
        catch (error) {
            console.error('Get settings error:', error);
            return settingsService_1.settingsService.getDefaultSettings();
        }
    });
    electron_1.ipcMain.handle(constants_1.IpcChannels.SAVE_SETTINGS, async (event, settings) => {
        try {
            await settingsService_1.settingsService.saveSettings(settings);
            return true;
        }
        catch (error) {
            console.error('Save settings error:', error);
            return false;
        }
    });
    // API key management
    electron_1.ipcMain.handle('get-api-key', async () => {
        try {
            return await settingsService_1.settingsService.getApiKey();
        }
        catch (error) {
            console.error('Get API key error:', error);
            return null;
        }
    });
    electron_1.ipcMain.handle('set-api-key', async (event, apiKey) => {
        try {
            await settingsService_1.settingsService.setApiKey(apiKey);
            geminiService_1.geminiService.setApiKey(apiKey);
            return true;
        }
        catch (error) {
            console.error('Set API key error:', error);
            return false;
        }
    });
    electron_1.ipcMain.handle('remove-api-key', async () => {
        try {
            await settingsService_1.settingsService.removeApiKey();
            geminiService_1.geminiService.setApiKey('');
            return true;
        }
        catch (error) {
            console.error('Remove API key error:', error);
            return false;
        }
    });
    console.log('IPC handlers initialized');
}
//# sourceMappingURL=ipcHandlers.js.map