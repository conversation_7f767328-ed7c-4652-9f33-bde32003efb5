import { BrowserWindow, screen, session } from 'electron';
import * as path from 'path';
import { isDev } from '../../shared/utils';

export function createMainWindow(): BrowserWindow {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Calculate window size (80% of screen size, minimum 1024x768)
  const windowWidth = Math.max(Math.floor(width * 0.8), 1024);
  const windowHeight = Math.max(Math.floor(height * 0.8), 768);

  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 1024,
    minHeight: 768,
    center: true,
    show: false, // Don't show until ready-to-show
    icon: path.join(__dirname, '../../../assets/icons/app-icon.png'),
    titleBarStyle: 'default',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      preload: path.join(__dirname, '../preload/preload.js'),
      sandbox: false, // Keep false to allow preload script access
      spellcheck: false,
    },
  });

  // Configure session security
  const ses = mainWindow.webContents.session;

  // Set security headers
  ses.webRequest.onHeadersReceived((details, callback) => {
    const cspPolicy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://generativelanguage.googleapis.com; object-src 'none'; base-uri 'self'; form-action 'self';";

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [cspPolicy],
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Referrer-Policy': ['strict-origin-when-cross-origin']
      }
    });
  });

  // Block insecure requests
  ses.webRequest.onBeforeRequest((details, callback) => {
    const url = new URL(details.url);

    // Allow HTTPS and file protocols
    if (url.protocol === 'https:' || url.protocol === 'file:' || url.protocol === 'data:') {
      callback({});
      return;
    }

    // Allow Google Gemini API
    if (url.hostname === 'generativelanguage.googleapis.com') {
      callback({});
      return;
    }

    // Block everything else
    callback({ cancel: true });
  });

  // Load the app from built files
  mainWindow.loadFile(path.join(__dirname, '../../../renderer/index.html'));

  // Open DevTools in development
  if (isDev()) {
    mainWindow.webContents.openDevTools();
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    console.log('Main window ready-to-show event fired');
    mainWindow.show();

    // Focus the window
    if (isDev()) {
      mainWindow.focus();
    }
  });

  // Force show window after 3 seconds if ready-to-show doesn't fire
  setTimeout(() => {
    if (!mainWindow.isVisible()) {
      console.log('Force showing window - ready-to-show did not fire');
      mainWindow.show();
    }
  }, 3000);

  // Add error handling for renderer process
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', errorCode, errorDescription, validatedURL);
  });

  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('Renderer process crashed:', killed);
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    // Dereference the window object
  });

  // Prevent navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    // Only allow file:// protocol navigation
    if (!navigationUrl.startsWith('file://')) {
      event.preventDefault();
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Open external links in default browser
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}
