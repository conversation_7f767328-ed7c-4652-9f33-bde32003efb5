import { BrowserWindow, screen, session } from 'electron';
import * as path from 'path';
import { isDev } from '../../shared/utils';

export function createMainWindow(): BrowserWindow {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Calculate window size (80% of screen size, minimum 1024x768)
  const windowWidth = Math.max(Math.floor(width * 0.8), 1024);
  const windowHeight = Math.max(Math.floor(height * 0.8), 768);

  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 1024,
    minHeight: 768,
    center: true,
    show: false, // Don't show until ready-to-show
    icon: path.join(__dirname, '../../../assets/icons/app-icon.png'),
    titleBarStyle: 'default',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      preload: path.join(__dirname, '../preload/preload.js'),
      sandbox: false, // Keep false to allow preload script access
      spellcheck: false,
    },
  });

  // Configure session security
  const ses = mainWindow.webContents.session;

  // Set security headers
  ses.webRequest.onHeadersReceived((details, callback) => {
    const cspPolicy = isDev()
      ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:* ws://localhost:* data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' http://localhost:*; img-src 'self' data: https: http://localhost:*; font-src 'self' data: http://localhost:*; connect-src 'self' https://generativelanguage.googleapis.com http://localhost:* ws://localhost:*; object-src 'none';"
      : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://generativelanguage.googleapis.com; object-src 'none'; base-uri 'self'; form-action 'self';";

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [cspPolicy],
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Referrer-Policy': ['strict-origin-when-cross-origin']
      }
    });
  });

  // Block insecure requests (only in production)
  if (!isDev()) {
    ses.webRequest.onBeforeRequest((details, callback) => {
      const url = new URL(details.url);

      // Allow HTTPS and file protocols
      if (url.protocol === 'https:' || url.protocol === 'file:' || url.protocol === 'data:') {
        callback({});
        return;
      }

      // Allow Google Gemini API
      if (url.hostname === 'generativelanguage.googleapis.com') {
        callback({});
        return;
      }

      // Block everything else in production
      callback({ cancel: true });
    });
  }

  // Load the app
  if (isDev()) {
    // Development: Load from webpack dev server
    mainWindow.loadURL('http://localhost:3000');
    
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production: Load from built files
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus the window
    if (isDev()) {
      mainWindow.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    // Dereference the window object
  });

  // Prevent navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
      event.preventDefault();
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Open external links in default browser
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}
