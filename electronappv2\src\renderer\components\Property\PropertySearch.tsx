import React from 'react';

const PropertySearch: React.FC = () => {
  return (
    <div className="flex flex-col h-full">
      <div className="p-6 border-b border-gray-200 bg-white">
        <h1 className="text-2xl font-bold text-gray-900">Property Search</h1>
        <p className="text-gray-600 mt-1">Find your perfect property in Bangalore</p>
      </div>

      <div className="flex-1 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-primary-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Property Search</h3>
          <p className="text-gray-500">Advanced property search functionality coming soon...</p>
        </div>
      </div>
    </div>
  );
};

export default PropertySearch;
