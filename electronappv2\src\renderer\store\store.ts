import { configureStore } from '@reduxjs/toolkit';
import appSlice from './slices/appSlice';
import userSlice from './slices/userSlice';
import chatSlice from './slices/chatSlice';
import propertySlice from './slices/propertySlice';

export const store = configureStore({
  reducer: {
    app: appSlice,
    user: userSlice,
    chat: chatSlice,
    property: propertySlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
