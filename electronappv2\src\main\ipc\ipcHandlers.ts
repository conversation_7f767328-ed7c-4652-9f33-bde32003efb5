import { ipc<PERSON>ain } from 'electron';
import { IpcChannels, MessageType } from '../../shared/constants';
import { geminiService } from '../../services/geminiService';
import { settingsService } from '../../services/settingsService';
import { authService } from '../../services/authService';
import { knowledgeService } from '../../services/knowledgeService';
import { propertySearchService } from '../../services/propertySearchService';
import { marketAnalysisService } from '../../services/marketAnalysisService';
import { dataVisualizationService } from '../../services/dataVisualizationService';
import { externalApiService } from '../../services/externalApiService';
import { advancedAnalyticsService } from '../../services/advancedAnalyticsService';
import { chatService, localityService, propertyService } from '../../database/services';
import { getDatabase } from '../../database/database';
import { generateId } from '../../shared/utils';

/**
 * Set up all IPC handlers for communication between main and renderer processes
 */
export function setupIpcHandlers(): void {
  // App info handlers
  ipcMain.handle(IpcChannels.GET_APP_VERSION, () => {
    return require('../../../../package.json').version;
  });

  ipcMain.handle(IpcChannels.GET_APP_NAME, () => {
    return require('../../../../package.json').productName;
  });

  // User management handlers
  ipcMain.handle(IpcChannels.GET_USER_PROFILE, async () => {
    try {
      return authService.getUserProfile();
    } catch (error) {
      console.error('Get profile error:', error);
      return null;
    }
  });

  ipcMain.handle(IpcChannels.SAVE_USER_PROFILE, async (_, profile) => {
    try {
      await authService.updateProfile(profile);
      return true;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  });

  // User authentication handlers
  ipcMain.handle(IpcChannels.LOGIN_USER, async (_, credentials) => {
    try {
      const { email, name } = credentials;
      const user = await authService.login(email, name);
      return user;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.LOGOUT_USER, async (_) => {
    try {
      authService.logout();
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  });

  ipcMain.handle(IpcChannels.REGISTER_USER, async (_, userData) => {
    try {
      const { name, email, preferences } = userData;
      const user = await authService.register(name, email, preferences);
      return user;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  });

  // Chat handlers
  ipcMain.handle(IpcChannels.SEND_CHAT_MESSAGE, async (_, message) => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Initialize Gemini service with API key if not already done
      if (!geminiService.isConfigured()) {
        const apiKey = authService.getApiKey() || await settingsService.getApiKey();
        if (!apiKey) {
          throw new Error('Google Gemini API key not configured. Please set it in Settings.');
        }
        geminiService.setApiKey(apiKey);
      }

      // Send query to Gemini
      const geminiResponse = await geminiService.query({
        prompt: message,
        context: {
          userPreferences: currentUser.preferences
        },
      });

      // Save user message to database
      await chatService.saveMessage(
        currentUser.id,
        message,
        undefined,
        MessageType.USER,
        0
      );

      // Save AI response to database
      const messageId = await chatService.saveMessage(
        currentUser.id,
        geminiResponse.response,
        undefined,
        MessageType.AI,
        geminiResponse.tokensUsed || 0
      );

      return {
        id: messageId.toString(),
        message: geminiResponse.response,
        timestamp: geminiResponse.timestamp,
        type: MessageType.AI,
        tokensUsed: geminiResponse.tokensUsed,
        metadata: geminiResponse.metadata,
      };
    } catch (error) {
      console.error('Chat message error:', error);
      return {
        id: generateId(),
        message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString(),
        type: MessageType.ERROR,
      };
    }
  });

  ipcMain.handle(IpcChannels.GET_CHAT_HISTORY, async (_, userId) => {
    try {
      const currentUser = authService.getCurrentUser();
      const targetUserId = userId || currentUser?.id;

      if (!targetUserId) {
        throw new Error('No user ID provided and no authenticated user');
      }

      const messages = await chatService.getChatHistory(targetUserId, 50);
      return messages;
    } catch (error) {
      console.error('Get chat history error:', error);
      return [];
    }
  });

  // Knowledge base handlers
  ipcMain.handle(IpcChannels.GET_LOCALITY_INFO, async (_, localityName) => {
    try {
      return await knowledgeService.getLocalityInfo(localityName);
    } catch (error) {
      console.error('Get locality info error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.SEARCH_LOCALITIES, async (_, query) => {
    try {
      return await knowledgeService.searchLocalities(query);
    } catch (error) {
      console.error('Search localities error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_MARKET_ANALYSIS, async (_, criteria) => {
    try {
      const { locality, propertyType } = criteria;
      return await marketAnalysisService.getMarketAnalysis(locality, propertyType);
    } catch (error) {
      console.error('Get market analysis error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.CALCULATE_ROI, async (_, roiData) => {
    try {
      const { purchasePrice, currentValue, rentalIncome, expenses, years } = roiData;
      return marketAnalysisService.calculateROI(purchasePrice, currentValue, rentalIncome, expenses, years);
    } catch (error) {
      console.error('Calculate ROI error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_PRICE_TRENDS, async (_, criteria) => {
    try {
      const { localities, propertyType } = criteria;
      return await marketAnalysisService.getPriceTrends(localities, propertyType);
    } catch (error) {
      console.error('Get price trends error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_INVESTMENT_OPPORTUNITIES, async (_, criteria) => {
    try {
      return await marketAnalysisService.getInvestmentOpportunities(criteria);
    } catch (error) {
      console.error('Get investment opportunities error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GENERATE_MARKET_REPORT, async (_, criteria) => {
    try {
      const { area, propertyTypes } = criteria;
      return await marketAnalysisService.generateMarketReport(area, propertyTypes);
    } catch (error) {
      console.error('Generate market report error:', error);
      throw error;
    }
  });

  // Data visualization handlers
  ipcMain.handle(IpcChannels.GET_PRICE_TREND_CHART_DATA, async (_, criteria) => {
    try {
      const { localities, propertyType } = criteria;
      return await dataVisualizationService.getPriceTrendChartData(localities, propertyType);
    } catch (error) {
      console.error('Get price trend chart data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_MARKET_ANALYSIS_CHART_DATA, async (_, criteria) => {
    try {
      const { locality, propertyType } = criteria;
      return await dataVisualizationService.getMarketAnalysisChartData(locality, propertyType);
    } catch (error) {
      console.error('Get market analysis chart data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_PROPERTY_DISTRIBUTION_DATA, async (_, localities) => {
    try {
      return await dataVisualizationService.getPropertyDistributionData(localities);
    } catch (error) {
      console.error('Get property distribution data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_MAP_DATA, async (_, localities) => {
    try {
      return await dataVisualizationService.getMapData(localities);
    } catch (error) {
      console.error('Get map data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_INVESTMENT_OPPORTUNITY_DATA, async (_, criteria) => {
    try {
      return await dataVisualizationService.getInvestmentOpportunityData(criteria);
    } catch (error) {
      console.error('Get investment opportunity data error:', error);
      throw error;
    }
  });

  // External API handlers
  ipcMain.handle(IpcChannels.FETCH_EXTERNAL_PROPERTIES, async (_, criteria) => {
    try {
      return await externalApiService.fetchExternalProperties(criteria);
    } catch (error) {
      console.error('Fetch external properties error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.FETCH_MARKET_DATA, async (_, locality) => {
    try {
      return await externalApiService.fetchMarketData(locality);
    } catch (error) {
      console.error('Fetch market data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.FETCH_LOCALITY_INFO, async (_, locality) => {
    try {
      return await externalApiService.fetchLocalityInfo(locality);
    } catch (error) {
      console.error('Fetch locality info error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.FETCH_REAL_ESTATE_NEWS, async (_, locality) => {
    try {
      return await externalApiService.fetchRealEstateNews(locality);
    } catch (error) {
      console.error('Fetch real estate news error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.VALIDATE_PROPERTY_DATA, async (_, property) => {
    try {
      return await externalApiService.validatePropertyData(property);
    } catch (error) {
      console.error('Validate property data error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.SET_EXTERNAL_API_KEY, async (_, data) => {
    try {
      const { service, apiKey } = data;
      externalApiService.setApiKey(service, apiKey);
      return true;
    } catch (error) {
      console.error('Set external API key error:', error);
      throw error;
    }
  });

  // Advanced analytics handlers
  ipcMain.handle(IpcChannels.GENERATE_PRICE_PREDICTION, async (_, criteria) => {
    try {
      const { locality, propertyType, timeHorizon } = criteria;
      return await advancedAnalyticsService.generatePricePrediction(locality, propertyType, timeHorizon);
    } catch (error) {
      console.error('Generate price prediction error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.CALCULATE_INVESTMENT_SCORE, async (_, criteria) => {
    try {
      return await advancedAnalyticsService.calculateInvestmentScore(criteria);
    } catch (error) {
      console.error('Calculate investment score error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.PERFORM_TREND_ANALYSIS, async (_, criteria) => {
    try {
      const { localities, propertyType } = criteria;
      return await advancedAnalyticsService.performTrendAnalysis(localities, propertyType);
    } catch (error) {
      console.error('Perform trend analysis error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GENERATE_MARKET_SENTIMENT, async (_, locality) => {
    try {
      return await advancedAnalyticsService.generateMarketSentiment(locality);
    } catch (error) {
      console.error('Generate market sentiment error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.OPTIMIZE_PORTFOLIO, async (_, criteria) => {
    try {
      return await advancedAnalyticsService.optimizePortfolio(criteria);
    } catch (error) {
      console.error('Optimize portfolio error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_INVESTMENT_RECOMMENDATIONS, async (_, criteria) => {
    try {
      const { budget, propertyType, purpose } = criteria;
      return knowledgeService.getInvestmentRecommendations(budget, propertyType, purpose);
    } catch (error) {
      console.error('Get investment recommendations error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_COMPARATIVE_ANALYSIS, async (_, localities) => {
    try {
      const { locality1, locality2 } = localities;
      return knowledgeService.getComparativeAnalysis(locality1, locality2);
    } catch (error) {
      console.error('Get comparative analysis error:', error);
      throw error;
    }
  });

  // Property search handlers
  ipcMain.handle(IpcChannels.SEARCH_PROPERTIES, async (_, searchQuery) => {
    try {
      return await propertySearchService.searchProperties(searchQuery);
    } catch (error) {
      console.error('Search properties error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_PROPERTY_DETAILS, async (_, propertyId) => {
    try {
      return await propertySearchService.getPropertyDetails(propertyId);
    } catch (error) {
      console.error('Get property details error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_PROPERTY_SUGGESTIONS, async (_) => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }
      return await propertySearchService.getPropertySuggestions(currentUser.preferences);
    } catch (error) {
      console.error('Get property suggestions error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_MARKET_TRENDS, async (_, criteria) => {
    try {
      const { locality, propertyType } = criteria || {};
      return await propertySearchService.getMarketTrends(locality, propertyType);
    } catch (error) {
      console.error('Get market trends error:', error);
      throw error;
    }
  });

  // Saved properties handlers
  ipcMain.handle(IpcChannels.SAVE_PROPERTY, async (_, propertyData) => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const { property, notes } = propertyData;
      await propertyService.saveProperty(currentUser.id, property.id, property, notes);
      return true;
    } catch (error) {
      console.error('Save property error:', error);
      throw error;
    }
  });

  ipcMain.handle(IpcChannels.GET_SAVED_PROPERTIES, async (_, userId) => {
    try {
      const currentUser = authService.getCurrentUser();
      const targetUserId = userId || currentUser?.id;

      if (!targetUserId) {
        throw new Error('No user ID provided and no authenticated user');
      }

      return await propertyService.getSavedProperties(targetUserId);
    } catch (error) {
      console.error('Get saved properties error:', error);
      return [];
    }
  });

  ipcMain.handle(IpcChannels.UNSAVE_PROPERTY, async (_, propertyId) => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      await propertyService.removeProperty(currentUser.id, propertyId);
      return true;
    } catch (error) {
      console.error('Unsave property error:', error);
      throw error;
    }
  });

  // Settings handlers
  ipcMain.handle(IpcChannels.GET_SETTINGS, async () => {
    try {
      return await settingsService.getSettings();
    } catch (error) {
      console.error('Get settings error:', error);
      return settingsService.getDefaultSettings();
    }
  });

  ipcMain.handle(IpcChannels.SAVE_SETTINGS, async (event, settings) => {
    try {
      await settingsService.saveSettings(settings);
      return true;
    } catch (error) {
      console.error('Save settings error:', error);
      return false;
    }
  });

  // API key management
  ipcMain.handle('get-api-key', async () => {
    try {
      return await settingsService.getApiKey();
    } catch (error) {
      console.error('Get API key error:', error);
      return null;
    }
  });

  ipcMain.handle('set-api-key', async (event, apiKey) => {
    try {
      await settingsService.setApiKey(apiKey);
      geminiService.setApiKey(apiKey);
      return true;
    } catch (error) {
      console.error('Set API key error:', error);
      return false;
    }
  });

  ipcMain.handle('remove-api-key', async () => {
    try {
      await settingsService.removeApiKey();
      geminiService.setApiKey('');
      return true;
    } catch (error) {
      console.error('Remove API key error:', error);
      return false;
    }
  });

  console.log('IPC handlers initialized');
}
