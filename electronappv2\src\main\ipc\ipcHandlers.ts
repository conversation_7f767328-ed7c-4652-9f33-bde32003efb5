import { ipc<PERSON>ain } from 'electron';
import { IpcChannels, MessageType } from '../../shared/constants';
import { geminiService } from '../../services/geminiService';
import { settingsService } from '../../services/settingsService';
import { getDatabase } from '../../database/database';
import { generateId } from '../../shared/utils';

/**
 * Set up all IPC handlers for communication between main and renderer processes
 */
export function setupIpcHandlers(): void {
  // App info handlers
  ipcMain.handle(IpcChannels.GET_APP_VERSION, () => {
    return require('../../../package.json').version;
  });

  ipcMain.handle(IpcChannels.GET_APP_NAME, () => {
    return require('../../../package.json').productName;
  });

  // User management handlers
  ipcMain.handle(IpcChannels.GET_USER_PROFILE, async () => {
    // TODO: Implement user profile retrieval
    return null;
  });

  ipcMain.handle(IpcChannels.SAVE_USER_PROFILE, async (event, profile) => {
    // TODO: Implement user profile saving
    return true;
  });

  // Chat handlers
  ipcMain.handle(IpcChannels.SEND_CHAT_MESSAGE, async (event, message, context) => {
    try {
      // Initialize Gemini service with API key if not already done
      if (!geminiService.isConfigured()) {
        const apiKey = await settingsService.getApiKey();
        if (!apiKey) {
          throw new Error('Google Gemini API key not configured. Please set it in Settings.');
        }
        geminiService.setApiKey(apiKey);
      }

      // Send query to Gemini
      const geminiResponse = await geminiService.query({
        prompt: message,
        context: context || {},
      });

      // Save conversation to database
      const db = getDatabase();
      const messageId = generateId();

      await db.run(
        'INSERT INTO conversations (id, message, response, message_type, timestamp, tokens_used) VALUES (?, ?, ?, ?, ?, ?)',
        [messageId, message, geminiResponse.response, MessageType.USER, new Date().toISOString(), geminiResponse.tokensUsed]
      );

      return {
        id: messageId,
        message: geminiResponse.response,
        timestamp: geminiResponse.timestamp,
        type: MessageType.AI,
        tokensUsed: geminiResponse.tokensUsed,
        metadata: geminiResponse.metadata,
      };
    } catch (error) {
      console.error('Chat message error:', error);
      return {
        id: generateId(),
        message: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString(),
        type: MessageType.ERROR,
      };
    }
  });

  ipcMain.handle(IpcChannels.GET_CHAT_HISTORY, async (event, userId) => {
    try {
      const db = getDatabase();
      const rows = await db.all(
        'SELECT * FROM conversations WHERE user_id = ? OR user_id IS NULL ORDER BY timestamp DESC LIMIT 100',
        [userId || null]
      );

      return rows.map(row => ({
        id: row.id,
        message: row.message,
        response: row.response,
        type: row.message_type,
        timestamp: row.timestamp,
        tokensUsed: row.tokens_used,
      }));
    } catch (error) {
      console.error('Get chat history error:', error);
      return [];
    }
  });

  // Property search handlers
  ipcMain.handle(IpcChannels.SEARCH_PROPERTIES, async (event, searchQuery) => {
    // TODO: Implement property search
    return [];
  });

  ipcMain.handle(IpcChannels.GET_PROPERTY_DETAILS, async (event, propertyId) => {
    // TODO: Implement property details retrieval
    return null;
  });

  // Saved properties handlers
  ipcMain.handle(IpcChannels.SAVE_PROPERTY, async (event, property) => {
    // TODO: Implement property saving
    return true;
  });

  ipcMain.handle(IpcChannels.GET_SAVED_PROPERTIES, async (event, userId) => {
    // TODO: Implement saved properties retrieval
    return [];
  });

  // Settings handlers
  ipcMain.handle(IpcChannels.GET_SETTINGS, async () => {
    try {
      return await settingsService.getSettings();
    } catch (error) {
      console.error('Get settings error:', error);
      return settingsService.getDefaultSettings();
    }
  });

  ipcMain.handle(IpcChannels.SAVE_SETTINGS, async (event, settings) => {
    try {
      await settingsService.saveSettings(settings);
      return true;
    } catch (error) {
      console.error('Save settings error:', error);
      return false;
    }
  });

  // API key management
  ipcMain.handle('get-api-key', async () => {
    try {
      return await settingsService.getApiKey();
    } catch (error) {
      console.error('Get API key error:', error);
      return null;
    }
  });

  ipcMain.handle('set-api-key', async (event, apiKey) => {
    try {
      await settingsService.setApiKey(apiKey);
      geminiService.setApiKey(apiKey);
      return true;
    } catch (error) {
      console.error('Set API key error:', error);
      return false;
    }
  });

  ipcMain.handle('remove-api-key', async () => {
    try {
      await settingsService.removeApiKey();
      geminiService.setApiKey('');
      return true;
    } catch (error) {
      console.error('Remove API key error:', error);
      return false;
    }
  });

  console.log('IPC handlers initialized');
}
