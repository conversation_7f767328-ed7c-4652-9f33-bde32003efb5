"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.geminiService = void 0;
const axios_1 = __importDefault(require("axios"));
const constants_1 = require("../shared/constants");
class GeminiService {
    constructor() {
        this.apiKey = null;
        this.baseURL = constants_1.APP_CONSTANTS.GEMINI_API_BASE_URL;
        this.model = constants_1.APP_CONSTANTS.DEFAULT_MODEL;
        this.apiClient = axios_1.default.create({
            timeout: constants_1.APP_CONSTANTS.REQUEST_TIMEOUT_MS,
            headers: {
                'Content-Type': 'application/json',
            },
        });
        this.setupInterceptors();
    }
    /**
     * Set the API key for Gemini service
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }
    /**
     * Get the current API key
     */
    getApiKey() {
        return this.apiKey;
    }
    /**
     * Check if API key is configured
     */
    isConfigured() {
        return !!this.apiKey;
    }
    /**
     * Send a query to Gemini AI
     */
    async query(request) {
        if (!this.apiKey) {
            throw new Error(constants_1.ErrorCodes.API_KEY_MISSING);
        }
        try {
            const prompt = this.buildPrompt(request);
            const payload = {
                contents: [
                    {
                        parts: [
                            {
                                text: prompt,
                            },
                        ],
                    },
                ],
                generationConfig: {
                    temperature: request.options?.temperature || constants_1.APP_CONSTANTS.TEMPERATURE,
                    maxOutputTokens: request.options?.maxTokens || constants_1.APP_CONSTANTS.MAX_TOKENS,
                    topP: 0.8,
                    topK: 40,
                },
                safetySettings: [
                    {
                        category: 'HARM_CATEGORY_HARASSMENT',
                        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
                    },
                    {
                        category: 'HARM_CATEGORY_HATE_SPEECH',
                        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
                    },
                    {
                        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
                    },
                    {
                        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
                    },
                ],
            };
            const response = await this.apiClient.post(`${this.baseURL}/models/${this.model}:generateContent?key=${this.apiKey}`, payload);
            return this.parseResponse(response.data, request.options?.model || this.model);
        }
        catch (error) {
            console.error('Gemini API error:', error);
            throw this.handleError(error);
        }
    }
    /**
     * Build the prompt with context
     */
    buildPrompt(request) {
        let prompt = '';
        // Add system context for PropertyClub AI
        prompt += `You are PropertyClub AI, an intelligent real estate assistant specialized in the Bangalore property market. You help users with:

1. Property search and recommendations
2. Market analysis and trends
3. Locality information and insights
4. Investment advice and ROI calculations
5. Legal and regulatory guidance
6. Price comparisons and negotiations

Key Guidelines:
- Focus specifically on Bangalore real estate market
- Provide accurate, helpful, and actionable information
- Use Indian Rupees (₹) for all price mentions
- Consider local factors like connectivity, amenities, and infrastructure
- Be conversational but professional
- If you don't have specific information, acknowledge it and suggest alternatives

`;
        // Add conversation history if available
        if (request.context?.conversationHistory && request.context.conversationHistory.length > 0) {
            prompt += 'Previous conversation:\n';
            request.context.conversationHistory.slice(-5).forEach((msg) => {
                prompt += `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.message}\n`;
            });
            prompt += '\n';
        }
        // Add user preferences if available
        if (request.context?.userPreferences) {
            const prefs = request.context.userPreferences;
            prompt += `User preferences:
- Budget: ${prefs.maxBudget ? `₹${prefs.maxBudget.toLocaleString('en-IN')}` : 'Not specified'}
- Property types: ${prefs.propertyTypes.join(', ')}
- Preferred localities: ${prefs.preferredLocalities.join(', ')}
- Transaction type: ${prefs.transactionType}

`;
        }
        // Add current location context if available
        if (request.context?.currentLocation) {
            prompt += `Current context: User is interested in ${request.context.currentLocation}\n\n`;
        }
        // Add the actual user query
        prompt += `User query: ${request.prompt}

Please provide a helpful, accurate response focused on Bangalore real estate.`;
        return prompt;
    }
    /**
     * Parse Gemini API response
     */
    parseResponse(apiResponse, model) {
        if (!apiResponse.candidates || apiResponse.candidates.length === 0) {
            throw new Error('No response generated from Gemini API');
        }
        const candidate = apiResponse.candidates[0];
        if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
            throw new Error('Invalid response format from Gemini API');
        }
        const responseText = candidate.content.parts[0].text;
        const tokensUsed = apiResponse.usageMetadata?.totalTokenCount || 0;
        return {
            response: responseText,
            tokensUsed,
            model,
            timestamp: new Date().toISOString(),
            metadata: {
                confidence: this.calculateConfidence(candidate),
                sources: this.extractSources(responseText),
                relatedQueries: this.generateRelatedQueries(responseText),
            },
        };
    }
    /**
     * Calculate confidence score based on safety ratings and finish reason
     */
    calculateConfidence(candidate) {
        if (candidate.finishReason !== 'STOP') {
            return 0.5; // Lower confidence for incomplete responses
        }
        // Base confidence
        let confidence = 0.8;
        // Adjust based on safety ratings
        if (candidate.safetyRatings) {
            const highRiskRatings = candidate.safetyRatings.filter((rating) => rating.probability === 'HIGH' || rating.probability === 'MEDIUM');
            confidence -= highRiskRatings.length * 0.1;
        }
        return Math.max(0.1, Math.min(1.0, confidence));
    }
    /**
     * Extract potential sources from response
     */
    extractSources(response) {
        const sources = [];
        // Look for common real estate websites mentioned
        const websitePatterns = [
            /99acres/gi,
            /magicbricks/gi,
            /housing\.com/gi,
            /commonfloor/gi,
            /nobroker/gi,
            /rera\.karnataka\.gov\.in/gi,
        ];
        websitePatterns.forEach(pattern => {
            if (pattern.test(response)) {
                const match = response.match(pattern);
                if (match) {
                    sources.push(match[0]);
                }
            }
        });
        return [...new Set(sources)]; // Remove duplicates
    }
    /**
     * Generate related queries based on response content
     */
    generateRelatedQueries(response) {
        const queries = [];
        // Extract locality names mentioned
        const localityPattern = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:Road|Street|Layout|Extension|Phase|Block))?/g;
        const localities = response.match(localityPattern) || [];
        localities.slice(0, 3).forEach(locality => {
            queries.push(`Tell me more about ${locality}`);
            queries.push(`What are the property prices in ${locality}?`);
        });
        // Add generic related queries
        if (response.toLowerCase().includes('price')) {
            queries.push('What factors affect property prices in Bangalore?');
        }
        if (response.toLowerCase().includes('investment')) {
            queries.push('Which areas have the best ROI in Bangalore?');
        }
        return queries.slice(0, 4); // Limit to 4 related queries
    }
    /**
     * Handle API errors
     */
    handleError(error) {
        if (axios_1.default.isAxiosError(error)) {
            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;
                switch (status) {
                    case 400:
                        return new Error(constants_1.ErrorCodes.INVALID_INPUT);
                    case 401:
                        return new Error(constants_1.ErrorCodes.API_KEY_INVALID);
                    case 403:
                        return new Error(constants_1.ErrorCodes.API_QUOTA_EXCEEDED);
                    case 429:
                        return new Error(constants_1.ErrorCodes.API_QUOTA_EXCEEDED);
                    default:
                        return new Error(`${constants_1.ErrorCodes.API_REQUEST_FAILED}: ${data?.error?.message || 'Unknown error'}`);
                }
            }
            else if (error.request) {
                return new Error(constants_1.ErrorCodes.NETWORK_ERROR);
            }
        }
        if (error.code === 'ECONNABORTED') {
            return new Error(constants_1.ErrorCodes.TIMEOUT_ERROR);
        }
        return new Error(`${constants_1.ErrorCodes.UNKNOWN_ERROR}: ${error.message}`);
    }
    /**
     * Setup axios interceptors
     */
    setupInterceptors() {
        // Request interceptor
        this.apiClient.interceptors.request.use((config) => {
            console.log(`Gemini API Request: ${config.method?.toUpperCase()} ${config.url}`);
            return config;
        }, (error) => {
            console.error('Gemini API Request Error:', error);
            return Promise.reject(error);
        });
        // Response interceptor
        this.apiClient.interceptors.response.use((response) => {
            console.log(`Gemini API Response: ${response.status} ${response.statusText}`);
            return response;
        }, (error) => {
            console.error('Gemini API Response Error:', error);
            return Promise.reject(error);
        });
    }
}
// Export singleton instance
exports.geminiService = new GeminiService();
exports.default = exports.geminiService;
//# sourceMappingURL=geminiService.js.map