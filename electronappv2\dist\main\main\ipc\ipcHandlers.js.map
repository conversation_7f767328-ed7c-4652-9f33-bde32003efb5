{"version": 3, "file": "ipcHandlers.js", "sourceRoot": "", "sources": ["../../../../src/main/ipc/ipcHandlers.ts"], "names": [], "mappings": ";;AAUA,4CAoKC;AA9KD,uCAAmC;AACnC,sDAAkE;AAClE,gEAA6D;AAC7D,oEAAiE;AACjE,sDAAsD;AACtD,8CAAgD;AAEhD;;GAEG;AACH,SAAgB,gBAAgB;IAC9B,oBAAoB;IACpB,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,eAAe,EAAE,GAAG,EAAE;QAC/C,OAAO,OAAO,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,YAAY,EAAE,GAAG,EAAE;QAC5C,OAAO,OAAO,CAAC,uBAAuB,CAAC,CAAC,WAAW,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QACtD,yCAAyC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;QACrE,sCAAsC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,gBAAgB;IAChB,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;QAC9E,IAAI,CAAC;YACH,6DAA6D;YAC7D,IAAI,CAAC,6BAAa,CAAC,YAAY,EAAE,EAAE,CAAC;gBAClC,MAAM,MAAM,GAAG,MAAM,iCAAe,CAAC,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;gBACtF,CAAC;gBACD,6BAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAClC,CAAC;YAED,uBAAuB;YACvB,MAAM,cAAc,GAAG,MAAM,6BAAa,CAAC,KAAK,CAAC;gBAC/C,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,OAAO,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,EAAE,GAAG,IAAA,sBAAW,GAAE,CAAC;YACzB,MAAM,SAAS,GAAG,IAAA,kBAAU,GAAE,CAAC;YAE/B,MAAM,EAAE,CAAC,GAAG,CACV,mHAAmH,EACnH,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,QAAQ,EAAE,uBAAW,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,cAAc,CAAC,UAAU,CAAC,CACrH,CAAC;YAEF,OAAO;gBACL,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,cAAc,CAAC,QAAQ;gBAChC,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,IAAI,EAAE,uBAAW,CAAC,EAAE;gBACpB,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,QAAQ,EAAE,cAAc,CAAC,QAAQ;aAClC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO;gBACL,EAAE,EAAE,IAAA,kBAAU,GAAE;gBAChB,OAAO,EAAE,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;gBACrG,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,IAAI,EAAE,uBAAW,CAAC,KAAK;aACxB,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACnE,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,IAAA,sBAAW,GAAE,CAAC;YACzB,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CACvB,oGAAoG,EACpG,CAAC,MAAM,IAAI,IAAI,CAAC,CACjB,CAAC;YAEF,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,QAAQ,EAAE,GAAG,CAAC,QAAQ;gBACtB,IAAI,EAAE,GAAG,CAAC,YAAY;gBACtB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,UAAU,EAAE,GAAG,CAAC,WAAW;aAC5B,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE;QACzE,kCAAkC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QAC3E,6CAA6C;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,4BAA4B;IAC5B,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QAClE,kCAAkC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,oBAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACvE,6CAA6C;QAC7C,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QAClD,IAAI,CAAC;YACH,OAAO,MAAM,iCAAe,CAAC,WAAW,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,iCAAe,CAAC,kBAAkB,EAAE,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,uBAAW,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;QAClE,IAAI,CAAC;YACH,MAAM,iCAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;QACvC,IAAI,CAAC;YACH,OAAO,MAAM,iCAAe,CAAC,SAAS,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;QACpD,IAAI,CAAC;YACH,MAAM,iCAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACxC,6BAAa,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QAC1C,IAAI,CAAC;YACH,MAAM,iCAAe,CAAC,YAAY,EAAE,CAAC;YACrC,6BAAa,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;AAC1C,CAAC"}