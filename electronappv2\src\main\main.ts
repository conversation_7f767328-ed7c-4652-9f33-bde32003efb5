import { app, BrowserWindow, <PERSON>u } from 'electron';
import * as path from 'path';
import { isDev } from '../shared/utils';
import { createMainWindow } from './windows/mainWindow';
import { setupIpcHandlers } from './ipc/ipcHandlers';
import { initializeDatabase } from '../database/database';
import { authService } from '../services/authService';
import { knowledgeService } from '../services/knowledgeService';
import { propertySearchService } from '../services/propertySearchService';
import { marketAnalysisService } from '../services/marketAnalysisService';
import { dataVisualizationService } from '../services/dataVisualizationService';
import { externalApiService } from '../services/externalApiService';
import { advancedAnalyticsService } from '../services/advancedAnalyticsService';

// Suppress DevTools warnings in console
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  const message = args.join(' ');
  if (message.includes('Autofill.enable') || 
      message.includes('Autofill.setAddresses') ||
      message.includes("wasn't found")) {
    return; // Don't log these specific DevTools warnings
  }
  originalConsoleError.apply(console, args);
};

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

class PropertyClubAI {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    // Initialize database
    await initializeDatabase();

    // Set up IPC handlers
    setupIpcHandlers();

    // Handle app events
    this.setupAppEvents();

    // Create application menu
    this.createMenu();
  }

  private setupAppEvents(): void {
    // This method will be called when Electron has finished initialization
    app.whenReady().then(async () => {
      await this.initializeServices();
      this.createMainWindow();

      // On OS X it's common to re-create a window in the app when the
      // dock icon is clicked and there are no other windows open.
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Quit when all windows are closed, except on macOS
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Security: Prevent new window creation and add additional security
    app.on('web-contents-created', (_event, contents) => {
      // Prevent new window creation
      contents.setWindowOpenHandler(() => {
        return { action: 'deny' };
      });

      // Prevent navigation to external URLs
      contents.on('will-navigate', (event, navigationUrl) => {
        // Only allow file protocol navigation
        if (!navigationUrl.startsWith('file://')) {
          event.preventDefault();
        }
      });

      // Prevent loading external resources
      contents.on('will-attach-webview', (event) => {
        event.preventDefault();
      });

      // Block permission requests
      contents.session.setPermissionRequestHandler((_webContents, _permission, callback) => {
        // Deny all permission requests for security
        callback(false);
      });
    });
  }

  private async initializeServices(): Promise<void> {
    try {
      console.log('Initializing services...');

      // Initialize database
      await initializeDatabase();

      // Initialize authentication service
      await authService.initialize();

      // Initialize knowledge service
      await knowledgeService.initialize();

      // Initialize property search service
      await propertySearchService.initialize();

      // Initialize market analysis service
      await marketAnalysisService.initialize();

      // Initialize data visualization service
      await dataVisualizationService.initialize();

      // Initialize external API service
      await externalApiService.initialize();

      // Initialize advanced analytics service
      await advancedAnalyticsService.initialize();

      console.log('Services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error;
    }
  }

  private createMainWindow(): void {
    this.mainWindow = createMainWindow();
  }

  private createMenu(): void {
    if (isDev()) {
      // Development menu with DevTools
      const template = [
        {
          label: 'View',
          submenu: [
            { role: 'reload' },
            { role: 'forceReload' },
            { role: 'toggleDevTools' },
            { type: 'separator' },
            { role: 'resetZoom' },
            { role: 'zoomIn' },
            { role: 'zoomOut' },
            { type: 'separator' },
            { role: 'togglefullscreen' as any }
          ]
        }
      ];
      const menu = Menu.buildFromTemplate(template as any);
      Menu.setApplicationMenu(menu);
    } else {
      // Production menu (minimal)
      Menu.setApplicationMenu(null);
    }
  }
}

// Initialize PropertyClubAI application
new PropertyClubAI();
