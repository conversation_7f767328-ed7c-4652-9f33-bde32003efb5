"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.settingsService = void 0;
const electron_1 = require("electron");
const fs_1 = require("fs");
const path = __importStar(require("path"));
class SettingsService {
    constructor() {
        this.settings = null;
        const userDataPath = electron_1.app.getPath('userData');
        this.settingsPath = path.join(userDataPath, 'settings.json');
    }
    /**
     * Load settings from file
     */
    async loadSettings() {
        try {
            const data = await fs_1.promises.readFile(this.settingsPath, 'utf-8');
            this.settings = JSON.parse(data);
            return this.settings;
        }
        catch (error) {
            // If file doesn't exist or is corrupted, return default settings
            this.settings = this.getDefaultSettings();
            await this.saveSettings(this.settings);
            return this.settings;
        }
    }
    /**
     * Save settings to file
     */
    async saveSettings(settings) {
        try {
            // Ensure directory exists
            const dir = path.dirname(this.settingsPath);
            await fs_1.promises.mkdir(dir, { recursive: true });
            // Save settings
            await fs_1.promises.writeFile(this.settingsPath, JSON.stringify(settings, null, 2));
            this.settings = settings;
        }
        catch (error) {
            console.error('Failed to save settings:', error);
            throw error;
        }
    }
    /**
     * Get current settings
     */
    async getSettings() {
        if (!this.settings) {
            return await this.loadSettings();
        }
        return this.settings;
    }
    /**
     * Update specific setting
     */
    async updateSetting(category, updates) {
        const currentSettings = await this.getSettings();
        currentSettings[category] = { ...currentSettings[category], ...updates };
        await this.saveSettings(currentSettings);
    }
    /**
     * Get API key (encrypted storage would be better for production)
     */
    async getApiKey() {
        const settings = await this.getSettings();
        return settings.apiKey || null;
    }
    /**
     * Set API key (encrypted storage would be better for production)
     */
    async setApiKey(apiKey) {
        const settings = await this.getSettings();
        settings.apiKey = apiKey;
        await this.saveSettings(settings);
    }
    /**
     * Remove API key
     */
    async removeApiKey() {
        const settings = await this.getSettings();
        delete settings.apiKey;
        await this.saveSettings(settings);
    }
    /**
     * Reset settings to default
     */
    async resetSettings() {
        const defaultSettings = this.getDefaultSettings();
        await this.saveSettings(defaultSettings);
        return defaultSettings;
    }
    /**
     * Get default settings
     */
    getDefaultSettings() {
        return {
            general: {
                language: 'en',
                theme: 'system',
                autoUpdate: true,
                startMinimized: false,
            },
            ai: {
                model: 'gemini-2.5-flash',
                temperature: 0.7,
                maxTokens: 8192,
                contextLength: 10,
            },
            privacy: {
                saveConversations: true,
                shareUsageData: false,
                autoDeleteOldChats: false,
                retentionDays: 30,
            },
            notifications: {
                priceAlerts: true,
                newListings: true,
                marketUpdates: true,
                systemUpdates: true,
            },
        };
    }
    /**
     * Validate settings structure
     */
    validateSettings(settings) {
        return (settings &&
            typeof settings === 'object' &&
            settings.general &&
            settings.ai &&
            settings.privacy &&
            settings.notifications);
    }
    /**
     * Migrate settings from older versions
     */
    migrateSettings(settings) {
        const defaultSettings = this.getDefaultSettings();
        // Merge with defaults to ensure all properties exist
        return {
            general: { ...defaultSettings.general, ...settings.general },
            ai: { ...defaultSettings.ai, ...settings.ai },
            privacy: { ...defaultSettings.privacy, ...settings.privacy },
            notifications: { ...defaultSettings.notifications, ...settings.notifications },
        };
    }
}
// Export singleton instance
exports.settingsService = new SettingsService();
exports.default = exports.settingsService;
//# sourceMappingURL=settingsService.js.map