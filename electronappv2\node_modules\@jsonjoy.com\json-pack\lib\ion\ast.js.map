{"version": 3, "file": "ast.js", "sourceRoot": "", "sources": ["../../src/ion/ast.ts"], "names": [], "mappings": ";;;AAAA,6DAA4D;AAY5D,MAAa,WAAW;IAAxB;QACkB,QAAG,GAAG,IAAI,CAAC;QACX,QAAG,GAAG,CAAC,CAAC;IAI1B,CAAC;IAHQ,UAAU;QACf,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAND,kCAMC;AAED,MAAa,WAAW;IAEtB,YAA4B,GAAY;QAAZ,QAAG,GAAH,GAAG,CAAS;QADxB,QAAG,GAAG,CAAC,CAAC;IACmB,CAAC;IACrC,UAAU;QACf,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAND,kCAMC;AAED,MAAa,WAAW;IAEtB,YAA4B,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;QACrC,IAAI,CAAC,GAAG;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAClB,IAAI,GAAG,IAAI,IAAI;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAC9B,IAAI,GAAG,IAAI,MAAM;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAChC,IAAI,GAAG,IAAI,QAAQ;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAClC,IAAI,GAAG,IAAI,UAAU;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACpC,IAAI,GAAG,IAAI,YAAY;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACtC,IAAI,GAAG,IAAI,cAAc;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;;YACxC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACpB,CAAC;IACM,UAAU;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACtB,CAAC;CACF;AAfD,kCAeC;AAED,MAAa,WAAW;IAEtB,YAA4B,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;QACrC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC;QAClB,IAAI,CAAC,IAAI;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACnB,IAAI,IAAI,IAAI,IAAI;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAC/B,IAAI,IAAI,IAAI,MAAM;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACjC,IAAI,IAAI,IAAI,QAAQ;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACnC,IAAI,IAAI,IAAI,UAAU;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACrC,IAAI,IAAI,IAAI,YAAY;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aACvC,IAAI,IAAI,IAAI,cAAc;YAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;;YACzC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACpB,CAAC;IACM,UAAU;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACtB,CAAC;CACF;AAhBD,kCAgBC;AAED,MAAa,YAAY;IAEvB,YAA4B,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;QADvB,QAAG,GAAW,CAAC,CAAC;IACU,CAAC;IACpC,UAAU;QACf,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IACtB,CAAC;CACF;AAND,oCAMC;AAED,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAU,EAAE;IACvC,IAAI,GAAG,IAAI,SAAS;QAAE,OAAO,CAAC,CAAC;SAC1B,IAAI,GAAG,IAAI,KAAiB;QAAE,OAAO,CAAC,CAAC;SACvC,IAAI,GAAG,IAAI,OAAyB;QAAE,OAAO,CAAC,CAAC;SAC/C,IAAI,GAAG,IAAI,SAAiC;QAAE,OAAO,CAAC,CAAC;SACvD,IAAI,GAAG,IAAI,WAAyC;QAAE,OAAO,CAAC,CAAC;;QAC/D,OAAO,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,MAAa,UAAU;IAErB,YAA4B,GAAW;QAAX,QAAG,GAAH,GAAG,CAAQ;QACrC,IAAI,CAAC,GAAG,GAAG,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IACM,UAAU;QACf,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC1E,CAAC;CACF;AARD,gCAQC;AAED,MAAa,UAAU;IAErB,YAA4B,GAAe;QAAf,QAAG,GAAH,GAAG,CAAY;QACzC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;IACxB,CAAC;IACM,UAAU;QACf,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC1E,CAAC;CACF;AARD,gCAQC;AAED,MAAa,UAAU;IAErB,YAA4B,GAA8B;QAA9B,QAAG,GAAH,GAAG,CAA2B;QACxD,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACf,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,MAAM;gBAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;iBACzB,CAAC;gBACJ,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAE,aAAa,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBAC1E,IAAI,CAAC,GAAG,GAAG,aAAa,CAAC;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IACM,UAAU;QACf,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC1E,CAAC;CACF;AAjBD,gCAiBC;AAED,MAAa,UAAU;IAErB,YAA4B,GAAyC;QAAzC,QAAG,GAAH,GAAG,CAAsC;QACnE,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACf,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,IAAI;gBAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;iBACvB,CAAC;gBACJ,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;oBAC7B,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChD,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC;IACM,UAAU;QACf,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC1E,CAAC;CACF;AAnBD,gCAmBC;AAED,MAAa,iBAAiB;IAG5B,YACkB,GAAqB,EACrB,WAAqB;QADrB,QAAG,GAAH,GAAG,CAAkB;QACrB,gBAAW,GAAX,WAAW,CAAU;QAErC,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;QACzB,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC;QACrB,GAAG,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IACM,UAAU;QACf,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;IAC1E,CAAC;CACF;AAjBD,8CAiBC;AAED,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAEpC,MAAM,KAAK,GAAG,CAAC,GAAY,EAAE,OAAe,EAAoB,EAAE;IACvE,IAAI,GAAG,KAAK,IAAI;QAAE,OAAO,IAAI,WAAW,EAAE,CAAC;IAC3C,IAAI,GAAG,YAAY,KAAK;QAAE,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAA,aAAK,EAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IACrF,IAAI,GAAG,YAAY,UAAU;QAAE,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAC1D,QAAQ,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,SAAS;YACZ,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;QAC9B,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,IAAI,aAAa,CAAC,GAAG,CAAC;gBAAE,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;;gBACjF,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;QACD,KAAK,QAAQ;YACX,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,MAAM,MAAM,GAAG,IAAI,GAAG,EAA4B,CAAC;YACnD,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;gBACtB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAA,aAAK,EAAE,GAAW,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;AAClC,CAAC,CAAC;AAvBW,QAAA,KAAK,SAuBhB"}