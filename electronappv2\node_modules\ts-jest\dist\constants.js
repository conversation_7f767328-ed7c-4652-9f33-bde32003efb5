"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_JEST_TEST_MATCH = exports.JS_EXT_TO_TREAT_AS_ESM = exports.TS_EXT_TO_TREAT_AS_ESM = exports.ESM_JS_TRANSFORM_PATTERN = exports.JS_TRANSFORM_PATTERN = exports.ESM_TS_JS_TRANSFORM_PATTERN = exports.TS_JS_TRANSFORM_PATTERN = exports.ESM_TS_TRANSFORM_PATTERN = exports.TS_TRANSFORM_PATTERN = exports.JS_JSX_REGEX = exports.TS_TSX_REGEX = exports.JS_JSX_EXTENSIONS = exports.DECLARATION_TYPE_EXT = exports.LINE_FEED = void 0;
exports.LINE_FEED = '\n';
exports.DECLARATION_TYPE_EXT = '.d.ts';
exports.JS_JSX_EXTENSIONS = ['.js', '.jsx'];
exports.TS_TSX_REGEX = /\.[cm]?tsx?$/;
exports.JS_JSX_REGEX = /\.[cm]?jsx?$/;
exports.TS_TRANSFORM_PATTERN = '^.+\\.tsx?$';
exports.ESM_TS_TRANSFORM_PATTERN = '^.+\\.m?tsx?$';
exports.TS_JS_TRANSFORM_PATTERN = '^.+\\.[tj]sx?$';
exports.ESM_TS_JS_TRANSFORM_PATTERN = '^.+\\.m?[tj]sx?$';
exports.JS_TRANSFORM_PATTERN = '^.+\\.jsx?$';
exports.ESM_JS_TRANSFORM_PATTERN = '^.+\\.m?jsx?$';
// `extensionsToTreatAsEsm` will throw error with `.mjs`
exports.TS_EXT_TO_TREAT_AS_ESM = ['.ts', '.tsx', '.mts'];
exports.JS_EXT_TO_TREAT_AS_ESM = ['.jsx'];
/**
 * @internal
 * See https://jestjs.io/docs/en/configuration#testmatch-arraystring
 */
exports.DEFAULT_JEST_TEST_MATCH = ['**/__tests__/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[jt]s?(x)'];
