import React, { useState } from 'react';

const PropertyClubInterface: React.FC = () => {
  const [inputMessage, setInputMessage] = useState('');

  const trendingLocations = [
    { id: '1', name: 'Whitefield', description: 'IT hub, excellent connectivity', priceRange: '₹80L - ₹2.5Cr' },
    { id: '2', name: 'Koramangala', description: 'Startup ecosystem, vibrant nightlife', priceRange: '₹1.2Cr - ₹4Cr' },
    { id: '3', name: 'Electronic City', description: 'Major IT corridor, affordable housing', priceRange: '₹60L - ₹1.8Cr' }
  ];

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">PropertyClub AI</h1>
            <p className="text-sm text-gray-500">Bangalore Real Estate Assistant</p>
          </div>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Share
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-6xl mx-auto p-6">
          {/* Logo and Search */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mr-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <span className="text-4xl font-bold text-gray-900">PropertyClub AI</span>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Ask anything about Bangalore real estate..."
                className="w-full px-6 py-4 pr-16 text-lg border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                rows={1}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
                <button className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Trending Hotspots */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Trending Hotspots</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {trendingLocations.map((location, index) => (
                <div key={location.id} className="trending-card">
                  <div className={`aspect-video relative ${
                    index === 0 ? 'gradient-bg-1' :
                    index === 1 ? 'gradient-bg-2' : 'gradient-bg-3'
                  }`}>
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-xl font-bold">{location.name}</h3>
                      <p className="text-sm opacity-90">{location.description}</p>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-sm font-medium text-blue-600">{location.priceRange}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Success Message */}
          <div className="text-center">
            <div className="bg-green-100 border border-green-200 rounded-xl p-6">
              <h2 className="text-2xl font-bold text-green-800 mb-2">🎉 PropertyClub AI is Working!</h2>
              <p className="text-green-700 text-lg">The interface is now loading and persisting successfully!</p>
              <p className="text-sm text-green-600 mt-2">Routing fixed - Ready for full Perplexity-style features!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyClubInterface;