import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { addMessage, setLoading, setError } from '../../store/slices/chatSlice';
import { MessageType } from '../../../shared/constants';

interface TrendingLocation {
  id: string;
  name: string;
  description: string;
  priceRange: string;
}

interface InspirationLocation {
  id: string;
  title: string;
  subtitle: string;
  category: string;
}

const PropertyClubInterface: React.FC = () => {
  const dispatch = useDispatch();
  const { messages, isLoading, error } = useSelector((state: RootState) => state.chat);
  const [inputMessage, setInputMessage] = useState('');
  const [showChat, setShowChat] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Top 3 trending locations
  const trendingLocations: TrendingLocation[] = [
    {
      id: '1',
      name: 'Whitefield',
      description: 'IT hub, excellent connectivity',
      priceRange: '₹80L - ₹2.5Cr'
    },
    {
      id: '2',
      name: 'Koramangala',
      description: 'Startup ecosystem, vibrant nightlife',
      priceRange: '₹1.2Cr - ₹4Cr'
    },
    {
      id: '3',
      name: 'Electronic City',
      description: 'Major IT corridor, affordable housing',
      priceRange: '₹60L - ₹1.8Cr'
    }
  ];

  // Top 10 inspiration locations
  const inspirationLocations: InspirationLocation[] = [
    {
      id: '1',
      title: 'Best luxury apartments in Indiranagar',
      subtitle: 'Premium living spaces',
      category: 'Luxury'
    },
    {
      id: '2',
      title: 'Affordable 2BHK in Marathahalli',
      subtitle: 'Great value for money',
      category: 'Affordable'
    },
    {
      id: '3',
      title: 'Investment opportunities in Sarjapur',
      subtitle: 'High ROI potential',
      category: 'Investment'
    },
    {
      id: '4',
      title: 'Family homes in JP Nagar',
      subtitle: 'Perfect for growing families',
      category: 'Family'
    },
    {
      id: '5',
      title: 'Modern villas in Hennur',
      subtitle: 'Spacious independent living',
      category: 'Villa'
    },
    {
      id: '6',
      title: 'Tech-friendly areas near Manyata',
      subtitle: 'Close to major IT parks',
      category: 'Tech Hub'
    },
    {
      id: '7',
      title: 'Premium plots in Devanahalli',
      subtitle: 'Near airport, high appreciation',
      category: 'Plots'
    },
    {
      id: '8',
      title: 'Gated communities in Yelahanka',
      subtitle: 'Secure family living',
      category: 'Gated Community'
    },
    {
      id: '9',
      title: 'Commercial spaces in Brigade Road',
      subtitle: 'Prime business location',
      category: 'Commercial'
    },
    {
      id: '10',
      title: 'Upcoming projects in Thanisandra',
      subtitle: 'Future growth potential',
      category: 'New Launch'
    }
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    setShowChat(true);

    const userMessage = {
      id: Date.now().toString(),
      message: inputMessage,
      type: MessageType.USER,
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMessage));
    const currentInput = inputMessage;
    setInputMessage('');
    dispatch(setLoading(true));
    dispatch(setError(null));

    try {
      const response = await window.electronAPI.sendChatMessage(currentInput);

      const aiMessage = {
        id: response.id || (Date.now() + 1).toString(),
        message: response.message,
        type: response.type || MessageType.AI,
        timestamp: response.timestamp || new Date().toISOString(),
        tokensUsed: response.tokensUsed,
        metadata: response.metadata,
      };

      dispatch(addMessage(aiMessage));

      if (response.type === MessageType.ERROR) {
        dispatch(setError(response.message));
      }
    } catch (err) {
      console.error('Chat error:', err);
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        message: 'Sorry, I encountered an error. Please check your API key in Settings and try again.',
        type: MessageType.ERROR,
        timestamp: new Date().toISOString(),
      };
      dispatch(addMessage(errorMessage));
      dispatch(setError('Failed to send message. Please check your API key configuration.'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    setShowChat(true);
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  useEffect(() => {
    if (messages.length > 0) {
      setShowChat(true);
    }
  }, [messages]);

  if (showChat) {
    return (
      <div className="flex flex-col h-full bg-white">
        {/* Chat Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <button
            onClick={() => setShowChat(false)}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to PropertyClub AI
          </button>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <span className="font-medium text-gray-900">PropertyClub AI</span>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === MessageType.USER ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-2xl px-4 py-3 rounded-2xl ${
                  message.type === MessageType.USER
                    ? 'bg-blue-600 text-white'
                    : message.type === MessageType.ERROR
                    ? 'bg-red-50 text-red-800 border border-red-200'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.message}</p>
                <div className="flex items-center justify-between mt-2">
                  <p className="text-xs opacity-70">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </p>
                  {message.tokensUsed && (
                    <p className="text-xs opacity-70">
                      {message.tokensUsed} tokens
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 text-gray-900 px-4 py-3 rounded-2xl">
                <div className="loading-dots">
                  <div></div>
                  <div></div>
                  <div></div>
                </div>
              </div>
            </div>
          )}

          {error && (
            <div className="flex justify-center">
              <div className="bg-red-50 text-red-700 px-4 py-3 rounded-2xl text-sm border border-red-200">
                {error}
              </div>
            </div>
          )}
        </div>

        {/* Chat Input */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-end space-x-3">
            <div className="flex-1 relative">
              <textarea
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask anything about Bangalore real estate..."
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={1}
                disabled={isLoading}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="absolute right-2 bottom-2 p-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">PropertyClub AI</h1>
            <p className="text-sm text-gray-500">Bangalore Real Estate Assistant</p>
          </div>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Share
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-6xl mx-auto p-6">
          {/* Logo and Search */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mr-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <span className="text-4xl font-bold text-gray-900">PropertyClub AI</span>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <textarea
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask anything about Bangalore real estate..."
                className="w-full px-6 py-4 pr-16 text-lg border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                rows={1}
                disabled={isLoading}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
                <button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Content Layout */}
          <div className="flex gap-8">
            <div className="flex-1">
              {/* Trending Hotspots */}
              <div className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Trending Hotspots</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {trendingLocations.map((location, index) => (
                    <div
                      key={location.id}
                      onClick={() => handleSuggestionClick(`Tell me about properties in ${location.name}`)}
                      className="trending-card"
                    >
                      <div className={`aspect-video relative ${
                        index === 0 ? 'gradient-bg-1' :
                        index === 1 ? 'gradient-bg-2' : 'gradient-bg-3'
                      }`}>
                        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div className="absolute bottom-4 left-4 text-white">
                          <h3 className="text-xl font-bold">{location.name}</h3>
                          <p className="text-sm opacity-90">{location.description}</p>
                        </div>
                      </div>
                      <div className="p-4">
                        <p className="text-sm font-medium text-blue-600">{location.priceRange}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Inspiration */}
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Inspiration</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {inspirationLocations.map((location, index) => (
                    <div
                      key={location.id}
                      onClick={() => handleSuggestionClick(location.title)}
                      className="inspiration-card"
                    >
                      <div className={`aspect-video relative ${
                        index % 6 === 0 ? 'gradient-bg-1' :
                        index % 6 === 1 ? 'gradient-bg-2' :
                        index % 6 === 2 ? 'gradient-bg-3' :
                        index % 6 === 3 ? 'gradient-bg-4' :
                        index % 6 === 4 ? 'gradient-bg-5' : 'gradient-bg-6'
                      }`}>
                        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                        <div className="absolute bottom-4 left-4 text-white">
                          <span className="inline-block px-2 py-1 bg-white bg-opacity-20 rounded-full text-xs font-medium mb-2">
                            {location.category}
                          </span>
                          <h3 className="text-lg font-bold leading-tight">{location.title}</h3>
                          <p className="text-sm opacity-90">{location.subtitle}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Sidebar */}
            <div className="w-80">
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Explore</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handleSuggestionClick("Show me trending properties in Bangalore")}
                    className="w-full text-left p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    Trending Properties
                  </button>
                  <button
                    onClick={() => handleSuggestionClick("What are the best investment areas in Bangalore?")}
                    className="w-full text-left p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    Investment Opportunities
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyClubInterface;
