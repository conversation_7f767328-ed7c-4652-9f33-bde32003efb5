/**
 * IPC Channel names for communication between main and renderer processes
 */
export enum IpcChannels {
  // App info
  GET_APP_VERSION = 'get-app-version',
  GET_APP_NAME = 'get-app-name',

  // User management
  GET_USER_PROFILE = 'get-user-profile',
  SAVE_USER_PROFILE = 'save-user-profile',
  DELETE_USER_PROFILE = 'delete-user-profile',

  // Authentication
  LOGIN_USER = 'login-user',
  LOGOUT_USER = 'logout-user',
  REGISTER_USER = 'register-user',

  // Chat functionality
  SEND_CHAT_MESSAGE = 'send-chat-message',
  GET_CHAT_HISTORY = 'get-chat-history',
  CLEAR_CHAT_HISTORY = 'clear-chat-history',
  DELETE_CHAT_MESSAGE = 'delete-chat-message',

  // Property search
  SEARCH_PROPERTIES = 'search-properties',
  GET_PROPERTY_DETAILS = 'get-property-details',
  GET_PROPERTY_SUGGESTIONS = 'get-property-suggestions',
  GET_MARKET_TRENDS = 'get-market-trends',

  // Saved properties
  SAVE_PROPERTY = 'save-property',
  UNSAVE_PROPERTY = 'unsave-property',
  GET_SAVED_PROPERTIES = 'get-saved-properties',

  // Bangalore localities
  GET_LOCALITIES = 'get-localities',
  GET_LOCALITY_INFO = 'get-locality-info',
  GET_LOCALITY_PRICES = 'get-locality-prices',
  SEARCH_LOCALITIES = 'search-localities',
  GET_MARKET_ANALYSIS = 'get-market-analysis',
  GET_INVESTMENT_RECOMMENDATIONS = 'get-investment-recommendations',
  GET_COMPARATIVE_ANALYSIS = 'get-comparative-analysis',
  CALCULATE_ROI = 'calculate-roi',
  GET_PRICE_TRENDS = 'get-price-trends',
  GET_INVESTMENT_OPPORTUNITIES = 'get-investment-opportunities',
  GENERATE_MARKET_REPORT = 'generate-market-report',

  // Data visualization
  GET_PRICE_TREND_CHART_DATA = 'get-price-trend-chart-data',
  GET_MARKET_ANALYSIS_CHART_DATA = 'get-market-analysis-chart-data',
  GET_PROPERTY_DISTRIBUTION_DATA = 'get-property-distribution-data',
  GET_MAP_DATA = 'get-map-data',
  GET_INVESTMENT_OPPORTUNITY_DATA = 'get-investment-opportunity-data',

  // External API integration
  FETCH_EXTERNAL_PROPERTIES = 'fetch-external-properties',
  FETCH_MARKET_DATA = 'fetch-market-data',
  FETCH_LOCALITY_INFO = 'fetch-locality-info',
  FETCH_REAL_ESTATE_NEWS = 'fetch-real-estate-news',
  VALIDATE_PROPERTY_DATA = 'validate-property-data',
  SET_EXTERNAL_API_KEY = 'set-external-api-key',

  // Advanced analytics
  GENERATE_PRICE_PREDICTION = 'generate-price-prediction',
  CALCULATE_INVESTMENT_SCORE = 'calculate-investment-score',
  PERFORM_TREND_ANALYSIS = 'perform-trend-analysis',
  GENERATE_MARKET_SENTIMENT = 'generate-market-sentiment',
  OPTIMIZE_PORTFOLIO = 'optimize-portfolio',

  // Document analysis
  ANALYZE_PROPERTY_DOCUMENT = 'analyze-property-document',
  ANALYZE_LEGAL_DOCUMENT = 'analyze-legal-document',
  ANALYZE_MARKET_REPORT = 'analyze-market-report',
  EXTRACT_KEY_INFORMATION = 'extract-key-information',
  VALIDATE_DOCUMENT_AUTHENTICITY = 'validate-document-authenticity',
  COMPARE_DOCUMENTS = 'compare-documents',

  // AI services
  QUERY_GEMINI_AI = 'query-gemini-ai',
  GET_AI_SUGGESTIONS = 'get-ai-suggestions',
  ANALYZE_PROPERTY = 'analyze-property',

  // Settings
  GET_SETTINGS = 'get-settings',
  SAVE_SETTINGS = 'save-settings',
  RESET_SETTINGS = 'reset-settings',

  // Database
  BACKUP_DATABASE = 'backup-database',
  RESTORE_DATABASE = 'restore-database',
  CLEAR_DATABASE = 'clear-database',

  // External APIs
  FETCH_EXTERNAL_DATA = 'fetch-external-data',
  UPDATE_MARKET_DATA = 'update-market-data',
}

/**
 * Application constants
 */
export const APP_CONSTANTS = {
  // Database
  DB_NAME: 'propertyclub_ai.db',
  DB_VERSION: 1,

  // API
  GEMINI_API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
  DEFAULT_MODEL: 'gemini-2.5-flash',
  MAX_TOKENS: 8192,
  TEMPERATURE: 0.7,

  // UI
  SIDEBAR_WIDTH: 280,
  CHAT_MAX_MESSAGES: 1000,
  SEARCH_DEBOUNCE_MS: 300,
  TYPING_INDICATOR_DELAY: 1000,

  // Performance
  MAX_CONCURRENT_REQUESTS: 3,
  REQUEST_TIMEOUT_MS: 30000,
  CACHE_DURATION_MS: 5 * 60 * 1000, // 5 minutes

  // Bangalore specific
  BANGALORE_COORDINATES: {
    lat: 12.9716,
    lng: 77.5946
  },
  SUPPORTED_LANGUAGES: ['en', 'hi', 'kn'],
  DEFAULT_CURRENCY: 'INR',
} as const;

/**
 * Error codes
 */
export enum ErrorCodes {
  // General
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',

  // Authentication
  AUTH_FAILED = 'AUTH_FAILED',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // API
  API_KEY_MISSING = 'API_KEY_MISSING',
  API_KEY_INVALID = 'API_KEY_INVALID',
  API_QUOTA_EXCEEDED = 'API_QUOTA_EXCEEDED',
  API_REQUEST_FAILED = 'API_REQUEST_FAILED',

  // Database
  DB_CONNECTION_FAILED = 'DB_CONNECTION_FAILED',
  DB_QUERY_FAILED = 'DB_QUERY_FAILED',
  DB_MIGRATION_FAILED = 'DB_MIGRATION_FAILED',

  // Validation
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
}

/**
 * Message types for chat
 */
export enum MessageType {
  USER = 'user',
  AI = 'ai',
  SYSTEM = 'system',
  ERROR = 'error',
}

/**
 * Property types
 */
export enum PropertyType {
  APARTMENT = 'apartment',
  VILLA = 'villa',
  PLOT = 'plot',
  COMMERCIAL = 'commercial',
  WAREHOUSE = 'warehouse',
}

/**
 * Transaction types
 */
export enum TransactionType {
  BUY = 'buy',
  RENT = 'rent',
  LEASE = 'lease',
  PG = 'pg',
}
