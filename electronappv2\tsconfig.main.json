{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist/main", "target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "noEmit": false, "declaration": false, "sourceMap": true}, "include": ["src/main/**/*", "src/shared/**/*", "src/database/**/*", "src/services/**/*", "src/utils/**/*", "src/types/**/*"], "exclude": ["src/renderer/**/*", "node_modules", "dist", "out", ".webpack"]}