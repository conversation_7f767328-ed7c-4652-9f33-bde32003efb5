import { PropertyType, TransactionType } from '../shared/constants';
import { marketAnalysisService } from './marketAnalysisService';
import { propertySearchService } from './propertySearchService';
import { knowledgeService } from './knowledgeService';

/**
 * Data Visualization Service
 * Provides data processing and formatting for charts, maps, and other visualizations
 */
class DataVisualizationService {
  /**
   * Initialize the data visualization service
   */
  async initialize(): Promise<void> {
    try {
      console.log('Data visualization service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize data visualization service:', error);
      throw error;
    }
  }

  /**
   * Get price trend chart data
   */
  async getPriceTrendChartData(localities: string[], propertyType?: PropertyType): Promise<any> {
    try {
      const trendData = await marketAnalysisService.getPriceTrends(localities, propertyType);
      
      const datasets = trendData.trends.map((trend: any, index: number) => ({
        label: trend.locality,
        data: trend.priceHistory.map((point: any) => ({
          x: point.date,
          y: point.price
        })),
        borderColor: this.getChartColor(index),
        backgroundColor: this.getChartColor(index, 0.1),
        fill: false,
        tension: 0.1
      }));

      return {
        type: 'line',
        data: {
          datasets
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: `Price Trends - ${propertyType || 'All Property Types'}`
            },
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: 'month'
              },
              title: {
                display: true,
                text: 'Date'
              }
            },
            y: {
              title: {
                display: true,
                text: 'Price per Sq Ft (₹)'
              }
            }
          }
        }
      };
    } catch (error) {
      console.error('Error getting price trend chart data:', error);
      throw error;
    }
  }

  /**
   * Get market analysis chart data
   */
  async getMarketAnalysisChartData(locality: string, propertyType?: PropertyType): Promise<any> {
    try {
      const analysis = await marketAnalysisService.getMarketAnalysis(locality, propertyType);
      
      return {
        priceDistribution: {
          type: 'doughnut',
          data: {
            labels: ['Below Average', 'Average', 'Above Average'],
            datasets: [{
              data: [30, 40, 30], // Mock data - would be calculated from actual properties
              backgroundColor: ['#ef4444', '#f59e0b', '#10b981'],
              borderWidth: 2
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Price Distribution'
              }
            }
          }
        },
        roiProjection: {
          type: 'bar',
          data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
            datasets: [{
              label: 'ROI %',
              data: [
                analysis.investment.roiProjection.annualizedROI,
                analysis.investment.roiProjection.annualizedROI * 1.1,
                analysis.investment.roiProjection.annualizedROI * 1.2,
                analysis.investment.roiProjection.annualizedROI * 1.3,
                analysis.investment.roiProjection.annualizedROI * 1.4
              ],
              backgroundColor: '#3b82f6',
              borderColor: '#1d4ed8',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'ROI Projection'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'ROI (%)'
                }
              }
            }
          }
        },
        marketIndicators: {
          type: 'radar',
          data: {
            labels: ['Price Growth', 'Demand', 'Liquidity', 'Infrastructure', 'Amenities', 'Connectivity'],
            datasets: [{
              label: locality,
              data: [
                analysis.trends.yearOverYearGrowth,
                80, // Mock demand score
                70, // Mock liquidity score
                85, // Mock infrastructure score
                90, // Mock amenities score
                88  // Mock connectivity score
              ],
              borderColor: '#8b5cf6',
              backgroundColor: 'rgba(139, 92, 246, 0.2)',
              pointBackgroundColor: '#8b5cf6',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: '#8b5cf6'
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Market Indicators'
              }
            },
            scales: {
              r: {
                beginAtZero: true,
                max: 100
              }
            }
          }
        }
      };
    } catch (error) {
      console.error('Error getting market analysis chart data:', error);
      throw error;
    }
  }

  /**
   * Get property distribution chart data
   */
  async getPropertyDistributionData(localities: string[]): Promise<any> {
    try {
      const distributionData = await Promise.all(
        localities.map(async locality => {
          const trends = await propertySearchService.getMarketTrends(locality);
          return {
            locality,
            totalProperties: trends.totalProperties,
            averagePrice: trends.averagePrice
          };
        })
      );

      return {
        propertyCount: {
          type: 'bar',
          data: {
            labels: distributionData.map(d => d.locality),
            datasets: [{
              label: 'Total Properties',
              data: distributionData.map(d => d.totalProperties),
              backgroundColor: '#10b981',
              borderColor: '#059669',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Property Count by Locality'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Number of Properties'
                }
              }
            }
          }
        },
        averagePrice: {
          type: 'bar',
          data: {
            labels: distributionData.map(d => d.locality),
            datasets: [{
              label: 'Average Price (₹)',
              data: distributionData.map(d => d.averagePrice),
              backgroundColor: '#f59e0b',
              borderColor: '#d97706',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Average Price by Locality'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Price (₹)'
                }
              }
            }
          }
        }
      };
    } catch (error) {
      console.error('Error getting property distribution data:', error);
      throw error;
    }
  }

  /**
   * Get map data for localities
   */
  async getMapData(localities: string[]): Promise<any> {
    try {
      const mapData = await Promise.all(
        localities.map(async locality => {
          const localityInfo = await knowledgeService.getLocalityInfo(locality);
          const trends = await propertySearchService.getMarketTrends(locality);
          
          // Parse coordinates if available
          let coordinates = null;
          if (localityInfo?.coordinates) {
            const [lat, lng] = localityInfo.coordinates.split(',').map(Number);
            coordinates = { lat, lng };
          }

          return {
            name: locality,
            coordinates,
            averagePrice: trends.averagePricePerSqft,
            totalProperties: trends.totalProperties,
            priceRange: trends.priceRange,
            growth: localityInfo?.trends?.yearOverYearGrowth || 0,
            insights: localityInfo?.insights || {}
          };
        })
      );

      return {
        center: { lat: 12.9716, lng: 77.5946 }, // Bangalore center
        zoom: 11,
        localities: mapData.filter(d => d.coordinates), // Only include localities with coordinates
        heatmapData: mapData
          .filter(d => d.coordinates)
          .map(d => ({
            lat: d.coordinates!.lat,
            lng: d.coordinates!.lng,
            intensity: d.averagePrice / 10000 // Normalize for heatmap
          }))
      };
    } catch (error) {
      console.error('Error getting map data:', error);
      throw error;
    }
  }

  /**
   * Get investment opportunity chart data
   */
  async getInvestmentOpportunityData(criteria: any): Promise<any> {
    try {
      const opportunities = await marketAnalysisService.getInvestmentOpportunities(criteria);
      
      return {
        type: 'scatter',
        data: {
          datasets: [{
            label: 'Investment Opportunities',
            data: opportunities.map(opp => ({
              x: opp.expectedROI,
              y: opp.averagePrice / 100000, // Convert to lakhs for better visualization
              locality: opp.locality,
              riskLevel: opp.riskLevel
            })),
            backgroundColor: opportunities.map(opp => 
              opp.riskLevel === 'low' ? '#10b981' : 
              opp.riskLevel === 'medium' ? '#f59e0b' : '#ef4444'
            ),
            borderColor: opportunities.map(opp => 
              opp.riskLevel === 'low' ? '#059669' : 
              opp.riskLevel === 'medium' ? '#d97706' : '#dc2626'
            ),
            pointRadius: 8,
            pointHoverRadius: 10
          }]
        },
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: 'Investment Opportunities (ROI vs Price)'
            },
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context: any) {
                  const point = context.raw;
                  return `${point.locality}: ROI ${context.parsed.x}%, Price ₹${context.parsed.y}L, Risk: ${point.riskLevel}`;
                }
              }
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: 'Expected ROI (%)'
              }
            },
            y: {
              title: {
                display: true,
                text: 'Average Price (₹ Lakhs)'
              }
            }
          }
        }
      };
    } catch (error) {
      console.error('Error getting investment opportunity data:', error);
      throw error;
    }
  }

  /**
   * Get chart color by index
   */
  private getChartColor(index: number, alpha: number = 1): string {
    const colors = [
      `rgba(59, 130, 246, ${alpha})`,   // Blue
      `rgba(16, 185, 129, ${alpha})`,   // Green
      `rgba(245, 158, 11, ${alpha})`,   // Yellow
      `rgba(239, 68, 68, ${alpha})`,    // Red
      `rgba(139, 92, 246, ${alpha})`,   // Purple
      `rgba(236, 72, 153, ${alpha})`,   // Pink
      `rgba(14, 165, 233, ${alpha})`,   // Sky
      `rgba(34, 197, 94, ${alpha})`     // Emerald
    ];
    return colors[index % colors.length];
  }
}

// Export singleton instance
export const dataVisualizationService = new DataVisualizationService();
