import * as sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import * as path from 'path';
import { app } from 'electron';
import { APP_CONSTANTS } from '@shared/constants';

let db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

/**
 * Initialize the SQLite database
 */
export async function initializeDatabase(): Promise<void> {
  try {
    const dbPath = path.join(app.getPath('userData'), APP_CONSTANTS.DB_NAME);
    
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });

    // Enable foreign keys
    await db.exec('PRAGMA foreign_keys = ON');

    // Create tables
    await createTables();

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Get the database instance
 */
export function getDatabase(): Database<sqlite3.Database, sqlite3.Statement> {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return db;
}

/**
 * Create all necessary tables
 */
async function createTables(): Promise<void> {
  if (!db) return;

  // Users table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE,
      preferences TEXT DEFAULT '{}',
      api_key TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Conversations table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      message TEXT NOT NULL,
      response TEXT,
      message_type TEXT DEFAULT 'user',
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      tokens_used INTEGER DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Saved properties table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS saved_properties (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      property_id TEXT,
      property_data TEXT NOT NULL,
      notes TEXT,
      saved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Bangalore localities table (pre-populated data)
  await db.exec(`
    CREATE TABLE IF NOT EXISTS localities (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      area TEXT,
      zone TEXT,
      pincode TEXT,
      coordinates TEXT,
      description TEXT,
      amenities TEXT,
      connectivity TEXT,
      avg_price_per_sqft REAL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Property searches table (for analytics)
  await db.exec(`
    CREATE TABLE IF NOT EXISTS property_searches (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      search_query TEXT NOT NULL,
      filters TEXT,
      results_count INTEGER DEFAULT 0,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Settings table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      key TEXT NOT NULL,
      value TEXT NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      UNIQUE(user_id, key)
    )
  `);

  // Create indexes for better performance
  await db.exec(`
    CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
    CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp);
    CREATE INDEX IF NOT EXISTS idx_saved_properties_user_id ON saved_properties(user_id);
    CREATE INDEX IF NOT EXISTS idx_localities_name ON localities(name);
    CREATE INDEX IF NOT EXISTS idx_property_searches_user_id ON property_searches(user_id);
    CREATE INDEX IF NOT EXISTS idx_settings_user_key ON settings(user_id, key);
  `);

  console.log('Database tables created successfully');
}

/**
 * Close the database connection
 */
export async function closeDatabase(): Promise<void> {
  if (db) {
    await db.close();
    db = null;
    console.log('Database connection closed');
  }
}
