# PropertyClub AI Environment Configuration

# Development Environment
NODE_ENV=development

# Google Gemini API Configuration
GOOGLE_GEMINI_API_KEY=AIzaSyANtUkmKWZLT4-gHTpBSyKjKwJ-DvrdEsk

# Application Configuration
APP_NAME=PropertyClub AI
APP_VERSION=1.0.0

# Database Configuration
DB_NAME=propertyclub_ai.db

# API Configuration
API_TIMEOUT=30000
MAX_TOKENS=8192
TEMPERATURE=0.7

# Development Settings
ENABLE_DEV_TOOLS=true
ENABLE_HOT_RELOAD=true

# Logging
LOG_LEVEL=info

# Security
ENABLE_CONTEXT_ISOLATION=true
ENABLE_NODE_INTEGRATION=false
