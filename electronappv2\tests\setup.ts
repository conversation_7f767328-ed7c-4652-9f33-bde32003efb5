import '@testing-library/jest-dom';

// Mock electron APIs
const mockElectronAPI = {
  getAppVersion: jest.fn().mockResolvedValue('1.0.0'),
  getAppName: jest.fn().mockResolvedValue('PropertyClub AI'),
  getUserProfile: jest.fn().mockResolvedValue(null),
  saveUserProfile: jest.fn().mockResolvedValue(true),
  sendChatMessage: jest.fn().mockResolvedValue({
    id: '1',
    message: 'Test response',
    timestamp: new Date().toISOString(),
    type: 'ai'
  }),
  getChatHistory: jest.fn().mockResolvedValue([]),
  searchProperties: jest.fn().mockResolvedValue([]),
  getSavedProperties: jest.fn().mockResolvedValue([]),
  getSettings: jest.fn().mockResolvedValue({}),
  saveSettings: jest.fn().mockResolvedValue(true),
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Suppress console warnings in tests
const originalConsoleWarn = console.warn;
console.warn = (...args: any[]) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('React Router')
  ) {
    return;
  }
  originalConsoleWarn.apply(console, args);
};
