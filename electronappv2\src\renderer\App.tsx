import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import Sidebar from './components/Sidebar/Sidebar';
import PropertyClubInterface from './components/Chat/PropertyClubInterface';
import PropertySearch from './components/Property/PropertySearch';
import SavedProperties from './components/Property/SavedProperties';
import Settings from './components/Settings/Settings';
import ErrorBoundary from './components/Common/ErrorBoundary';
import { setAppInfo } from './store/slices/appSlice';

const App: React.FC = () => {
  console.log('App: Component starting to render');

  // Skip loading state for debugging
  const dispatch = useDispatch();

  // Initialize app info immediately
  dispatch(setAppInfo({ name: 'PropertyClub AI', version: '1.0.0' }));

  console.log('App: About to render main app interface');

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex-shrink-0">
          <Sidebar />
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-red-200">
          <div style={{ padding: '20px', backgroundColor: 'yellow' }}>
            <h2>MAIN CONTENT AREA - ROUTING TEST</h2>
            <p>This should be visible if routing works</p>
          </div>
          <Routes>
            <Route path="/" element={<PropertyClubInterface />} />
            <Route path="/chat" element={<PropertyClubInterface />} />
            <Route path="/search" element={<PropertySearch />} />
            <Route path="/saved" element={<SavedProperties />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default App;