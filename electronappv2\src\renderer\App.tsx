import React, { useEffect, useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import Sidebar from './components/Sidebar/Sidebar';
import PropertyClubInterface from './components/Chat/PropertyClubInterface';
import PropertySearch from './components/Property/PropertySearch';
import SavedProperties from './components/Property/SavedProperties';
import Settings from './components/Settings/Settings';
import LoadingScreen from './components/Common/LoadingScreen';
import ErrorBoundary from './components/Common/ErrorBoundary';
import { setAppInfo } from './store/slices/appSlice';

const App: React.FC = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Try to get app information from Electron API, fallback to defaults
        let appName = 'PropertyClub AI';
        let appVersion = '1.0.0';

        try {
          if (window.electronAPI) {
            appName = await window.electronAPI.getAppName();
            appVersion = await window.electronAPI.getAppVersion();
          }
        } catch (apiError) {
          console.warn('Electron API not available, using defaults:', apiError);
        }

        dispatch(setAppInfo({ name: appName, version: appVersion }));

        // Initialize user profile if available
        try {
          if (window.electronAPI) {
            const userProfile = await window.electronAPI.getUserProfile();
            if (userProfile) {
              console.log('User profile loaded:', userProfile);
            }
          }
        } catch (profileError) {
          console.warn('User profile not available:', profileError);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Failed to initialize app:', err);
        setError('Failed to initialize PropertyClub AI. Please restart the application.');
        setIsLoading(false);
      }
    };

    // Small delay to ensure everything is ready
    setTimeout(initializeApp, 500);
  }, [dispatch]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex-shrink-0">
          <Sidebar />
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white">
          <Routes>
            <Route path="/" element={<PropertyClubInterface />} />
            <Route path="/chat" element={<PropertyClubInterface />} />
            <Route path="/search" element={<PropertySearch />} />
            <Route path="/saved" element={<SavedProperties />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default App;