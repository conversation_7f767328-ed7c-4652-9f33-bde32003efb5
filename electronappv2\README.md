# PropertyClub AI

An intelligent real estate assistant desktop application built with Electron, specifically designed for the Bangalore property market. PropertyClub AI integrates with Google Gemini 2.5 Flash to provide real-time, contextually aware answers to real estate queries.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Electron App  │    │  Google Gemini  │    │  External APIs  │
│                 │    │   2.5 Flash     │    │                 │
│  ┌───────────┐  │    │                 │    │  ┌───────────┐  │
│  │    UI     │  │◄──►│  API Gateway    │◄──►│  │Property   │  │
│  │(React/TS) │  │    │                 │    │  │Portal APIs│  │
│  └───────────┘  │    └─────────────────┘    │  └───────────┘  │
│  ┌───────────┐  │                           │  ┌───────────┐  │
│  │Local DB   │  │                           │  │Government │  │
│  │(SQLite)   │  │                           │  │APIs       │  │
│  └───────────┘  │                           │  └───────────┘  │
└─────────────────┘                           └─────────────────┘
```

## 🚀 Features

### Phase 1: Foundation (Current)
- ✅ Modern Electron app structure with TypeScript
- ✅ React 18 with Redux Toolkit for state management
- ✅ Tailwind CSS for styling
- ✅ SQLite database for local storage
- ✅ IPC communication between main and renderer processes
- ✅ Basic chat interface
- ✅ Development environment with hot reload

### Phase 2: Core Features (Planned)
- 🔄 Google Gemini 2.5 Flash API integration
- 🔄 Bangalore-specific knowledge base
- 🔄 Property search functionality
- 🔄 Data visualization components
- 🔄 User authentication and profiles

### Phase 3: Advanced Features (Planned)
- 📋 External API integrations (99acres, MagicBricks)
- 📋 Document analysis capabilities
- 📋 Market intelligence and predictions
- 📋 Investment analysis tools

### Phase 4: Polish & Launch (Planned)
- 📋 Performance optimization
- 📋 Security audit
- 📋 User testing and feedback
- 📋 Distribution setup

## 🛠️ Technology Stack

### Frontend
- **Framework**: Electron with TypeScript
- **UI Library**: React 18
- **State Management**: Redux Toolkit
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Charts**: Recharts

### Backend Integration
- **AI Service**: Google Gemini 2.5 Flash API
- **HTTP Client**: Axios
- **Local Database**: SQLite3

### Development Tools
- **Build Tool**: Webpack + Electron Forge
- **Testing**: Jest + React Testing Library
- **Linting**: ESLint + TypeScript ESLint
- **Formatting**: Prettier
- **Version Control**: Git

## 📁 Project Structure

```
src/
├── main/                 # Main process (Node.js)
│   ├── main.ts          # Application entry point
│   ├── windows/         # Window management
│   ├── ipc/             # IPC handlers
│   └── preload/         # Preload scripts
├── renderer/            # Renderer process (React)
│   ├── components/      # React components
│   ├── store/           # Redux store and slices
│   ├── styles/          # CSS and styling
│   └── App.tsx          # Main React component
├── shared/              # Shared utilities and constants
├── database/            # Database schemas and operations
├── services/            # External API services
├── types/               # TypeScript type definitions
└── utils/               # Utility functions

assets/                  # Static assets
docs/                    # Documentation
tests/                   # Test files
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/jimnanologos/electronapp-v1.git
   cd electronapp-v1/electronappv2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Add your Google Gemini API key
   ```

4. **Build the application**
   ```bash
   npm run build
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Available Scripts

- `npm start` - Build and start the application
- `npm run dev` - Start development with hot reload
- `npm run build` - Build for production
- `npm run test` - Run tests
- `npm run lint` - Run ESLint
- `npm run package` - Package the application
- `npm run make` - Create distributable packages

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:

```env
GOOGLE_GEMINI_API_KEY=your_api_key_here
NODE_ENV=development
```

### Database
The application uses SQLite for local storage. The database is automatically created on first run at:
- **Windows**: `%APPDATA%/PropertyClub AI/propertyclub_ai.db`
- **macOS**: `~/Library/Application Support/PropertyClub AI/propertyclub_ai.db`
- **Linux**: `~/.config/PropertyClub AI/propertyclub_ai.db`

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Generate coverage report:
```bash
npm test -- --coverage
```

## 📦 Building and Distribution

### Development Build
```bash
npm run build
```

### Production Package
```bash
npm run package
```

### Create Installers
```bash
npm run make
```

This will create platform-specific installers in the `out/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Ensure code passes linting and tests

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Google Gemini API for AI capabilities
- Electron community for the framework
- React and Redux teams for the UI framework
- Tailwind CSS for the styling system

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>

---

**PropertyClub AI** - Democratizing access to real estate information in Bangalore through AI-powered assistance.
