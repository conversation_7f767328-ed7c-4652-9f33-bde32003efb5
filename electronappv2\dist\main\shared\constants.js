"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionType = exports.PropertyType = exports.MessageType = exports.ErrorCodes = exports.APP_CONSTANTS = exports.IpcChannels = void 0;
/**
 * IPC Channel names for communication between main and renderer processes
 */
var IpcChannels;
(function (IpcChannels) {
    // App info
    IpcChannels["GET_APP_VERSION"] = "get-app-version";
    IpcChannels["GET_APP_NAME"] = "get-app-name";
    // User management
    IpcChannels["GET_USER_PROFILE"] = "get-user-profile";
    IpcChannels["SAVE_USER_PROFILE"] = "save-user-profile";
    IpcChannels["DELETE_USER_PROFILE"] = "delete-user-profile";
    // Authentication
    IpcChannels["LOGIN_USER"] = "login-user";
    IpcChannels["LOGOUT_USER"] = "logout-user";
    IpcChannels["REGISTER_USER"] = "register-user";
    // Chat functionality
    IpcChannels["SEND_CHAT_MESSAGE"] = "send-chat-message";
    IpcChannels["GET_CHAT_HISTORY"] = "get-chat-history";
    IpcChannels["CLEAR_CHAT_HISTORY"] = "clear-chat-history";
    IpcChannels["DELETE_CHAT_MESSAGE"] = "delete-chat-message";
    // Property search
    IpcChannels["SEARCH_PROPERTIES"] = "search-properties";
    IpcChannels["GET_PROPERTY_DETAILS"] = "get-property-details";
    IpcChannels["GET_PROPERTY_SUGGESTIONS"] = "get-property-suggestions";
    IpcChannels["GET_MARKET_TRENDS"] = "get-market-trends";
    // Saved properties
    IpcChannels["SAVE_PROPERTY"] = "save-property";
    IpcChannels["UNSAVE_PROPERTY"] = "unsave-property";
    IpcChannels["GET_SAVED_PROPERTIES"] = "get-saved-properties";
    // Bangalore localities
    IpcChannels["GET_LOCALITIES"] = "get-localities";
    IpcChannels["GET_LOCALITY_INFO"] = "get-locality-info";
    IpcChannels["GET_LOCALITY_PRICES"] = "get-locality-prices";
    // AI services
    IpcChannels["QUERY_GEMINI_AI"] = "query-gemini-ai";
    IpcChannels["GET_AI_SUGGESTIONS"] = "get-ai-suggestions";
    IpcChannels["ANALYZE_PROPERTY"] = "analyze-property";
    // Settings
    IpcChannels["GET_SETTINGS"] = "get-settings";
    IpcChannels["SAVE_SETTINGS"] = "save-settings";
    IpcChannels["RESET_SETTINGS"] = "reset-settings";
    // Database
    IpcChannels["BACKUP_DATABASE"] = "backup-database";
    IpcChannels["RESTORE_DATABASE"] = "restore-database";
    IpcChannels["CLEAR_DATABASE"] = "clear-database";
    // External APIs
    IpcChannels["FETCH_EXTERNAL_DATA"] = "fetch-external-data";
    IpcChannels["UPDATE_MARKET_DATA"] = "update-market-data";
})(IpcChannels || (exports.IpcChannels = IpcChannels = {}));
/**
 * Application constants
 */
exports.APP_CONSTANTS = {
    // Database
    DB_NAME: 'propertyclub_ai.db',
    DB_VERSION: 1,
    // API
    GEMINI_API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta',
    DEFAULT_MODEL: 'gemini-2.5-flash',
    MAX_TOKENS: 8192,
    TEMPERATURE: 0.7,
    // UI
    SIDEBAR_WIDTH: 280,
    CHAT_MAX_MESSAGES: 1000,
    SEARCH_DEBOUNCE_MS: 300,
    TYPING_INDICATOR_DELAY: 1000,
    // Performance
    MAX_CONCURRENT_REQUESTS: 3,
    REQUEST_TIMEOUT_MS: 30000,
    CACHE_DURATION_MS: 5 * 60 * 1000, // 5 minutes
    // Bangalore specific
    BANGALORE_COORDINATES: {
        lat: 12.9716,
        lng: 77.5946
    },
    SUPPORTED_LANGUAGES: ['en', 'hi', 'kn'],
    DEFAULT_CURRENCY: 'INR',
};
/**
 * Error codes
 */
var ErrorCodes;
(function (ErrorCodes) {
    // General
    ErrorCodes["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
    ErrorCodes["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorCodes["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    // Authentication
    ErrorCodes["AUTH_FAILED"] = "AUTH_FAILED";
    ErrorCodes["USER_NOT_FOUND"] = "USER_NOT_FOUND";
    ErrorCodes["INVALID_CREDENTIALS"] = "INVALID_CREDENTIALS";
    // API
    ErrorCodes["API_KEY_MISSING"] = "API_KEY_MISSING";
    ErrorCodes["API_KEY_INVALID"] = "API_KEY_INVALID";
    ErrorCodes["API_QUOTA_EXCEEDED"] = "API_QUOTA_EXCEEDED";
    ErrorCodes["API_REQUEST_FAILED"] = "API_REQUEST_FAILED";
    // Database
    ErrorCodes["DB_CONNECTION_FAILED"] = "DB_CONNECTION_FAILED";
    ErrorCodes["DB_QUERY_FAILED"] = "DB_QUERY_FAILED";
    ErrorCodes["DB_MIGRATION_FAILED"] = "DB_MIGRATION_FAILED";
    // Validation
    ErrorCodes["INVALID_INPUT"] = "INVALID_INPUT";
    ErrorCodes["MISSING_REQUIRED_FIELD"] = "MISSING_REQUIRED_FIELD";
    ErrorCodes["INVALID_FORMAT"] = "INVALID_FORMAT";
})(ErrorCodes || (exports.ErrorCodes = ErrorCodes = {}));
/**
 * Message types for chat
 */
var MessageType;
(function (MessageType) {
    MessageType["USER"] = "user";
    MessageType["AI"] = "ai";
    MessageType["SYSTEM"] = "system";
    MessageType["ERROR"] = "error";
})(MessageType || (exports.MessageType = MessageType = {}));
/**
 * Property types
 */
var PropertyType;
(function (PropertyType) {
    PropertyType["APARTMENT"] = "apartment";
    PropertyType["VILLA"] = "villa";
    PropertyType["PLOT"] = "plot";
    PropertyType["COMMERCIAL"] = "commercial";
    PropertyType["WAREHOUSE"] = "warehouse";
})(PropertyType || (exports.PropertyType = PropertyType = {}));
/**
 * Transaction types
 */
var TransactionType;
(function (TransactionType) {
    TransactionType["BUY"] = "buy";
    TransactionType["RENT"] = "rent";
    TransactionType["LEASE"] = "lease";
    TransactionType["PG"] = "pg";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
//# sourceMappingURL=constants.js.map