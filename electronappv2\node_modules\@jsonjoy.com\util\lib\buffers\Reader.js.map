{"version": 3, "file": "Reader.js", "sourceRoot": "", "sources": ["../../src/buffers/Reader.ts"], "names": [], "mappings": ";;;AAAA,kDAA6C;AAG7C,MAAa,MAAM;IAAnB;QACS,UAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAC3B,SAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvC,MAAC,GAAG,CAAC,CAAC;IAkGf,CAAC;IAhGQ,KAAK,CAAC,KAAiB;QAC5B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IAEM,IAAI;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAEM,IAAI,CAAC,MAAc;QACxB,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC;IACnB,CAAC;IAEM,GAAG,CAAC,IAAY;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,EAAE;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAE9B,CAAC;IAEM,EAAE;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAEM,GAAG;QAIR,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,GAAG;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,IAAI,CAAC,IAAY;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;QACf,OAAO,IAAA,uBAAU,EAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,MAAc;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;YAAE,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AArGD,wBAqGC"}