{"version": 3, "file": "helpers.dataset.cjs", "sources": ["../../src/helpers/helpers.core.ts", "../../src/helpers/helpers.math.ts", "../../src/helpers/helpers.collection.ts", "../../src/helpers/helpers.extras.ts", "../../src/helpers/helpers.easing.ts", "../../src/helpers/helpers.color.ts", "../../src/core/core.animations.defaults.js", "../../src/core/core.layouts.defaults.js", "../../src/helpers/helpers.intl.ts", "../../src/core/core.ticks.js", "../../src/core/core.scale.defaults.js", "../../src/core/core.defaults.js", "../../src/helpers/helpers.canvas.ts", "../../src/helpers/helpers.options.ts", "../../src/helpers/helpers.config.ts", "../../src/helpers/helpers.curve.ts", "../../src/helpers/helpers.dom.ts", "../../src/helpers/helpers.interpolation.ts", "../../src/helpers/helpers.rtl.ts", "../../src/helpers/helpers.segment.js", "../../src/helpers/helpers.dataset.ts"], "sourcesContent": ["/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || value === undefined;\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n: unknown) {\n  return typeof n === 'symbol' || (typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n));\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\nimport {isNullOrUndef} from './helpers.core.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, vScale, _parsed} = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = (_parsed\n          .slice(0, start + 1)\n          .reverse()\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = (_parsed\n          .slice(end - 1)\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    clip: true,\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas?: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  if (!ctx && !canvas) {\n    return;\n  }\n\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type PrivateChart from '../core/core.controller.js';\nimport type {Chart, ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart | PrivateChart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart | PrivateChart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  (chart as PrivateChart).height = Math.floor(chart.height);\n  (chart as PrivateChart).width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    (chart as PrivateChart).currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n", "import type {Chart, ChartArea, ChartMeta, Scale, TRBL} from '../types/index.js';\n\nfunction getSizeForArea(scale: Scale, chartArea: ChartArea, field: keyof ChartArea) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\n\nfunction getDatasetArea(meta: ChartMeta, chartArea: ChartArea): TRBL {\n  const {xScale, yScale} = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\n\nexport function getDatasetClipArea(chart: Chart, meta: ChartMeta): TRBL | false {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\n"], "names": ["noop", "uid", "id", "isNullOrUndef", "value", "undefined", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNonPrimitive", "n", "Symbol", "toPrimitive", "isNumber", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "isFiniteNumber", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "vScale", "_parsed", "spanGaps", "dataset", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "distanceToDefinedLo", "findIndex", "point", "distanceToDefinedHi", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "Ticks", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer", "getSizeForArea", "chartArea", "field", "getDatasetArea", "getDatasetClipArea", "_clip", "disabled"], "mappings": ";;;;;;;;;;AAAA;;;;IAUO,SAASA,IAAO,GAAA;AACrB,YACD;AAED;;AAEC,IACM,MAAMC,GAAM,GAAC,CAAA,IAAM;AACxB,IAAA,IAAIC,EAAK,GAAA,CAAA,CAAA;AACT,IAAA,OAAO,IAAMA,EAAAA,EAAAA,CAAAA;AACf,CAAA,IAAK;AAEL;;;;AAIC,IACM,SAASC,aAAcC,CAAAA,KAAc,EAA6B;IACvE,OAAOA,KAAAA,KAAU,IAAI,IAAIA,KAAUC,KAAAA,SAAAA,CAAAA;AACrC,CAAC;AAED;;;;AAIC,IACM,SAASC,OAAqBF,CAAAA,KAAc,EAAgB;AACjE,IAAA,IAAIG,MAAMD,OAAO,IAAIC,KAAMD,CAAAA,OAAO,CAACF,KAAQ,CAAA,EAAA;AACzC,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,MAAMI,OAAOC,MAAOC,CAAAA,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAAA,CAAAA,CAAAA;IAC5C,IAAII,IAAAA,CAAKK,KAAK,CAAC,CAAG,EAAA,CAAA,CAAA,KAAO,SAAaL,IAAAA,IAAAA,CAAKK,KAAK,CAAC,CAAC,CAAA,CAAA,KAAO,QAAU,EAAA;AACjE,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;;;AAIC,IACM,SAASC,QAASV,CAAAA,KAAc,EAAsB;IAC3D,OAAOA,KAAAA,KAAU,IAAI,IAAIK,MAAOC,CAAAA,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAW,CAAA,KAAA,iBAAA,CAAA;AACrE,CAAC;AAED;;;IAIA,SAASW,cAAeX,CAAAA,KAAc,EAAmB;IACvD,OAAQ,CAAA,OAAOA,KAAAA,KAAU,YAAYA,KAAiBY,YAAAA,MAAK,KAAMC,QAAAA,CAAS,CAACb,KAAAA,CAAAA,CAAAA;AAC7E,CAAA;AAKA;;;;AAIC,IACM,SAASc,eAAAA,CAAgBd,KAAc,EAAEe,YAAoB,EAAE;IACpE,OAAOJ,cAAAA,CAAeX,KAASA,CAAAA,GAAAA,KAAAA,GAAQe,YAAY,CAAA;AACrD,CAAC;AAED;;;;AAIC,IACM,SAASC,cAAAA,CAAkBhB,KAAoB,EAAEe,YAAe,EAAE;AACvE,IAAA,OAAO,OAAOf,KAAAA,KAAU,WAAce,GAAAA,YAAAA,GAAef,KAAK,CAAA;AAC5D,CAAC;MAEYiB,YAAe,GAAA,CAACjB,OAAwBkB,SACnD,GAAA,OAAOlB,UAAU,QAAYA,IAAAA,KAAAA,CAAMmB,QAAQ,CAAC,OAC1CC,UAAWpB,CAAAA,KAAAA,CAAAA,GAAS,MAClB,CAACA,KAAAA,GAAQkB,UAAU;MAEZG,WAAc,GAAA,CAACrB,OAAwBkB,SAClD,GAAA,OAAOlB,UAAU,QAAYA,IAAAA,KAAAA,CAAMmB,QAAQ,CAAC,OAC1CC,UAAWpB,CAAAA,KAAAA,CAAAA,GAAS,MAAMkB,SACxB,GAAA,CAAClB,MAAM;AAEb;;;;;;IAOO,SAASsB,QACdC,CAAAA,EAAiB,EACjBC,IAAe,EACfC,OAAY,EACG;AACf,IAAA,IAAIF,EAAM,IAAA,OAAOA,EAAGf,CAAAA,IAAI,KAAK,UAAY,EAAA;QACvC,OAAOe,EAAAA,CAAGG,KAAK,CAACD,OAASD,EAAAA,IAAAA,CAAAA,CAAAA;KAC1B;AACH,CAAC;AAuBM,SAASG,KACdC,QAAiC,EACjCL,EAAoC,EACpCE,OAAY,EACZI,OAAiB,EACjB;AACA,IAAA,IAAIC,GAAWC,GAAaC,EAAAA,IAAAA,CAAAA;AAC5B,IAAA,IAAI9B,QAAQ0B,QAAW,CAAA,EAAA;AACrBG,QAAAA,GAAAA,GAAMH,SAASK,MAAM,CAAA;AACrB,QAAA,IAAIJ,OAAS,EAAA;AACX,YAAA,IAAKC,CAAIC,GAAAA,GAAAA,GAAM,CAAGD,EAAAA,CAAAA,IAAK,GAAGA,CAAK,EAAA,CAAA;AAC7BP,gBAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAAAA,EAASG,QAAQ,CAACE,EAAE,EAAEA,CAAAA,CAAAA,CAAAA;AAChC,aAAA;SACK,MAAA;AACL,YAAA,IAAKA,CAAI,GAAA,CAAA,EAAGA,CAAIC,GAAAA,GAAAA,EAAKD,CAAK,EAAA,CAAA;AACxBP,gBAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAAAA,EAASG,QAAQ,CAACE,EAAE,EAAEA,CAAAA,CAAAA,CAAAA;AAChC,aAAA;SACD;KACI,MAAA,IAAIpB,SAASkB,QAAW,CAAA,EAAA;QAC7BI,IAAO3B,GAAAA,MAAAA,CAAO2B,IAAI,CAACJ,QAAAA,CAAAA,CAAAA;AACnBG,QAAAA,GAAAA,GAAMC,KAAKC,MAAM,CAAA;AACjB,QAAA,IAAKH,CAAI,GAAA,CAAA,EAAGA,CAAIC,GAAAA,GAAAA,EAAKD,CAAK,EAAA,CAAA;AACxBP,YAAAA,EAAAA,CAAGf,IAAI,CAACiB,OAASG,EAAAA,QAAQ,CAACI,IAAI,CAACF,CAAAA,CAAE,CAAC,EAAEE,IAAI,CAACF,CAAE,CAAA,CAAA,CAAA;AAC7C,SAAA;KACD;AACH,CAAC;AAED;;;;;AAKC,IACM,SAASI,cAAAA,CAAeC,EAAqB,EAAEC,EAAqB,EAAE;IAC3E,IAAIN,CAAAA,EAAWO,MAAcC,EAAqBC,EAAAA,EAAAA,CAAAA;IAElD,IAAI,CAACJ,MAAM,CAACC,EAAAA,IAAMD,GAAGF,MAAM,KAAKG,EAAGH,CAAAA,MAAM,EAAE;AACzC,QAAA,OAAO,KAAK,CAAA;KACb;IAED,IAAKH,CAAAA,GAAI,GAAGO,IAAOF,GAAAA,EAAAA,CAAGF,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC3CQ,EAAKH,GAAAA,EAAE,CAACL,CAAE,CAAA,CAAA;QACVS,EAAKH,GAAAA,EAAE,CAACN,CAAE,CAAA,CAAA;QAEV,IAAIQ,EAAAA,CAAGE,YAAY,KAAKD,EAAGC,CAAAA,YAAY,IAAIF,EAAAA,CAAGG,KAAK,KAAKF,EAAGE,CAAAA,KAAK,EAAE;AAChE,YAAA,OAAO,KAAK,CAAA;SACb;AACH,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,CAAC;AAED;;;AAGC,IACM,SAASC,KAASC,CAAAA,MAAS,EAAK;AACrC,IAAA,IAAIzC,QAAQyC,MAAS,CAAA,EAAA;QACnB,OAAOA,MAAAA,CAAOC,GAAG,CAACF,KAAAA,CAAAA,CAAAA;KACnB;AAED,IAAA,IAAIhC,SAASiC,MAAS,CAAA,EAAA;AACpB,QAAA,MAAME,MAASxC,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,CAAA,CAAA;QACjC,MAAMd,IAAAA,GAAO3B,MAAO2B,CAAAA,IAAI,CAACW,MAAAA,CAAAA,CAAAA;QACzB,MAAMI,IAAAA,GAAOf,KAAKC,MAAM,CAAA;AACxB,QAAA,IAAIe,CAAI,GAAA,CAAA,CAAA;QAER,MAAOA,CAAAA,GAAID,IAAM,EAAA,EAAEC,CAAG,CAAA;AACpBH,YAAAA,MAAM,CAACb,IAAI,CAACgB,CAAAA,CAAE,CAAC,GAAGN,KAAMC,CAAAA,MAAM,CAACX,IAAI,CAACgB,CAAAA,CAAE,CAAC,CAAA,CAAA;AACzC,SAAA;QAEA,OAAOH,MAAAA,CAAAA;KACR;IAED,OAAOF,MAAAA,CAAAA;AACT,CAAC;AAED,SAASM,UAAAA,CAAWC,GAAW,EAAE;IAC/B,OAAO;AAAC,QAAA,WAAA;AAAa,QAAA,WAAA;AAAa,QAAA,aAAA;KAAc,CAACC,OAAO,CAACD,GAAAA,CAAAA,KAAS,CAAC,CAAA,CAAA;AACrE,CAAA;AAEA;;;;IAKO,SAASE,OAAAA,CAAQF,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAEU,OAAkB,EAAE;IAC7F,IAAI,CAACJ,WAAWC,GAAM,CAAA,EAAA;AACpB,QAAA,OAAA;KACD;IAED,MAAMI,IAAAA,GAAOT,MAAM,CAACK,GAAI,CAAA,CAAA;IACxB,MAAMK,IAAAA,GAAOZ,MAAM,CAACO,GAAI,CAAA,CAAA;IAExB,IAAIxC,QAAAA,CAAS4C,IAAS5C,CAAAA,IAAAA,QAAAA,CAAS6C,IAAO,CAAA,EAAA;;AAEpCC,QAAAA,KAAAA,CAAMF,MAAMC,IAAMF,EAAAA,OAAAA,CAAAA,CAAAA;KACb,MAAA;QACLR,MAAM,CAACK,GAAI,CAAA,GAAGR,KAAMa,CAAAA,IAAAA,CAAAA,CAAAA;KACrB;AACH,CAAC;AA0BM,SAASC,KAASX,CAAAA,MAAS,EAAEF,MAAmB,EAAEU,OAAsB,EAAa;IAC1F,MAAMI,OAAAA,GAAUvD,OAAQyC,CAAAA,MAAAA,CAAAA,GAAUA,MAAS,GAAA;AAACA,QAAAA,MAAAA;AAAO,KAAA,CAAA;IACnD,MAAMN,IAAAA,GAAOoB,QAAQxB,MAAM,CAAA;IAE3B,IAAI,CAACvB,SAASmC,MAAS,CAAA,EAAA;QACrB,OAAOA,MAAAA,CAAAA;KACR;AAEDQ,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;IACtB,MAAMK,MAAAA,GAASL,OAAQK,CAAAA,MAAM,IAAIN,OAAAA,CAAAA;IACjC,IAAIO,OAAAA,CAAAA;AAEJ,IAAA,IAAK,IAAI7B,CAAI,GAAA,CAAA,EAAGA,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;QAC7B6B,OAAUF,GAAAA,OAAO,CAAC3B,CAAE,CAAA,CAAA;QACpB,IAAI,CAACpB,SAASiD,OAAU,CAAA,EAAA;YACtB,SAAS;SACV;QAED,MAAM3B,IAAAA,GAAO3B,MAAO2B,CAAAA,IAAI,CAAC2B,OAAAA,CAAAA,CAAAA;QACzB,IAAK,IAAIX,CAAI,GAAA,CAAA,EAAGD,IAAOf,GAAAA,IAAAA,CAAKC,MAAM,EAAEe,CAAAA,GAAID,IAAM,EAAA,EAAEC,CAAG,CAAA;AACjDU,YAAAA,MAAAA,CAAO1B,IAAI,CAACgB,CAAE,CAAA,EAAEH,QAAQc,OAASN,EAAAA,OAAAA,CAAAA,CAAAA;AACnC,SAAA;AACF,KAAA;IAEA,OAAOR,MAAAA,CAAAA;AACT,CAAC;AAgBM,SAASe,OAAAA,CAAWf,MAAS,EAAEF,MAAmB,EAAa;;IAEpE,OAAOa,KAAAA,CAASX,QAAQF,MAAQ,EAAA;QAACe,MAAQG,EAAAA,SAAAA;AAAS,KAAA,CAAA,CAAA;AACpD,CAAC;AAED;;;IAIO,SAASA,SAAUX,CAAAA,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAE;IAC3E,IAAI,CAACM,WAAWC,GAAM,CAAA,EAAA;AACpB,QAAA,OAAA;KACD;IAED,MAAMI,IAAAA,GAAOT,MAAM,CAACK,GAAI,CAAA,CAAA;IACxB,MAAMK,IAAAA,GAAOZ,MAAM,CAACO,GAAI,CAAA,CAAA;IAExB,IAAIxC,QAAAA,CAAS4C,IAAS5C,CAAAA,IAAAA,QAAAA,CAAS6C,IAAO,CAAA,EAAA;AACpCK,QAAAA,OAAAA,CAAQN,IAAMC,EAAAA,IAAAA,CAAAA,CAAAA;KACT,MAAA,IAAI,CAAClD,MAAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAAA,EAAQK,GAAM,CAAA,EAAA;QAC7DL,MAAM,CAACK,GAAI,CAAA,GAAGR,KAAMa,CAAAA,IAAAA,CAAAA,CAAAA;KACrB;AACH,CAAC;AAED;;IAGO,SAASQ,WAAAA,CAAYC,KAAa,EAAEhE,KAAc,EAAEiE,QAAgB,EAAEN,OAAe,EAAE;AAC5F,IAAA,IAAI3D,UAAUC,SAAW,EAAA;AACvBiE,QAAAA,OAAAA,CAAQC,IAAI,CAACH,KAAAA,GAAQ,KAAQC,GAAAA,QAAAA,GAC3B,kCAAkCN,OAAU,GAAA,WAAA,CAAA,CAAA;KAC/C;AACH,CAAC;AAED;AACA,MAAMS,YAAe,GAAA;;AAEnB,IAAA,EAAA,EAAIC,CAAAA,CAAKA,GAAAA,CAAAA;;IAETC,CAAGC,EAAAA,CAAAA,CAAKA,GAAAA,CAAAA,CAAED,CAAC;IACXE,CAAGD,EAAAA,CAAAA,CAAKA,GAAAA,CAAAA,CAAEC,CAAC;AACb,CAAA,CAAA;AAEA;;AAEC,IACM,SAASC,SAAUvB,CAAAA,GAAW,EAAE;IACrC,MAAMwB,KAAAA,GAAQxB,GAAIyB,CAAAA,KAAK,CAAC,GAAA,CAAA,CAAA;AACxB,IAAA,MAAM3C,OAAiB,EAAE,CAAA;AACzB,IAAA,IAAI4C,GAAM,GAAA,EAAA,CAAA;IACV,KAAK,MAAMC,QAAQH,KAAO,CAAA;QACxBE,GAAOC,IAAAA,IAAAA,CAAAA;QACP,IAAID,GAAAA,CAAIzD,QAAQ,CAAC,IAAO,CAAA,EAAA;AACtByD,YAAAA,GAAAA,GAAMA,GAAInE,CAAAA,KAAK,CAAC,CAAA,EAAG,CAAC,CAAK,CAAA,GAAA,GAAA,CAAA;SACpB,MAAA;AACLuB,YAAAA,IAAAA,CAAK8C,IAAI,CAACF,GAAAA,CAAAA,CAAAA;YACVA,GAAM,GAAA,EAAA,CAAA;SACP;AACH,KAAA;IACA,OAAO5C,IAAAA,CAAAA;AACT,CAAC;AAED,SAAS+C,eAAAA,CAAgB7B,GAAW,EAAE;AACpC,IAAA,MAAMlB,OAAOyC,SAAUvB,CAAAA,GAAAA,CAAAA,CAAAA;AACvB,IAAA,OAAO8B,CAAAA,GAAO,GAAA;QACZ,KAAK,MAAMhC,KAAKhB,IAAM,CAAA;AACpB,YAAA,IAAIgB,MAAM,EAAI,EAAA;gBAGZ,MAAM;aACP;YACDgC,GAAMA,GAAAA,GAAAA,IAAOA,GAAG,CAAChC,CAAE,CAAA,CAAA;AACrB,SAAA;QACA,OAAOgC,GAAAA,CAAAA;AACT,KAAA,CAAA;AACF,CAAA;AAEO,SAASC,gBAAAA,CAAiBD,GAAc,EAAE9B,GAAW,EAAO;IACjE,MAAMgC,QAAAA,GAAWd,YAAY,CAAClB,GAAI,CAAA,KAAKkB,YAAY,CAAClB,GAAAA,CAAI,GAAG6B,eAAAA,CAAgB7B,GAAG,CAAA,CAAA,CAAA;AAC9E,IAAA,OAAOgC,QAASF,CAAAA,GAAAA,CAAAA,CAAAA;AAClB,CAAC;AAED;;AAEC,IACM,SAASG,WAAYC,CAAAA,GAAW,EAAE;IACvC,OAAOA,GAAAA,CAAIC,MAAM,CAAC,CAAA,CAAA,CAAGC,WAAW,EAAKF,GAAAA,GAAAA,CAAI3E,KAAK,CAAC,CAAA,CAAA,CAAA;AACjD,CAAC;MAGY8E,OAAU,GAAA,CAACvF,KAAmB,GAAA,OAAOA,UAAU,YAAY;MAE3DwF,UAAa,GAAA,CAACxF,KAAqD,GAAA,OAAOA,UAAU,WAAW;AAE5G;AACayF,MAAAA,SAAAA,GAAY,CAAIC,CAAAA,EAAWC,CAAc,GAAA;AACpD,IAAA,IAAID,CAAEE,CAAAA,IAAI,KAAKD,CAAAA,CAAEC,IAAI,EAAE;AACrB,QAAA,OAAO,KAAK,CAAA;KACb;IAED,KAAK,MAAMC,QAAQH,CAAG,CAAA;AACpB,QAAA,IAAI,CAACC,CAAAA,CAAEG,GAAG,CAACD,IAAO,CAAA,EAAA;AAChB,YAAA,OAAO,KAAK,CAAA;SACb;AACH,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,EAAE;AAEF;;;AAGC,IACM,SAASE,aAAcC,CAAAA,CAAa,EAAE;IAC3C,OAAOA,CAAAA,CAAE5F,IAAI,KAAK,SAAa4F,IAAAA,CAAAA,CAAE5F,IAAI,KAAK,OAAA,IAAW4F,CAAE5F,CAAAA,IAAI,KAAK,aAAA,CAAA;AAClE;;AC5ZA;;;AAGC,IAEM,MAAM6F,EAAKC,GAAAA,IAAAA,CAAKD,GAAG;AACnB,MAAME,GAAM,GAAA,CAAA,GAAIF,GAAG;AACnB,MAAMG,KAAQD,GAAAA,GAAAA,GAAMF,GAAG;AACjBI,MAAAA,QAAAA,GAAWzF,MAAO0F,CAAAA,kBAAkB;AAC1C,MAAMC,WAAcN,GAAAA,EAAAA,GAAK,IAAI;AAC7B,MAAMO,OAAUP,GAAAA,EAAAA,GAAK,EAAE;AACvB,MAAMQ,UAAaR,GAAAA,EAAAA,GAAK,EAAE;AACpBS,MAAAA,aAAAA,GAAgBT,EAAK,GAAA,CAAA,GAAI,EAAE;AAE3BU,MAAAA,KAAAA,GAAQT,IAAKS,CAAAA,MAAM;AACnBC,MAAAA,IAAAA,GAAOV,IAAKU,CAAAA,KAAK;AAEvB,SAASC,YAAavC,CAAAA,CAAS,EAAEE,CAAS,EAAEsC,OAAe,EAAE;AAClE,IAAA,OAAOZ,IAAKa,CAAAA,GAAG,CAACzC,CAAAA,GAAIE,CAAKsC,CAAAA,GAAAA,OAAAA,CAAAA;AAC3B,CAAC;AAED;;AAEC,IACM,SAASE,OAAQC,CAAAA,KAAa,EAAE;IACrC,MAAMC,YAAAA,GAAehB,IAAKiB,CAAAA,KAAK,CAACF,KAAAA,CAAAA,CAAAA;AAChCA,IAAAA,KAAAA,GAAQJ,aAAaI,KAAOC,EAAAA,YAAAA,EAAcD,KAAQ,GAAA,IAAA,CAAA,GAAQC,eAAeD,KAAK,CAAA;IAC9E,MAAMG,SAAAA,GAAYlB,KAAKmB,GAAG,CAAC,IAAInB,IAAKoB,CAAAA,KAAK,CAACX,KAAMM,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAChD,IAAA,MAAMM,WAAWN,KAAQG,GAAAA,SAAAA,CAAAA;IACzB,MAAMI,YAAAA,GAAeD,QAAY,IAAA,CAAA,GAAI,CAAIA,GAAAA,QAAAA,IAAY,IAAI,CAAIA,GAAAA,QAAAA,IAAY,CAAI,GAAA,CAAA,GAAI,EAAE,CAAA;AACnF,IAAA,OAAOC,YAAeJ,GAAAA,SAAAA,CAAAA;AACxB,CAAC;AAED;;;AAGC,IACM,SAASK,UAAWzH,CAAAA,KAAa,EAAE;AACxC,IAAA,MAAM0H,SAAmB,EAAE,CAAA;IAC3B,MAAMC,IAAAA,GAAOzB,IAAKyB,CAAAA,IAAI,CAAC3H,KAAAA,CAAAA,CAAAA;IACvB,IAAI8B,CAAAA,CAAAA;AAEJ,IAAA,IAAKA,CAAI,GAAA,CAAA,EAAGA,CAAI6F,GAAAA,IAAAA,EAAM7F,CAAK,EAAA,CAAA;QACzB,IAAI9B,KAAAA,GAAQ8B,MAAM,CAAG,EAAA;AACnB4F,YAAAA,MAAAA,CAAO5C,IAAI,CAAChD,CAAAA,CAAAA,CAAAA;YACZ4F,MAAO5C,CAAAA,IAAI,CAAC9E,KAAQ8B,GAAAA,CAAAA,CAAAA,CAAAA;SACrB;AACH,KAAA;AACA,IAAA,IAAI6F,IAAUA,MAAAA,IAAO,GAAA,CAAA,CAAI,EAAA;AACvBD,QAAAA,MAAAA,CAAO5C,IAAI,CAAC6C,IAAAA,CAAAA,CAAAA;KACb;AAEDD,IAAAA,MAAAA,CAAOE,IAAI,CAAC,CAAClC,GAAGC,CAAMD,GAAAA,CAAAA,GAAIC,GAAGkC,GAAG,EAAA,CAAA;IAChC,OAAOH,MAAAA,CAAAA;AACT,CAAC;AAED;;IAGA,SAASI,cAAeC,CAAAA,CAAU,EAAE;AAClC,IAAA,OAAO,OAAOA,CAAM,KAAA,QAAA,IAAa,OAAOA,CAAM,KAAA,QAAA,IAAYA,MAAM,IAAI,IAAI,EAAEC,OAAOC,WAAW,IAAIF,KAAK,UAAcA,IAAAA,CAAAA,IAAK,aAAaA,CAAAA,CAAAA,CAAAA;AACvI,CAAA;AAEO,SAASG,QAASH,CAAAA,CAAU,EAAe;AAChD,IAAA,OAAO,CAACD,cAAeC,CAAAA,CAAAA,CAAAA,IAAM,CAACI,KAAM/G,CAAAA,UAAAA,CAAW2G,OAAiBlH,QAASkH,CAAAA,CAAAA,CAAAA,CAAAA;AAC3E,CAAC;AAEM,SAASK,WAAAA,CAAY9D,CAAS,EAAEwC,OAAe,EAAE;IACtD,MAAMuB,OAAAA,GAAUnC,IAAKiB,CAAAA,KAAK,CAAC7C,CAAAA,CAAAA,CAAAA;AAC3B,IAAA,OAAO,OAAYwC,GAAAA,OAAAA,IAAYxC,CAAO,IAAC+D,UAAUvB,OAAYxC,IAAAA,CAAAA,CAAAA;AAC/D,CAAC;AAED;;IAGO,SAASgE,kBACdC,CAAAA,KAA+B,EAC/B1F,MAAoC,EACpC2F,QAAgB,EAChB;AACA,IAAA,IAAI1G,GAAWO,IAAcrC,EAAAA,KAAAA,CAAAA;IAE7B,IAAK8B,CAAAA,GAAI,GAAGO,IAAOkG,GAAAA,KAAAA,CAAMtG,MAAM,EAAEH,CAAAA,GAAIO,MAAMP,CAAK,EAAA,CAAA;AAC9C9B,QAAAA,KAAAA,GAAQuI,KAAK,CAACzG,CAAE,CAAA,CAAC0G,QAAS,CAAA,CAAA;QAC1B,IAAI,CAACL,MAAMnI,KAAQ,CAAA,EAAA;AACjB6C,YAAAA,MAAAA,CAAO4F,GAAG,GAAGvC,IAAAA,CAAKuC,GAAG,CAAC5F,MAAAA,CAAO4F,GAAG,EAAEzI,KAAAA,CAAAA,CAAAA;AAClC6C,YAAAA,MAAAA,CAAO6F,GAAG,GAAGxC,IAAAA,CAAKwC,GAAG,CAAC7F,MAAAA,CAAO6F,GAAG,EAAE1I,KAAAA,CAAAA,CAAAA;SACnC;AACH,KAAA;AACF,CAAC;AAEM,SAAS2I,SAAUC,CAAAA,OAAe,EAAE;IACzC,OAAOA,OAAAA,IAAW3C,EAAAA,GAAK,GAAE,CAAA,CAAA;AAC3B,CAAC;AAEM,SAAS4C,SAAUC,CAAAA,OAAe,EAAE;IACzC,OAAOA,OAAAA,IAAW,GAAA,GAAM7C,EAAC,CAAA,CAAA;AAC3B,CAAC;AAED;;;;;;AAMC,IACM,SAAS8C,cAAezE,CAAAA,CAAS,EAAE;IACxC,IAAI,CAAC0E,eAAe1E,CAAI,CAAA,EAAA;AACtB,QAAA,OAAA;KACD;AACD,IAAA,IAAI0B,CAAI,GAAA,CAAA,CAAA;AACR,IAAA,IAAIiD,CAAI,GAAA,CAAA,CAAA;AACR,IAAA,MAAO/C,KAAKiB,KAAK,CAAC7C,CAAI0B,GAAAA,CAAAA,CAAAA,GAAKA,MAAM1B,CAAG,CAAA;QAClC0B,CAAK,IAAA,EAAA,CAAA;AACLiD,QAAAA,CAAAA,EAAAA,CAAAA;AACF,KAAA;IACA,OAAOA,CAAAA,CAAAA;AACT,CAAC;AAED;AACO,SAASC,iBAAAA,CACdC,WAAkB,EAClBC,UAAiB,EACjB;AACA,IAAA,MAAMC,mBAAsBD,GAAAA,UAAAA,CAAW9E,CAAC,GAAG6E,YAAY7E,CAAC,CAAA;AACxD,IAAA,MAAMgF,mBAAsBF,GAAAA,UAAAA,CAAW5E,CAAC,GAAG2E,YAAY3E,CAAC,CAAA;AACxD,IAAA,MAAM+E,2BAA2BrD,IAAKyB,CAAAA,IAAI,CAAC0B,mBAAAA,GAAsBA,sBAAsBC,mBAAsBA,GAAAA,mBAAAA,CAAAA,CAAAA;AAE7G,IAAA,IAAIE,KAAQtD,GAAAA,IAAAA,CAAKuD,KAAK,CAACH,mBAAqBD,EAAAA,mBAAAA,CAAAA,CAAAA;IAE5C,IAAIG,KAAAA,GAAS,CAAC,GAAA,GAAMvD,EAAK,EAAA;AACvBuD,QAAAA,KAAAA,IAASrD;KACV;IAED,OAAO;AACLqD,QAAAA,KAAAA;QACAE,QAAUH,EAAAA,wBAAAA;AACZ,KAAA,CAAA;AACF,CAAC;AAEM,SAASI,qBAAAA,CAAsBC,GAAU,EAAEC,GAAU,EAAE;IAC5D,OAAO3D,IAAAA,CAAKyB,IAAI,CAACzB,IAAAA,CAAKmB,GAAG,CAACwC,GAAAA,CAAIvF,CAAC,GAAGsF,GAAAA,CAAItF,CAAC,EAAE,CAAA,CAAA,GAAK4B,KAAKmB,GAAG,CAACwC,IAAIrF,CAAC,GAAGoF,GAAIpF,CAAAA,CAAC,EAAE,CAAA,CAAA,CAAA,CAAA;AACxE,CAAC;AAED;;;AAGC,IACM,SAASsF,UAAAA,CAAWpE,CAAS,EAAEC,CAAS,EAAE;AAC/C,IAAA,OAAO,CAACD,CAAAA,GAAIC,CAAIS,GAAAA,KAAI,IAAKD,GAAMF,GAAAA,EAAAA,CAAAA;AACjC,CAAC;AAED;;;AAGC,IACM,SAAS8D,eAAgBrE,CAAAA,CAAS,EAAE;AACzC,IAAA,OAAO,CAACA,CAAIS,GAAAA,GAAAA,GAAMA,GAAE,IAAKA,GAAAA,CAAAA;AAC3B,CAAC;AAED;;IAGO,SAAS6D,aAAAA,CAAcR,KAAa,EAAES,KAAa,EAAEC,GAAW,EAAEC,qBAA+B,EAAE;AACxG,IAAA,MAAMzE,IAAIqE,eAAgBP,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAMY,IAAIL,eAAgBE,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAMjE,IAAI+D,eAAgBG,CAAAA,GAAAA,CAAAA,CAAAA;IAC1B,MAAMG,YAAAA,GAAeN,gBAAgBK,CAAI1E,GAAAA,CAAAA,CAAAA,CAAAA;IACzC,MAAM4E,UAAAA,GAAaP,gBAAgB/D,CAAIN,GAAAA,CAAAA,CAAAA,CAAAA;IACvC,MAAM6E,YAAAA,GAAeR,gBAAgBrE,CAAI0E,GAAAA,CAAAA,CAAAA,CAAAA;IACzC,MAAMI,UAAAA,GAAaT,gBAAgBrE,CAAIM,GAAAA,CAAAA,CAAAA,CAAAA;IACvC,OAAON,CAAAA,KAAM0E,KAAK1E,CAAMM,KAAAA,CAAAA,IAAMmE,yBAAyBC,CAAMpE,KAAAA,CAAAA,IACvDqE,YAAeC,GAAAA,UAAAA,IAAcC,YAAeC,GAAAA,UAAAA,CAAAA;AACpD,CAAC;AAED;;;;;;IAOO,SAASC,WAAYzK,CAAAA,KAAa,EAAEyI,GAAW,EAAEC,GAAW,EAAE;AACnE,IAAA,OAAOxC,KAAKwC,GAAG,CAACD,KAAKvC,IAAKuC,CAAAA,GAAG,CAACC,GAAK1I,EAAAA,KAAAA,CAAAA,CAAAA,CAAAA;AACrC,CAAC;AAED;;;AAGC,IACM,SAAS0K,WAAY1K,CAAAA,KAAa,EAAE;IACzC,OAAOyK,WAAAA,CAAYzK,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,CAAA,CAAA;AACpC,CAAC;AAED;;;;;;IAOO,SAAS2K,UAAAA,CAAW3K,KAAa,EAAEiK,KAAa,EAAEC,GAAW,EAAEpD,OAAU,GAAA,IAAI,EAAE;AACpF,IAAA,OAAO9G,KAASkG,IAAAA,IAAAA,CAAKuC,GAAG,CAACwB,KAAOC,EAAAA,GAAAA,CAAAA,GAAOpD,OAAW9G,IAAAA,KAAAA,IAASkG,IAAKwC,CAAAA,GAAG,CAACuB,KAAAA,EAAOC,GAAOpD,CAAAA,GAAAA,OAAAA,CAAAA;AACpF;;AC3LO,SAAS8D,OACdC,CAAAA,KAAgB,EAChB7K,KAAa,EACb8K,GAAgC,EAChC;IACAA,GAAMA,GAAAA,GAAAA,KAAQ,CAACrI,KAAAA,GAAUoI,KAAK,CAACpI,KAAAA,CAAM,GAAGzC,KAAI,CAAA,CAAA;IAC5C,IAAI+K,EAAAA,GAAKF,KAAM5I,CAAAA,MAAM,GAAG,CAAA,CAAA;AACxB,IAAA,IAAI+I,EAAK,GAAA,CAAA,CAAA;IACT,IAAIC,GAAAA,CAAAA;IAEJ,MAAOF,EAAAA,GAAKC,KAAK,CAAG,CAAA;QAClBC,GAAM,GAACD,KAAKD,EAAO,IAAA,CAAA,CAAA;AACnB,QAAA,IAAID,IAAIG,GAAM,CAAA,EAAA;YACZD,EAAKC,GAAAA,GAAAA,CAAAA;SACA,MAAA;YACLF,EAAKE,GAAAA,GAAAA,CAAAA;SACN;AACH,KAAA;IAEA,OAAO;AAACD,QAAAA,EAAAA;AAAID,QAAAA,EAAAA;AAAE,KAAA,CAAA;AAChB,CAAC;AAED;;;;;;;AAOC,IACM,MAAMG,YAAe,GAAA,CAC1BL,KACA3H,EAAAA,GAAAA,EACAlD,KACAmL,EAAAA,IAAAA,GAEAP,OAAQC,CAAAA,KAAAA,EAAO7K,KAAOmL,EAAAA,IAAAA,GAClB1I,CAAAA,KAAS,GAAA;AACT,QAAA,MAAM2I,EAAKP,GAAAA,KAAK,CAACpI,KAAAA,CAAM,CAACS,GAAI,CAAA,CAAA;QAC5B,OAAOkI,EAAAA,GAAKpL,KAASoL,IAAAA,EAAAA,KAAOpL,KAAS6K,IAAAA,KAAK,CAACpI,KAAQ,GAAA,CAAA,CAAE,CAACS,GAAAA,CAAI,KAAKlD,KAAAA,CAAAA;KAE/DyC,GAAAA,CAAAA,QAASoI,KAAK,CAACpI,MAAM,CAACS,GAAAA,CAAI,GAAGlD,KAAK,EAAE;AAE1C;;;;;;AAMC,IACYqL,MAAAA,aAAAA,GAAgB,CAC3BR,KACA3H,EAAAA,GAAAA,EACAlD,QAEA4K,OAAQC,CAAAA,KAAAA,EAAO7K,KAAOyC,EAAAA,CAAAA,QAASoI,KAAK,CAACpI,MAAM,CAACS,GAAAA,CAAI,IAAIlD,KAAO,EAAA;AAE7D;;;;;;IAOO,SAASsL,cAAeC,CAAAA,MAAgB,EAAE9C,GAAW,EAAEC,GAAW,EAAE;AACzE,IAAA,IAAIuB,KAAQ,GAAA,CAAA,CAAA;IACZ,IAAIC,GAAAA,GAAMqB,OAAOtJ,MAAM,CAAA;AAEvB,IAAA,MAAOgI,QAAQC,GAAOqB,IAAAA,MAAM,CAACtB,KAAAA,CAAM,GAAGxB,GAAK,CAAA;AACzCwB,QAAAA,KAAAA,EAAAA,CAAAA;AACF,KAAA;AACA,IAAA,MAAOC,MAAMD,KAASsB,IAAAA,MAAM,CAACrB,GAAM,GAAA,CAAA,CAAE,GAAGxB,GAAK,CAAA;AAC3CwB,QAAAA,GAAAA,EAAAA,CAAAA;AACF,KAAA;IAEA,OAAOD,KAAAA,GAAQ,CAAKC,IAAAA,GAAAA,GAAMqB,MAAOtJ,CAAAA,MAAM,GACnCsJ,MAAAA,CAAO9K,KAAK,CAACwJ,KAAOC,EAAAA,GAAAA,CAAAA,GACpBqB,MAAM,CAAA;AACZ,CAAC;AAED,MAAMC,WAAc,GAAA;AAAC,IAAA,MAAA;AAAQ,IAAA,KAAA;AAAO,IAAA,OAAA;AAAS,IAAA,QAAA;AAAU,IAAA,SAAA;AAAU,CAAA,CAAA;AAgB1D,SAASC,iBAAAA,CAAkBlD,KAAK,EAAEmD,QAAQ,EAAE;IACjD,IAAInD,KAAAA,CAAMoD,QAAQ,EAAE;AAClBpD,QAAAA,KAAAA,CAAMoD,QAAQ,CAACC,SAAS,CAAC9G,IAAI,CAAC4G,QAAAA,CAAAA,CAAAA;AAC9B,QAAA,OAAA;KACD;IAEDrL,MAAOwL,CAAAA,cAAc,CAACtD,KAAAA,EAAO,UAAY,EAAA;AACvCuD,QAAAA,YAAAA,EAAc,IAAI;AAClBC,QAAAA,UAAAA,EAAY,KAAK;QACjB/L,KAAO,EAAA;YACL4L,SAAW,EAAA;AAACF,gBAAAA,QAAAA;AAAS,aAAA;AACvB,SAAA;AACF,KAAA,CAAA,CAAA;IAEAF,WAAYQ,CAAAA,OAAO,CAAC,CAAC9I,GAAQ,GAAA;QAC3B,MAAM+I,MAAAA,GAAS,YAAY9G,WAAYjC,CAAAA,GAAAA,CAAAA,CAAAA;QACvC,MAAMgJ,IAAAA,GAAO3D,KAAK,CAACrF,GAAI,CAAA,CAAA;QAEvB7C,MAAOwL,CAAAA,cAAc,CAACtD,KAAAA,EAAOrF,GAAK,EAAA;AAChC4I,YAAAA,YAAAA,EAAc,IAAI;AAClBC,YAAAA,UAAAA,EAAY,KAAK;YACjB/L,KAAM,CAAA,CAAA,GAAGwB,IAAI,EAAE;AACb,gBAAA,MAAM2K,GAAMD,GAAAA,IAAAA,CAAKxK,KAAK,CAAC,IAAI,EAAEF,IAAAA,CAAAA,CAAAA;AAE7B+G,gBAAAA,KAAAA,CAAMoD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAC,CAACI,MAAW,GAAA;AAC3C,oBAAA,IAAI,OAAOA,MAAM,CAACH,MAAAA,CAAO,KAAK,UAAY,EAAA;wBACxCG,MAAM,CAACH,OAAO,CAAIzK,GAAAA,IAAAA,CAAAA,CAAAA;qBACnB;AACH,iBAAA,CAAA,CAAA;gBAEA,OAAO2K,GAAAA,CAAAA;AACT,aAAA;AACF,SAAA,CAAA,CAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAQM,SAASE,mBAAAA,CAAoB9D,KAAK,EAAEmD,QAAQ,EAAE;IACnD,MAAMY,IAAAA,GAAO/D,MAAMoD,QAAQ,CAAA;AAC3B,IAAA,IAAI,CAACW,IAAM,EAAA;AACT,QAAA,OAAA;KACD;IAED,MAAMV,SAAAA,GAAYU,KAAKV,SAAS,CAAA;IAChC,MAAMnJ,KAAAA,GAAQmJ,SAAUzI,CAAAA,OAAO,CAACuI,QAAAA,CAAAA,CAAAA;IAChC,IAAIjJ,KAAAA,KAAU,CAAC,CAAG,EAAA;QAChBmJ,SAAUW,CAAAA,MAAM,CAAC9J,KAAO,EAAA,CAAA,CAAA,CAAA;KACzB;IAED,IAAImJ,SAAAA,CAAU3J,MAAM,GAAG,CAAG,EAAA;AACxB,QAAA,OAAA;KACD;IAEDuJ,WAAYQ,CAAAA,OAAO,CAAC,CAAC9I,GAAQ,GAAA;QAC3B,OAAOqF,KAAK,CAACrF,GAAI,CAAA,CAAA;AACnB,KAAA,CAAA,CAAA;AAEA,IAAA,OAAOqF,MAAMoD,QAAQ,CAAA;AACvB,CAAC;AAED;;AAEC,IACM,SAASa,YAAgBC,CAAAA,KAAU,EAAE;IAC1C,MAAMC,GAAAA,GAAM,IAAIC,GAAOF,CAAAA,KAAAA,CAAAA,CAAAA;AAEvB,IAAA,IAAIC,GAAI9G,CAAAA,IAAI,KAAK6G,KAAAA,CAAMxK,MAAM,EAAE;QAC7B,OAAOwK,KAAAA,CAAAA;KACR;IAED,OAAOtM,KAAAA,CAAMyM,IAAI,CAACF,GAAAA,CAAAA,CAAAA;AACpB;;ACzLO,SAASG,UAAWC,CAAAA,SAAiB,EAAEC,SAAiB,EAAEC,UAAkB,EAAE;IACnF,OAAOD,SAAAA,GAAY,GAAMD,GAAAA,SAAAA,GAAY,KAAQE,GAAAA,UAAAA,CAAAA;AAC/C,CAAC;AAED;;AAEA,GACaC,MAAAA,gBAAAA,GAAoB,WAAW;IAC1C,IAAI,OAAOC,WAAW,WAAa,EAAA;QACjC,OAAO,SAAS5L,QAAQ,EAAE;YACxB,OAAOA,QAAAA,EAAAA,CAAAA;AACT,SAAA,CAAA;KACD;AACD,IAAA,OAAO4L,OAAOC,qBAAqB,CAAA;AACrC,CAAK,GAAA;AAEL;;;AAGC,IACM,SAASC,SAAAA,CACd7L,EAA4B,EAC5BE,OAAY,EACZ;AACA,IAAA,IAAI4L,YAAY,EAAE,CAAA;AAClB,IAAA,IAAIC,UAAU,KAAK,CAAA;IAEnB,OAAO,SAAS,GAAG9L,IAAW,EAAE;;QAE9B6L,SAAY7L,GAAAA,IAAAA,CAAAA;AACZ,QAAA,IAAI,CAAC8L,OAAS,EAAA;AACZA,YAAAA,OAAAA,GAAU,IAAI,CAAA;YACdL,gBAAiBzM,CAAAA,IAAI,CAAC0M,MAAAA,EAAQ,IAAM;AAClCI,gBAAAA,OAAAA,GAAU,KAAK,CAAA;gBACf/L,EAAGG,CAAAA,KAAK,CAACD,OAAS4L,EAAAA,SAAAA,CAAAA,CAAAA;AACpB,aAAA,CAAA,CAAA;SACD;AACH,KAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACM,SAASE,QAAAA,CAAmChM,EAA4B,EAAEiM,KAAa,EAAE;IAC9F,IAAIC,OAAAA,CAAAA;IACJ,OAAO,SAAS,GAAGjM,IAAW,EAAE;AAC9B,QAAA,IAAIgM,KAAO,EAAA;YACTE,YAAaD,CAAAA,OAAAA,CAAAA,CAAAA;YACbA,OAAUE,GAAAA,UAAAA,CAAWpM,IAAIiM,KAAOhM,EAAAA,IAAAA,CAAAA,CAAAA;SAC3B,MAAA;YACLD,EAAGG,CAAAA,KAAK,CAAC,IAAI,EAAEF,IAAAA,CAAAA,CAAAA;SAChB;QACD,OAAOgM,KAAAA,CAAAA;AACT,KAAA,CAAA;AACF,CAAC;AAED;;;AAGC,IACM,MAAMI,kBAAqB,GAAA,CAACC,KAAsCA,GAAAA,KAAAA,KAAU,OAAU,GAAA,MAAA,GAASA,KAAU,KAAA,KAAA,GAAQ,OAAU,GAAA,SAAS;AAE3I;;;AAGC,IACYC,MAAAA,cAAAA,GAAiB,CAACD,KAAmC5D,EAAAA,KAAAA,EAAeC,MAAgB2D,KAAU,KAAA,OAAA,GAAU5D,QAAQ4D,KAAU,KAAA,KAAA,GAAQ3D,MAAM,CAACD,QAAQC,GAAE,IAAK,EAAE;AAEvK;;;AAGC,IACY6D,MAAAA,MAAAA,GAAS,CAACF,KAAoCG,EAAAA,IAAAA,EAAcC,OAAeC,GAAiB,GAAA;IACvG,MAAMC,KAAAA,GAAQD,GAAM,GAAA,MAAA,GAAS,OAAO,CAAA;IACpC,OAAOL,KAAAA,KAAUM,KAAQF,GAAAA,KAAAA,GAAQJ,KAAU,KAAA,QAAA,GAAW,CAACG,IAAOC,GAAAA,KAAI,IAAK,CAAA,GAAID,IAAI,CAAA;AACjF,EAAE;AAEF;;;IAIO,SAASI,gCAAiCC,CAAAA,IAAmC,EAAEC,MAAsB,EAAEC,kBAA2B,EAAE;IACzI,MAAMC,UAAAA,GAAaF,OAAOrM,MAAM,CAAA;AAEhC,IAAA,IAAIgI,KAAQ,GAAA,CAAA,CAAA;AACZ,IAAA,IAAIwE,KAAQD,GAAAA,UAAAA,CAAAA;IAEZ,IAAIH,IAAAA,CAAKK,OAAO,EAAE;AAChB,QAAA,MAAM,EAACC,MAAM,GAAEC,SAAQC,OAAAA,GAAQ,GAAGR,IAAAA,CAAAA;AAClC,QAAA,MAAMS,WAAWT,IAAKU,CAAAA,OAAO,GAAGV,IAAKU,CAAAA,OAAO,CAAC1L,OAAO,GAAGgL,IAAKU,CAAAA,OAAO,CAAC1L,OAAO,CAACyL,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAA;QAClG,MAAME,IAAAA,GAAOL,OAAOK,IAAI,CAAA;QACxB,MAAM,EAACvG,GAAG,GAAEC,GAAG,GAAEuG,UAAU,GAAEC,UAAU,GAAC,GAAGP,MAAAA,CAAOQ,aAAa,EAAA,CAAA;AAE/D,QAAA,IAAIF,UAAY,EAAA;YACdhF,KAAQ/D,GAAAA,IAAAA,CAAKuC,GAAG;AAEdyC,YAAAA,YAAAA,CAAa2D,OAASG,EAAAA,IAAAA,EAAMvG,GAAKuC,CAAAA,CAAAA,EAAE;YAEnCuD,kBAAqBC,GAAAA,UAAAA,GAAatD,aAAaoD,MAAQU,EAAAA,IAAAA,EAAML,OAAOS,gBAAgB,CAAC3G,MAAMuC,EAAE,CAAA,CAAA;AAC/F,YAAA,IAAI8D,QAAU,EAAA;AACZ,gBAAA,MAAMO,sBAAuBR,OAC1BpO,CAAAA,KAAK,CAAC,CAAGwJ,EAAAA,KAAAA,GAAQ,GACjBpI,OAAO,EAAA,CACPyN,SAAS,CACRC,CAAAA,QAAS,CAACxP,aAAAA,CAAcwP,KAAK,CAACX,MAAAA,CAAOI,IAAI,CAAC,CAAA,CAAA,CAAA;gBAC9C/E,KAAS/D,IAAAA,IAAAA,CAAKwC,GAAG,CAAC,CAAG2G,EAAAA,mBAAAA,CAAAA,CAAAA;aACtB;YACDpF,KAAQQ,GAAAA,WAAAA,CAAYR,KAAO,EAAA,CAAA,EAAGuE,UAAa,GAAA,CAAA,CAAA,CAAA;SAC5C;AACD,QAAA,IAAIU,UAAY,EAAA;AACd,YAAA,IAAIhF,GAAMhE,GAAAA,IAAAA,CAAKwC,GAAG;YAEhBwC,YAAa2D,CAAAA,OAAAA,EAASF,MAAOK,CAAAA,IAAI,EAAEtG,GAAAA,EAAK,IAAI,CAAEqC,CAAAA,EAAE,GAAG,CAAA;AAEnDwD,YAAAA,kBAAAA,GAAqB,CAAIrD,GAAAA,YAAAA,CAAaoD,MAAQU,EAAAA,IAAAA,EAAML,MAAOS,CAAAA,gBAAgB,CAAC1G,GAAAA,CAAAA,EAAM,IAAI,CAAA,CAAEqC,EAAE,GAAG,CAAC,CAAA,CAAA;AAChG,YAAA,IAAI+D,QAAU,EAAA;AACZ,gBAAA,MAAMU,sBAAuBX,OAC1BpO,CAAAA,KAAK,CAACyJ,GAAAA,GAAM,GACZoF,SAAS,CACRC,CAAAA,KAAAA,GAAS,CAACxP,aAAcwP,CAAAA,KAAK,CAACX,MAAAA,CAAOI,IAAI,CAAC,CAAA,CAAA,CAAA;gBAC9C9E,GAAOhE,IAAAA,IAAAA,CAAKwC,GAAG,CAAC,CAAG8G,EAAAA,mBAAAA,CAAAA,CAAAA;aACpB;YACDf,KAAQhE,GAAAA,WAAAA,CAAYP,GAAKD,EAAAA,KAAAA,EAAOuE,UAAcvE,CAAAA,GAAAA,KAAAA,CAAAA;SACzC,MAAA;AACLwE,YAAAA,KAAAA,GAAQD,UAAavE,GAAAA,KAAAA,CAAAA;SACtB;KACF;IAED,OAAO;AAACA,QAAAA,KAAAA;AAAOwE,QAAAA,KAAAA;AAAK,KAAA,CAAA;AACtB,CAAC;AAED;;;;;AAKC,IACM,SAASgB,mBAAoBpB,CAAAA,IAAI,EAAE;AACxC,IAAA,MAAM,EAACqB,MAAM,GAAEC,SAAQC,YAAAA,GAAa,GAAGvB,IAAAA,CAAAA;AACvC,IAAA,MAAMwB,SAAY,GAAA;AAChBC,QAAAA,IAAAA,EAAMJ,OAAOjH,GAAG;AAChBsH,QAAAA,IAAAA,EAAML,OAAOhH,GAAG;AAChBsH,QAAAA,IAAAA,EAAML,OAAOlH,GAAG;AAChBwH,QAAAA,IAAAA,EAAMN,OAAOjH,GAAG;AAClB,KAAA,CAAA;AACA,IAAA,IAAI,CAACkH,YAAc,EAAA;AACjBvB,QAAAA,IAAAA,CAAKuB,YAAY,GAAGC,SAAAA,CAAAA;AACpB,QAAA,OAAO,IAAI,CAAA;KACZ;IACD,MAAMK,OAAAA,GAAUN,aAAaE,IAAI,KAAKJ,OAAOjH,GAAG,IAC7CmH,YAAaG,CAAAA,IAAI,KAAKL,MAAAA,CAAOhH,GAAG,IAChCkH,YAAAA,CAAaI,IAAI,KAAKL,MAAOlH,CAAAA,GAAG,IAChCmH,YAAaK,CAAAA,IAAI,KAAKN,MAAAA,CAAOjH,GAAG,CAAA;IAEnCrI,MAAO8P,CAAAA,MAAM,CAACP,YAAcC,EAAAA,SAAAA,CAAAA,CAAAA;IAC5B,OAAOK,OAAAA,CAAAA;AACT;;AChKA,MAAME,MAAS,GAAA,CAACC,CAAcA,GAAAA,CAAAA,KAAM,KAAKA,CAAM,KAAA,CAAA,CAAA;AAC/C,MAAMC,SAAAA,GAAY,CAACD,CAAAA,EAAWjG,CAAWnB,EAAAA,CAAAA,GAAc,EAAE/C,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMgJ,CAAK,IAAA,CAAA,CAAMnK,CAAAA,GAAAA,IAAAA,CAAKqK,GAAG,CAAC,CAACF,CAAIjG,GAAAA,CAAAA,IAAKjE,GAAAA,GAAM8C,CAAC,CAAA,CAAA,CAAA;AAChH,MAAMuH,UAAAA,GAAa,CAACH,CAAWjG,EAAAA,CAAAA,EAAWnB,IAAc/C,IAAKmB,CAAAA,GAAG,CAAC,CAAG,EAAA,CAAC,KAAKgJ,CAAKnK,CAAAA,GAAAA,IAAAA,CAAKqK,GAAG,CAAEF,CAAAA,CAAIjG,GAAAA,CAAAA,IAAKjE,GAAAA,GAAM8C,CAAK,CAAA,GAAA,CAAA,CAAA;AAE7G;;;;AAIC,UACKwH,OAAU,GAAA;AACdC,IAAAA,MAAAA,EAAQ,CAACL,CAAcA,GAAAA,CAAAA;IAEvBM,UAAY,EAAA,CAACN,IAAcA,CAAIA,GAAAA,CAAAA;AAE/BO,IAAAA,WAAAA,EAAa,CAACP,CAAc,GAAA,CAACA,CAAKA,IAAAA,IAAI,CAAA,CAAA;IAEtCQ,aAAe,EAAA,CAACR,IAAgBA,CAAAA,CAAK,IAAA,GAAE,IAAK,CAAA,GACxC,GAAMA,GAAAA,CAAAA,GAAIA,IACV,CAAC,GAAA,IAAQ,EAAEA,CAAAA,IAAMA,CAAI,GAAA,CAAA,CAAK,GAAA,CAAA,CAAE;IAEhCS,WAAa,EAAA,CAACT,CAAcA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA;IAEpCU,YAAc,EAAA,CAACV,IAAc,CAACA,KAAK,CAAA,IAAKA,IAAIA,CAAI,GAAA,CAAA;IAEhDW,cAAgB,EAAA,CAACX,IAAgBA,CAAAA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,IAAIA,CACd,GAAA,GAAA,IAAQA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAEhCY,IAAAA,WAAAA,EAAa,CAACZ,CAAAA,GAAcA,CAAIA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA;AAExCa,IAAAA,YAAAA,EAAc,CAACb,CAAAA,GAAc,EAAE,CAACA,CAAK,IAAA,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAI,CAAA,CAAA;IAEtDc,cAAgB,EAAA,CAACd,CAAc,GAAC,CAACA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,IAClB,CAAC,GAAA,IAAQA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAErCe,IAAAA,WAAAA,EAAa,CAACf,CAAAA,GAAcA,CAAIA,GAAAA,CAAAA,GAAIA,IAAIA,CAAIA,GAAAA,CAAAA;IAE5CgB,YAAc,EAAA,CAAChB,CAAc,GAACA,CAAAA,CAAAA,IAAK,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA;IAExDiB,cAAgB,EAAA,CAACjB,CAAc,GAAC,CAACA,CAAK,IAAA,GAAE,IAAK,CAAA,GACzC,GAAMA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GACtB,GAAO,IAAA,CAACA,CAAK,IAAA,CAAA,IAAKA,CAAAA,GAAIA,CAAIA,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,CAAE;AAExCkB,IAAAA,UAAAA,EAAY,CAAClB,CAAc,GAAA,CAACnK,KAAKsL,GAAG,CAACnB,IAAI7J,OAAW,CAAA,GAAA,CAAA;AAEpDiL,IAAAA,WAAAA,EAAa,CAACpB,CAAAA,GAAcnK,IAAKqK,CAAAA,GAAG,CAACF,CAAI7J,GAAAA,OAAAA,CAAAA;IAEzCkL,aAAe,EAAA,CAACrB,CAAc,GAAA,CAAC,GAAOnK,IAAAA,KAAKsL,GAAG,CAACvL,EAAKoK,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAA;AAEzDsB,IAAAA,UAAAA,EAAY,CAACtB,CAAAA,GAAc,CAACA,KAAM,IAAK,CAAInK,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMgJ,CAAAA,GAAI,CAAA,CAAG,CAAA;AAEpEuB,IAAAA,WAAAA,EAAa,CAACvB,CAAAA,GAAc,CAACA,KAAM,IAAK,CAAI,GAAA,CAACnK,IAAKmB,CAAAA,GAAG,CAAC,CAAA,EAAG,CAAC,EAAA,GAAKgJ,KAAK,CAAC;AAErEwB,IAAAA,aAAAA,EAAe,CAACxB,CAAAA,GAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIA,CAAI,GAAA,GAAA,GAC9C,GAAMnK,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,CAAG,EAAA,EAAA,IAAMgJ,CAAI,GAAA,CAAA,GAAI,CAAA,CAAA,CAAA,GAChC,GAAO,IAAA,CAACnK,IAAAA,CAAKmB,GAAG,CAAC,CAAA,EAAG,CAAC,EAAA,IAAMgJ,CAAI,GAAA,CAAA,GAAI,CAAA,CAAA,CAAA,GAAM,CAAA,CAAE;AAE/CyB,IAAAA,UAAAA,EAAY,CAACzB,CAAAA,GAAc,CAACA,IAAK,IAAKA,CAAI,GAAA,EAAEnK,IAAAA,CAAKyB,IAAI,CAAC,CAAA,GAAI0I,CAAIA,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAE;IAErE0B,WAAa,EAAA,CAAC1B,CAAcnK,GAAAA,IAAAA,CAAKyB,IAAI,CAAC,IAAI,CAAC0I,CAAK,IAAA,CAAA,IAAKA,CAAAA,CAAAA;AAErD2B,IAAAA,aAAAA,EAAe,CAAC3B,CAAAA,GAAc,CAAEA,CAAK,IAAA,GAAE,IAAK,CAAA,GACxC,CAAC,GAAA,IAAOnK,IAAAA,CAAKyB,IAAI,CAAC,CAAA,GAAI0I,CAAIA,GAAAA,CAAAA,CAAAA,GAAK,CAAA,CAAA,GAC/B,GAAOnK,IAAAA,KAAKyB,IAAI,CAAC,CAAI,GAAC0I,CAAAA,CAAK,IAAA,CAAA,IAAKA,CAAAA,CAAAA,GAAK,CAAA,CAAE;IAE3C4B,aAAe,EAAA,CAAC5B,IAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIC,SAAUD,CAAAA,CAAAA,EAAG,OAAO,GAAI,CAAA;IAEtE6B,cAAgB,EAAA,CAAC7B,IAAcD,MAAOC,CAAAA,CAAAA,CAAAA,GAAKA,IAAIG,UAAWH,CAAAA,CAAAA,EAAG,OAAO,GAAI,CAAA;AAExE8B,IAAAA,gBAAAA,CAAAA,CAAiB9B,CAAS,EAAE;AAC1B,QAAA,MAAMjG,CAAI,GAAA,MAAA,CAAA;AACV,QAAA,MAAMnB,CAAI,GAAA,IAAA,CAAA;AACV,QAAA,OAAOmH,OAAOC,CAAKA,CAAAA,GAAAA,CAAAA,GACjBA,IAAI,GACA,GAAA,GAAA,GAAMC,UAAUD,CAAI,GAAA,CAAA,EAAGjG,CAAGnB,EAAAA,CAAAA,CAAAA,GAC1B,MAAM,GAAMuH,GAAAA,UAAAA,CAAWH,IAAI,CAAI,GAAA,CAAA,EAAGjG,GAAGnB,CAAE,CAAA,CAAA;AAC/C,KAAA;AAEAmJ,IAAAA,UAAAA,CAAAA,CAAW/B,CAAS,EAAE;AACpB,QAAA,MAAMjG,CAAI,GAAA,OAAA,CAAA;QACV,OAAOiG,CAAAA,GAAIA,KAAMjG,CAAAA,CAAI,GAAA,CAAA,IAAKiG,CAAAA,GAAIjG,CAAAA,CAAAA,CAAAA;AAChC,KAAA;AAEAiI,IAAAA,WAAAA,CAAAA,CAAYhC,CAAS,EAAE;AACrB,QAAA,MAAMjG,CAAI,GAAA,OAAA,CAAA;AACV,QAAA,OAAO,CAACiG,CAAK,IAAA,CAAA,IAAKA,CAAK,IAAA,CAACjG,CAAI,GAAA,CAAA,IAAKiG,CAAAA,GAAIjG,CAAAA,CAAK,GAAA,CAAA,CAAA;AAC5C,KAAA;AAEAkI,IAAAA,aAAAA,CAAAA,CAAcjC,CAAS,EAAE;AACvB,QAAA,IAAIjG,CAAI,GAAA,OAAA,CAAA;AACR,QAAA,IAAI,CAACiG,CAAK,IAAA,GAAE,IAAK,CAAG,EAAA;AAClB,YAAA,OAAO,OAAOA,CAAAA,GAAIA,CAAK,IAAA,CAAEjG,CAAAA,CAAAA,IAAM,KAAK,IAAK,CAAA,IAAKiG,CAAAA,GAAIjG,CAAAA,CAAC,CAAA,CAAA;SACpD;QACD,OAAO,GAAA,IAAO,CAACiG,KAAK,CAAA,IAAKA,KAAM,CAAA,CAACjG,KAAM,KAAK,IAAK,CAAA,IAAKiG,CAAAA,GAAIjG,CAAAA,CAAAA,GAAK,CAAA,CAAA,CAAA;AAChE,KAAA;AAEAmI,IAAAA,YAAAA,EAAc,CAAClC,CAAc,GAAA,CAAA,GAAII,OAAQ+B,CAAAA,aAAa,CAAC,CAAInC,GAAAA,CAAAA,CAAAA;AAE3DmC,IAAAA,aAAAA,CAAAA,CAAcnC,CAAS,EAAE;AACvB,QAAA,MAAMoC,CAAI,GAAA,MAAA,CAAA;AACV,QAAA,MAAMC,CAAI,GAAA,IAAA,CAAA;QACV,IAAIrC,CAAAA,GAAK,IAAIqC,CAAI,EAAA;AACf,YAAA,OAAOD,IAAIpC,CAAIA,GAAAA,CAAAA,CAAAA;SAChB;QACD,IAAIA,CAAAA,GAAK,IAAIqC,CAAI,EAAA;AACf,YAAA,OAAOD,KAAKpC,CAAAA,IAAM,GAAMqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,IAAA,CAAA;SACnC;QACD,IAAIA,CAAAA,GAAK,MAAMqC,CAAI,EAAA;AACjB,YAAA,OAAOD,KAAKpC,CAAAA,IAAM,IAAOqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,MAAA,CAAA;SACpC;AACD,QAAA,OAAOoC,KAAKpC,CAAAA,IAAM,KAAQqC,GAAAA,CAAC,IAAKrC,CAAI,GAAA,QAAA,CAAA;AACtC,KAAA;AAEAsC,IAAAA,eAAAA,EAAiB,CAACtC,CAAc,GAACA,IAAI,GACjCI,GAAAA,OAAAA,CAAQ8B,YAAY,CAAClC,CAAAA,GAAI,CAAK,CAAA,GAAA,GAAA,GAC9BI,QAAQ+B,aAAa,CAACnC,IAAI,CAAI,GAAA,CAAA,CAAA,GAAK,MAAM,GAAG;AAClD;;ACrHO,SAASuC,mBAAoB5S,CAAAA,KAAc,EAA2C;IAC3F,IAAIA,KAAAA,IAAS,OAAOA,KAAAA,KAAU,QAAU,EAAA;QACtC,MAAMI,IAAAA,GAAOJ,MAAMO,QAAQ,EAAA,CAAA;QAC3B,OAAOH,IAAAA,KAAS,4BAA4BA,IAAS,KAAA,yBAAA,CAAA;KACtD;AAED,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAWM,SAASyS,KAAM7S,CAAAA,KAAK,EAAE;AAC3B,IAAA,OAAO4S,mBAAoB5S,CAAAA,KAAAA,CAAAA,GAASA,KAAQ,GAAA,IAAI8S,cAAM9S,KAAM,CAAA,CAAA;AAC9D,CAAC;AAKM,SAAS+S,aAAc/S,CAAAA,KAAK,EAAE;AACnC,IAAA,OAAO4S,mBAAoB5S,CAAAA,KAAAA,CAAAA,GACvBA,KACA,GAAA,IAAI8S,aAAM9S,CAAAA,KAAAA,CAAAA,CAAOgT,QAAQ,CAAC,GAAKC,CAAAA,CAAAA,MAAM,CAAC,GAAA,CAAA,CAAKC,SAAS,EAAE,CAAA;AAC5D;;AC/BA,MAAMC,OAAU,GAAA;AAAC,IAAA,GAAA;AAAK,IAAA,GAAA;AAAK,IAAA,aAAA;AAAe,IAAA,QAAA;AAAU,IAAA,SAAA;AAAU,CAAA,CAAA;AAC9D,MAAMC,MAAS,GAAA;AAAC,IAAA,OAAA;AAAS,IAAA,aAAA;AAAe,IAAA,iBAAA;AAAkB,CAAA,CAAA;AAEnD,SAASC,uBAAwBC,CAAAA,QAAQ,EAAE;IAChDA,QAAS5G,CAAAA,GAAG,CAAC,WAAa,EAAA;QACxBc,KAAOvN,EAAAA,SAAAA;QACPsT,QAAU,EAAA,IAAA;QACVC,MAAQ,EAAA,cAAA;QACRjS,EAAItB,EAAAA,SAAAA;QACJ2M,IAAM3M,EAAAA,SAAAA;QACNwT,IAAMxT,EAAAA,SAAAA;QACNyT,EAAIzT,EAAAA,SAAAA;QACJG,IAAMH,EAAAA,SAAAA;AACR,KAAA,CAAA,CAAA;IAEAqT,QAASK,CAAAA,QAAQ,CAAC,WAAa,EAAA;AAC7BC,QAAAA,SAAAA,EAAW,KAAK;AAChBC,QAAAA,UAAAA,EAAY,KAAK;AACjBC,QAAAA,WAAAA,EAAa,CAACC,IAASA,GAAAA,IAAAA,KAAS,YAAgBA,IAAAA,IAAAA,KAAS,gBAAgBA,IAAS,KAAA,IAAA;AACpF,KAAA,CAAA,CAAA;IAEAT,QAAS5G,CAAAA,GAAG,CAAC,YAAc,EAAA;QACzB0G,MAAQ,EAAA;YACNhT,IAAM,EAAA,OAAA;YACN4T,UAAYZ,EAAAA,MAAAA;AACd,SAAA;QACAD,OAAS,EAAA;YACP/S,IAAM,EAAA,QAAA;YACN4T,UAAYb,EAAAA,OAAAA;AACd,SAAA;AACF,KAAA,CAAA,CAAA;IAEAG,QAASK,CAAAA,QAAQ,CAAC,YAAc,EAAA;QAC9BC,SAAW,EAAA,WAAA;AACb,KAAA,CAAA,CAAA;IAEAN,QAAS5G,CAAAA,GAAG,CAAC,aAAe,EAAA;QAC1BuH,MAAQ,EAAA;YACNC,SAAW,EAAA;gBACTX,QAAU,EAAA,GAAA;AACZ,aAAA;AACF,SAAA;QACAY,MAAQ,EAAA;YACND,SAAW,EAAA;gBACTX,QAAU,EAAA,CAAA;AACZ,aAAA;AACF,SAAA;QACAa,IAAM,EAAA;YACJC,UAAY,EAAA;gBACVjB,MAAQ,EAAA;oBACNxG,IAAM,EAAA,aAAA;AACR,iBAAA;gBACA0H,OAAS,EAAA;oBACPlU,IAAM,EAAA,SAAA;AACNmT,oBAAAA,QAAAA,EAAU;AACZ,iBAAA;AACF,aAAA;AACF,SAAA;QACAgB,IAAM,EAAA;YACJF,UAAY,EAAA;gBACVjB,MAAQ,EAAA;oBACNM,EAAI,EAAA,aAAA;AACN,iBAAA;gBACAY,OAAS,EAAA;oBACPlU,IAAM,EAAA,SAAA;oBACNoT,MAAQ,EAAA,QAAA;AACRjS,oBAAAA,EAAAA,EAAI8C,CAAAA,CAAAA,GAAKA,CAAI,GAAA,CAAA;AACf,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA,CAAA,CAAA;AACF;;ACvEO,SAASmQ,oBAAqBlB,CAAAA,QAAQ,EAAE;IAC7CA,QAAS5G,CAAAA,GAAG,CAAC,QAAU,EAAA;AACrB+H,QAAAA,WAAAA,EAAa,IAAI;QACjBC,OAAS,EAAA;YACPC,GAAK,EAAA,CAAA;YACL1G,KAAO,EAAA,CAAA;YACP2G,MAAQ,EAAA,CAAA;YACR5G,IAAM,EAAA,CAAA;AACR,SAAA;AACF,KAAA,CAAA,CAAA;AACF;;ACTA,MAAM6G,YAAY,IAAIC,GAAAA,EAAAA,CAAAA;AAEtB,SAASC,eAAgBC,CAAAA,MAAc,EAAE3R,OAAkC,EAAE;AAC3EA,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;AACtB,IAAA,MAAM4R,QAAWD,GAAAA,MAAAA,GAASE,IAAKC,CAAAA,SAAS,CAAC9R,OAAAA,CAAAA,CAAAA;IACzC,IAAI+R,SAAAA,GAAYP,SAAUQ,CAAAA,GAAG,CAACJ,QAAAA,CAAAA,CAAAA;AAC9B,IAAA,IAAI,CAACG,SAAW,EAAA;AACdA,QAAAA,SAAAA,GAAY,IAAIE,IAAAA,CAAKC,YAAY,CAACP,MAAQ3R,EAAAA,OAAAA,CAAAA,CAAAA;QAC1CwR,SAAUnI,CAAAA,GAAG,CAACuI,QAAUG,EAAAA,SAAAA,CAAAA,CAAAA;KACzB;IACD,OAAOA,SAAAA,CAAAA;AACT,CAAA;AAEO,SAASI,YAAaC,CAAAA,GAAW,EAAET,MAAc,EAAE3R,OAAkC,EAAE;AAC5F,IAAA,OAAO0R,eAAgBC,CAAAA,MAAAA,EAAQ3R,OAASqS,CAAAA,CAAAA,MAAM,CAACD,GAAAA,CAAAA,CAAAA;AACjD;;ACRA,MAAME,UAAa,GAAA;AAOjBpK,CAAAA,MAAAA,CAAAA,CAAOvL,KAAK,EAAE;AACZ,QAAA,OAAOE,QAAQF,KAAS,CAAA,IAAyBA,KAAAA,GAAS,KAAKA,KAAK,CAAA;AACtE,KAAA;AASC,CACD4V,SAAQC,SAAS,EAAEpT,KAAK,EAAEqT,KAAK,EAAE;AAC/B,QAAA,IAAID,cAAc,CAAG,EAAA;AACnB,YAAA,OAAO;SACR;AAED,QAAA,MAAMb,SAAS,IAAI,CAACe,KAAK,CAAC1S,OAAO,CAAC2R,MAAM,CAAA;QACxC,IAAIgB,QAAAA,CAAAA;QACJ,IAAIC,KAAAA,GAAQJ;QAEZ,IAAIC,KAAAA,CAAM7T,MAAM,GAAG,CAAG,EAAA;YAEpB,MAAMiU,OAAAA,GAAUhQ,KAAKwC,GAAG,CAACxC,KAAKa,GAAG,CAAC+O,KAAK,CAAC,CAAE,CAAA,CAAC9V,KAAK,CAAGkG,EAAAA,IAAAA,CAAKa,GAAG,CAAC+O,KAAK,CAACA,MAAM7T,MAAM,GAAG,CAAE,CAAA,CAACjC,KAAK,CAAA,CAAA,CAAA;YACzF,IAAIkW,OAAAA,GAAU,IAAQA,IAAAA,OAAAA,GAAU,KAAO,EAAA;gBACrCF,QAAW,GAAA,YAAA,CAAA;aACZ;AAEDC,YAAAA,KAAAA,GAAQE,eAAeN,SAAWC,EAAAA,KAAAA,CAAAA,CAAAA;SACnC;AAED,QAAA,MAAMM,QAAWzP,GAAAA,KAAAA,CAAMT,IAAKa,CAAAA,GAAG,CAACkP,KAAAA,CAAAA,CAAAA,CAAAA;AAOhC,QAAA,MAAMI,aAAalO,KAAMiO,CAAAA,QAAAA,CAAAA,GAAY,CAAIlQ,GAAAA,IAAAA,CAAKwC,GAAG,CAACxC,IAAAA,CAAKuC,GAAG,CAAC,CAAC,CAAIvC,GAAAA,IAAAA,CAAKoB,KAAK,CAAC8O,QAAAA,CAAAA,EAAW,KAAK,CAAE,CAAA,CAAA;AAE7F,QAAA,MAAM/S,OAAU,GAAA;AAAC2S,YAAAA,QAAAA;YAAUM,qBAAuBD,EAAAA,UAAAA;YAAYE,qBAAuBF,EAAAA,UAAAA;AAAU,SAAA,CAAA;QAC/FhW,MAAO8P,CAAAA,MAAM,CAAC9M,OAAS,EAAA,IAAI,CAACA,OAAO,CAACyS,KAAK,CAACJ,MAAM,CAAA,CAAA;QAEhD,OAAOF,YAAAA,CAAaK,WAAWb,MAAQ3R,EAAAA,OAAAA,CAAAA,CAAAA;AACzC,KAAA;AAUC,CACDmT,aAAYX,SAAS,EAAEpT,KAAK,EAAEqT,KAAK,EAAE;AACnC,QAAA,IAAID,cAAc,CAAG,EAAA;YACnB,OAAO,GAAA,CAAA;SACR;AACD,QAAA,MAAMY,MAASX,GAAAA,KAAK,CAACrT,KAAAA,CAAM,CAACiU,WAAW,IAAKb,SAAa3P,GAAAA,IAAAA,CAAKmB,GAAG,CAAC,EAAA,EAAInB,IAAKoB,CAAAA,KAAK,CAACX,KAAMkP,CAAAA,SAAAA,CAAAA,CAAAA,CAAAA,CAAAA;QACvF,IAAI;AAAC,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,CAAA;AAAG,YAAA,EAAA;AAAI,YAAA,EAAA;AAAG,SAAA,CAACc,QAAQ,CAACF,MAAAA,CAAAA,IAAWhU,QAAQ,GAAMqT,GAAAA,KAAAA,CAAM7T,MAAM,EAAE;YACvE,OAAO0T,UAAAA,CAAWC,OAAO,CAACpV,IAAI,CAAC,IAAI,EAAEqV,WAAWpT,KAAOqT,EAAAA,KAAAA,CAAAA,CAAAA;SACxD;QACD,OAAO,EAAA,CAAA;AACT,KAAA;AAEF,CAAA,CAAA;AAGA,SAASK,cAAeN,CAAAA,SAAS,EAAEC,KAAK,EAAE;IAGxC,IAAIG,KAAAA,GAAQH,KAAM7T,CAAAA,MAAM,GAAG,CAAA,GAAI6T,KAAK,CAAC,CAAE,CAAA,CAAC9V,KAAK,GAAG8V,KAAK,CAAC,CAAE,CAAA,CAAC9V,KAAK,GAAG8V,KAAK,CAAC,CAAE,CAAA,CAAC9V,KAAK,GAAG8V,KAAK,CAAC,CAAE,CAAA,CAAC9V,KAAK,CAAA;IAGhG,IAAIkG,IAAAA,CAAKa,GAAG,CAACkP,KAAAA,CAAAA,IAAU,KAAKJ,SAAc3P,KAAAA,IAAAA,CAAKoB,KAAK,CAACuO,SAAY,CAAA,EAAA;QAE/DI,KAAQJ,GAAAA,SAAAA,GAAY3P,IAAKoB,CAAAA,KAAK,CAACuO,SAAAA,CAAAA,CAAAA;KAChC;IACD,OAAOI,KAAAA,CAAAA;AACT,CAAA;AAKC,CACD,YAAe;AAACN,IAAAA,UAAAA;AAAU,CAAE;;ACnGrB,SAASiB,kBAAmBtD,CAAAA,QAAQ,EAAE;IAC3CA,QAAS5G,CAAAA,GAAG,CAAC,OAAS,EAAA;AACpBmK,QAAAA,OAAAA,EAAS,IAAI;AACbC,QAAAA,MAAAA,EAAQ,KAAK;AACbjV,QAAAA,OAAAA,EAAS,KAAK;AACdkV,QAAAA,WAAAA,EAAa,KAAK;AAQjB,CACDC,MAAQ,EAAA,OAAA;AAERC,QAAAA,IAAAA,EAAM,IAAI;AAKT,CACDC,KAAO,EAAA,CAAA;QAGPC,IAAM,EAAA;AACJN,YAAAA,OAAAA,EAAS,IAAI;YACbO,SAAW,EAAA,CAAA;AACXC,YAAAA,eAAAA,EAAiB,IAAI;AACrBC,YAAAA,SAAAA,EAAW,IAAI;YACfC,UAAY,EAAA,CAAA;AACZC,YAAAA,SAAAA,EAAW,CAACC,IAAAA,EAAMpU,OAAYA,GAAAA,OAAAA,CAAQ+T,SAAS;AAC/CM,YAAAA,SAAAA,EAAW,CAACD,IAAAA,EAAMpU,OAAYA,GAAAA,OAAAA,CAAQwP,KAAK;AAC3CiE,YAAAA,MAAAA,EAAQ,KAAK;AACf,SAAA;QAEAa,MAAQ,EAAA;AACNd,YAAAA,OAAAA,EAAS,IAAI;AACbe,YAAAA,IAAAA,EAAM,EAAE;YACRC,UAAY,EAAA,GAAA;YACZC,KAAO,EAAA,CAAA;AACT,SAAA;QAGAC,KAAO,EAAA;AAELlB,YAAAA,OAAAA,EAAS,KAAK;YAGdmB,IAAM,EAAA,EAAA;YAGNtD,OAAS,EAAA;gBACPC,GAAK,EAAA,CAAA;gBACLC,MAAQ,EAAA,CAAA;AACV,aAAA;AACF,SAAA;QAGAkB,KAAO,EAAA;YACLmC,WAAa,EAAA,CAAA;YACbC,WAAa,EAAA,EAAA;AACbC,YAAAA,MAAAA,EAAQ,KAAK;YACbC,eAAiB,EAAA,CAAA;YACjBC,eAAiB,EAAA,EAAA;YACjB3D,OAAS,EAAA,CAAA;AACTmC,YAAAA,OAAAA,EAAS,IAAI;AACbyB,YAAAA,QAAAA,EAAU,IAAI;YACdC,eAAiB,EAAA,CAAA;YACjBC,WAAa,EAAA,CAAA;YAEblX,QAAUmX,EAAAA,KAAAA,CAAM9C,UAAU,CAACpK,MAAM;AACjCmN,YAAAA,KAAAA,EAAO,EAAC;AACRC,YAAAA,KAAAA,EAAO,EAAC;YACR9K,KAAO,EAAA,QAAA;YACP+K,UAAY,EAAA,MAAA;AAEZC,YAAAA,iBAAAA,EAAmB,KAAK;YACxBC,aAAe,EAAA,2BAAA;YACfC,eAAiB,EAAA,CAAA;AACnB,SAAA;AACF,KAAA,CAAA,CAAA;AAEAzF,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,aAAe,EAAA,OAAA,EAAS,EAAI,EAAA,OAAA,CAAA,CAAA;AAC3C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,YAAc,EAAA,OAAA,EAAS,EAAI,EAAA,aAAA,CAAA,CAAA;AAC1C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,cAAgB,EAAA,OAAA,EAAS,EAAI,EAAA,aAAA,CAAA,CAAA;AAC5C1F,IAAAA,QAAAA,CAAS0F,KAAK,CAAC,aAAe,EAAA,OAAA,EAAS,EAAI,EAAA,OAAA,CAAA,CAAA;IAE3C1F,QAASK,CAAAA,QAAQ,CAAC,OAAS,EAAA;AACzBC,QAAAA,SAAAA,EAAW,KAAK;AAChBE,QAAAA,WAAAA,EAAa,CAACC,IAAAA,GAAS,CAACA,IAAAA,CAAKkF,UAAU,CAAC,QAAA,CAAA,IAAa,CAAClF,IAAAA,CAAKkF,UAAU,CAAC,OAAYlF,CAAAA,IAAAA,IAAAA,KAAS,cAAcA,IAAS,KAAA,QAAA;AAClHF,QAAAA,UAAAA,EAAY,CAACE,IAASA,GAAAA,IAAAA,KAAS,YAAgBA,IAAAA,IAAAA,KAAS,oBAAoBA,IAAS,KAAA,MAAA;AACvF,KAAA,CAAA,CAAA;IAEAT,QAASK,CAAAA,QAAQ,CAAC,QAAU,EAAA;QAC1BC,SAAW,EAAA,OAAA;AACb,KAAA,CAAA,CAAA;IAEAN,QAASK,CAAAA,QAAQ,CAAC,aAAe,EAAA;AAC/BG,QAAAA,WAAAA,EAAa,CAACC,IAAAA,GAASA,IAAS,KAAA,iBAAA,IAAqBA,IAAS,KAAA,UAAA;QAC9DF,UAAY,EAAA,CAACE,OAASA,IAAS,KAAA,iBAAA;AACjC,KAAA,CAAA,CAAA;AACF;;MClGamF,SAAY7Y,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,EAAE;MAChCqW,WAAc9Y,GAAAA,MAAAA,CAAOyC,MAAM,CAAC,IAAI,EAAE;AAM9C,CACD,SAASsW,UAAAA,CAASC,IAAI,EAAEnW,GAAG,EAAE;AAC3B,IAAA,IAAI,CAACA,GAAK,EAAA;QACR,OAAOmW,IAAAA,CAAAA;KACR;IACD,MAAMrX,IAAAA,GAAOkB,GAAIyB,CAAAA,KAAK,CAAC,GAAA,CAAA,CAAA;IACvB,IAAK,IAAI7C,CAAI,GAAA,CAAA,EAAGiG,CAAI/F,GAAAA,IAAAA,CAAKC,MAAM,EAAEH,CAAAA,GAAIiG,CAAG,EAAA,EAAEjG,CAAG,CAAA;QAC3C,MAAMkB,CAAAA,GAAIhB,IAAI,CAACF,CAAE,CAAA,CAAA;AACjBuX,QAAAA,IAAAA,GAAOA,IAAI,CAACrW,CAAE,CAAA,KAAKqW,IAAI,CAACrW,CAAAA,CAAE,GAAG3C,MAAAA,CAAOyC,MAAM,CAAC,IAAI,CAAA,CAAA,CAAA;AACjD,KAAA;IACA,OAAOuW,IAAAA,CAAAA;AACT,CAAA;AAEA,SAAS3M,IAAI4M,IAAI,EAAEtV,KAAK,EAAEuH,MAAM,EAAE;IAChC,IAAI,OAAOvH,UAAU,QAAU,EAAA;QAC7B,OAAOR,KAAAA,CAAM4V,UAASE,CAAAA,IAAAA,EAAMtV,KAAQuH,CAAAA,EAAAA,MAAAA,CAAAA,CAAAA;KACrC;IACD,OAAO/H,KAAAA,CAAM4V,UAASE,CAAAA,IAAAA,EAAM,EAAKtV,CAAAA,EAAAA,KAAAA,CAAAA,CAAAA;AACnC,CAAA;AAKC,CACM,MAAMuV,QAAAA,CAAAA;IACXC,WAAYC,CAAAA,YAAY,EAAEC,SAAS,CAAE;QACnC,IAAI,CAACxF,SAAS,GAAGjU,SAAAA,CAAAA;QACjB,IAAI,CAAC0Z,eAAe,GAAG,iBAAA,CAAA;QACvB,IAAI,CAACC,WAAW,GAAG,iBAAA,CAAA;QACnB,IAAI,CAAC/G,KAAK,GAAG,MAAA,CAAA;QACb,IAAI,CAACgH,QAAQ,GAAG,EAAC,CAAA;QACjB,IAAI,CAACC,gBAAgB,GAAG,CAACC,OAAAA,GAAYA,QAAQhE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB,EAAA,CAAA;QAC/E,IAAI,CAACC,QAAQ,GAAG,EAAC,CAAA;QACjB,IAAI,CAACC,MAAM,GAAG;AACZ,YAAA,WAAA;AACA,YAAA,UAAA;AACA,YAAA,OAAA;AACA,YAAA,YAAA;AACA,YAAA,WAAA;AACD,SAAA,CAAA;QACD,IAAI,CAACC,IAAI,GAAG;YACVC,MAAQ,EAAA,oDAAA;YACRzU,IAAM,EAAA,EAAA;YACN0U,KAAO,EAAA,QAAA;YACPC,UAAY,EAAA,GAAA;AACZC,YAAAA,MAAAA,EAAQ,IAAI;AACd,SAAA,CAAA;QACA,IAAI,CAACC,KAAK,GAAG,EAAC,CAAA;QACd,IAAI,CAACC,oBAAoB,GAAG,CAACC,KAAKtX,OAAY0P,GAAAA,aAAAA,CAAc1P,QAAQsW,eAAe,CAAA,CAAA;QACnF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,KAAKtX,OAAY0P,GAAAA,aAAAA,CAAc1P,QAAQuW,WAAW,CAAA,CAAA;QAC3E,IAAI,CAACiB,UAAU,GAAG,CAACF,KAAKtX,OAAY0P,GAAAA,aAAAA,CAAc1P,QAAQwP,KAAK,CAAA,CAAA;QAC/D,IAAI,CAACiI,SAAS,GAAG,GAAA,CAAA;QACjB,IAAI,CAACC,WAAW,GAAG;YACjBC,IAAM,EAAA,SAAA;AACNC,YAAAA,SAAAA,EAAW,IAAI;AACfC,YAAAA,gBAAAA,EAAkB,KAAK;AACzB,SAAA,CAAA;QACA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACC,OAAO,GAAG,EAAC,CAAA;QAChB,IAAI,CAACC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAACC,KAAK,GAAGxb,SAAAA,CAAAA;QACb,IAAI,CAACyb,MAAM,GAAG,EAAC,CAAA;QACf,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAAA;QAEnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAAA,CAAAA,CAAAA;QACd,IAAI,CAAC/X,KAAK,CAACgY,SAAAA,CAAAA,CAAAA;AACb,KAAA;AAKA,CACAhN,GAAI1I,CAAAA,KAAK,EAAEuH,MAAM,EAAE;QACjB,OAAOmB,GAAAA,CAAI,IAAI,EAAE1I,KAAOuH,EAAAA,MAAAA,CAAAA,CAAAA;AAC1B,KAAA;AAKA8J,CAAAA,GAAAA,CAAIrR,KAAK,EAAE;QACT,OAAOoV,UAAAA,CAAS,IAAI,EAAEpV,KAAAA,CAAAA,CAAAA;AACxB,KAAA;AAKA,CACA2P,QAAS3P,CAAAA,KAAK,EAAEuH,MAAM,EAAE;QACtB,OAAOmB,GAAAA,CAAIyM,aAAanV,KAAOuH,EAAAA,MAAAA,CAAAA,CAAAA;AACjC,KAAA;IAEAsQ,QAAS7X,CAAAA,KAAK,EAAEuH,MAAM,EAAE;QACtB,OAAOmB,GAAAA,CAAIwM,WAAWlV,KAAOuH,EAAAA,MAAAA,CAAAA,CAAAA;AAC/B,KAAA;AAmBAyN,CAAAA,KAAAA,CAAMhV,KAAK,EAAE+P,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;QAC1C,MAAMC,WAAAA,GAAc5C,UAAS,CAAA,IAAI,EAAEpV,KAAAA,CAAAA,CAAAA;QACnC,MAAMiY,iBAAAA,GAAoB7C,UAAS,CAAA,IAAI,EAAE0C,WAAAA,CAAAA,CAAAA;AACzC,QAAA,MAAMI,cAAc,GAAMnI,GAAAA,IAAAA,CAAAA;QAE1B1T,MAAO8b,CAAAA,gBAAgB,CAACH,WAAa,EAAA;AAEnC,YAAA,CAACE,cAAc;gBACblc,KAAOgc,EAAAA,WAAW,CAACjI,IAAK,CAAA;AACxBqI,gBAAAA,QAAAA,EAAU,IAAI;AAChB,aAAA;AAEA,YAAA,CAACrI,OAAO;AACNhI,gBAAAA,UAAAA,EAAY,IAAI;gBAChBsJ,GAAM,CAAA,GAAA;oBACJ,MAAMgH,KAAAA,GAAQ,IAAI,CAACH,WAAY,CAAA,CAAA;oBAC/B,MAAMrZ,MAAAA,GAASoZ,iBAAiB,CAACF,UAAW,CAAA,CAAA;AAC5C,oBAAA,IAAIrb,SAAS2b,KAAQ,CAAA,EAAA;AACnB,wBAAA,OAAOhc,MAAO8P,CAAAA,MAAM,CAAC,IAAItN,MAAQwZ,EAAAA,KAAAA,CAAAA,CAAAA;qBAClC;AACD,oBAAA,OAAOrb,eAAeqb,KAAOxZ,EAAAA,MAAAA,CAAAA,CAAAA;AAC/B,iBAAA;AACA6J,gBAAAA,GAAAA,CAAAA,CAAI1M,KAAK,EAAE;oBACT,IAAI,CAACkc,YAAY,GAAGlc,KAAAA,CAAAA;AACtB,iBAAA;AACF,aAAA;AACF,SAAA,CAAA,CAAA;AACF,KAAA;AAEA0B,IAAAA,KAAAA,CAAM4a,QAAQ,EAAE;AACdA,QAAAA,QAAAA,CAAStQ,OAAO,CAAC,CAACtK,KAAAA,GAAUA,MAAM,IAAI,CAAA,CAAA,CAAA;AACxC,KAAA;AACF,CAAC;AAGD,eAAe,gBAAgB,IAAI6X,QAAS,CAAA;AAC1CzF,IAAAA,WAAAA,EAAa,CAACC,IAAAA,GAAS,CAACA,IAAAA,CAAKkF,UAAU,CAAC,IAAA,CAAA;IACxCpF,UAAY,EAAA,CAACE,OAASA,IAAS,KAAA,QAAA;IAC/B0G,KAAO,EAAA;QACL7G,SAAW,EAAA,aAAA;AACb,KAAA;IACAmH,WAAa,EAAA;AACXjH,QAAAA,WAAAA,EAAa,KAAK;AAClBD,QAAAA,UAAAA,EAAY,KAAK;AACnB,KAAA;AACF,CAAG,EAAA;AAACR,IAAAA,uBAAAA;AAAyBmB,IAAAA,oBAAAA;AAAsBoC,IAAAA,kBAAAA;CAAmB,CAAE;;AC5JxE;;;;;AAKC,IACM,SAAS2F,YAAanC,CAAAA,IAAc,EAAE;IAC3C,IAAI,CAACA,QAAQra,aAAcqa,CAAAA,IAAAA,CAAKxU,IAAI,CAAK7F,IAAAA,aAAAA,CAAcqa,IAAKC,CAAAA,MAAM,CAAG,EAAA;AACnE,QAAA,OAAO,IAAI,CAAA;KACZ;AAED,IAAA,OAAO,CAACD,IAAKE,CAAAA,KAAK,GAAGF,IAAAA,CAAKE,KAAK,GAAG,GAAM,GAAA,EAAE,KACvCF,IAAAA,CAAKI,MAAM,GAAGJ,IAAKI,CAAAA,MAAM,GAAG,GAAA,GAAM,EAAC,CACpCJ,GAAAA,IAAAA,CAAKxU,IAAI,GAAG,KACZwU,GAAAA,IAAAA,CAAKC,MAAM,CAAA;AACf,CAAC;AAED;;AAEC,IACM,SAASmC,YACd7B,CAAAA,GAA6B,EAC7B8B,IAA4B,EAC5BC,EAAY,EACZC,OAAe,EACfC,MAAc,EACd;IACA,IAAIC,SAAAA,GAAYJ,IAAI,CAACG,MAAO,CAAA,CAAA;AAC5B,IAAA,IAAI,CAACC,SAAW,EAAA;QACdA,SAAYJ,GAAAA,IAAI,CAACG,MAAO,CAAA,GAAGjC,IAAImC,WAAW,CAACF,QAAQ9E,KAAK,CAAA;AACxD4E,QAAAA,EAAAA,CAAG5X,IAAI,CAAC8X,MAAAA,CAAAA,CAAAA;KACT;AACD,IAAA,IAAIC,YAAYF,OAAS,EAAA;QACvBA,OAAUE,GAAAA,SAAAA,CAAAA;KACX;IACD,OAAOF,OAAAA,CAAAA;AACT,CAAC;AAKD;;AAEC;AAEM,SAASI,aACdpC,GAA6B,EAC7BP,IAAY,EACZ4C,aAAqB,EACrBC,KAAiF,EACjF;AACAA,IAAAA,KAAAA,GAAQA,SAAS,EAAC,CAAA;AAClB,IAAA,IAAIR,OAAOQ,KAAMR,CAAAA,IAAI,GAAGQ,KAAMR,CAAAA,IAAI,IAAI,EAAC,CAAA;AACvC,IAAA,IAAIC,KAAKO,KAAMC,CAAAA,cAAc,GAAGD,KAAMC,CAAAA,cAAc,IAAI,EAAE,CAAA;IAE1D,IAAID,KAAAA,CAAM7C,IAAI,KAAKA,IAAM,EAAA;QACvBqC,IAAOQ,GAAAA,KAAAA,CAAMR,IAAI,GAAG,EAAC,CAAA;QACrBC,EAAKO,GAAAA,KAAAA,CAAMC,cAAc,GAAG,EAAE,CAAA;AAC9BD,QAAAA,KAAAA,CAAM7C,IAAI,GAAGA,IAAAA,CAAAA;KACd;AAEDO,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;AAERxC,IAAAA,GAAAA,CAAIP,IAAI,GAAGA,IAAAA,CAAAA;AACX,IAAA,IAAIuC,OAAU,GAAA,CAAA,CAAA;IACd,MAAMta,IAAAA,GAAO2a,cAAc/a,MAAM,CAAA;IACjC,IAAIH,CAAAA,EAAWsb,CAAWC,EAAAA,IAAAA,EAAcC,KAAwBC,EAAAA,WAAAA,CAAAA;AAChE,IAAA,IAAKzb,CAAI,GAAA,CAAA,EAAGA,CAAIO,GAAAA,IAAAA,EAAMP,CAAK,EAAA,CAAA;QACzBwb,KAAQN,GAAAA,aAAa,CAAClb,CAAE,CAAA,CAAA;;AAGxB,QAAA,IAAIwb,UAAUrd,SAAaqd,IAAAA,KAAAA,KAAU,IAAI,IAAI,CAACpd,QAAQod,KAAQ,CAAA,EAAA;AAC5DX,YAAAA,OAAAA,GAAUH,YAAa7B,CAAAA,GAAAA,EAAK8B,IAAMC,EAAAA,EAAAA,EAAIC,OAASW,EAAAA,KAAAA,CAAAA,CAAAA;SAC1C,MAAA,IAAIpd,QAAQod,KAAQ,CAAA,EAAA;;;YAGzB,IAAKF,CAAAA,GAAI,GAAGC,IAAOC,GAAAA,KAAAA,CAAMrb,MAAM,EAAEmb,CAAAA,GAAIC,MAAMD,CAAK,EAAA,CAAA;gBAC9CG,WAAcD,GAAAA,KAAK,CAACF,CAAE,CAAA,CAAA;;AAEtB,gBAAA,IAAIG,gBAAgBtd,SAAasd,IAAAA,WAAAA,KAAgB,IAAI,IAAI,CAACrd,QAAQqd,WAAc,CAAA,EAAA;AAC9EZ,oBAAAA,OAAAA,GAAUH,YAAa7B,CAAAA,GAAAA,EAAK8B,IAAMC,EAAAA,EAAAA,EAAIC,OAASY,EAAAA,WAAAA,CAAAA,CAAAA;iBAChD;AACH,aAAA;SACD;AACH,KAAA;AAEA5C,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;IAEX,MAAMC,KAAAA,GAAQf,EAAGza,CAAAA,MAAM,GAAG,CAAA,CAAA;IAC1B,IAAIwb,KAAAA,GAAQT,aAAc/a,CAAAA,MAAM,EAAE;AAChC,QAAA,IAAKH,CAAI,GAAA,CAAA,EAAGA,CAAI2b,GAAAA,KAAAA,EAAO3b,CAAK,EAAA,CAAA;AAC1B,YAAA,OAAO2a,IAAI,CAACC,EAAE,CAAC5a,EAAE,CAAC,CAAA;AACpB,SAAA;QACA4a,EAAGnQ,CAAAA,MAAM,CAAC,CAAGkR,EAAAA,KAAAA,CAAAA,CAAAA;KACd;IACD,OAAOd,OAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;IAQO,SAASe,WAAY3H,CAAAA,KAAY,EAAE4H,KAAa,EAAE7F,KAAa,EAAE;IACtE,MAAMgC,gBAAAA,GAAmB/D,MAAM6H,uBAAuB,CAAA;IACtD,MAAMC,SAAAA,GAAY/F,UAAU,CAAI5R,GAAAA,IAAAA,CAAKwC,GAAG,CAACoP,KAAAA,GAAQ,CAAG,EAAA,GAAA,CAAA,GAAO,CAAC,CAAA;IAC5D,OAAO5R,IAAAA,CAAKiB,KAAK,CAAEwW,CAAAA,KAAQE,GAAAA,SAAQ,IAAK/D,gBAAAA,CAAAA,GAAoBA,gBAAmB+D,GAAAA,SAAAA,CAAAA;AACjF,CAAC;AAED;;AAEC,IACM,SAASC,WAAAA,CAAYC,MAA0B,EAAEpD,GAA8B,EAAE;IACtF,IAAI,CAACA,GAAO,IAAA,CAACoD,MAAQ,EAAA;AACnB,QAAA,OAAA;KACD;IAEDpD,GAAMA,GAAAA,GAAAA,IAAOoD,MAAOC,CAAAA,UAAU,CAAC,IAAA,CAAA,CAAA;AAE/BrD,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;;;AAGRxC,IAAAA,GAAAA,CAAIsD,cAAc,EAAA,CAAA;IAClBtD,GAAIuD,CAAAA,SAAS,CAAC,CAAG,EAAA,CAAA,EAAGH,OAAOjG,KAAK,EAAEiG,OAAOI,MAAM,CAAA,CAAA;AAC/CxD,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AASM,SAASY,UACdzD,GAA6B,EAC7BtX,OAAyB,EACzBiB,CAAS,EACTE,CAAS,EACT;;AAEA6Z,IAAAA,eAAAA,CAAgB1D,GAAKtX,EAAAA,OAAAA,EAASiB,CAAGE,EAAAA,CAAAA,EAAG,IAAI,CAAA,CAAA;AAC1C,CAAC;AAED;AACO,SAAS6Z,eACd1D,CAAAA,GAA6B,EAC7BtX,OAAyB,EACzBiB,CAAS,EACTE,CAAS,EACT8Z,CAAS,EACT;AACA,IAAA,IAAIle,MAAcme,OAAiBC,EAAAA,OAAAA,EAAiB5Y,IAAc6Y,EAAAA,YAAAA,EAAsB3G,OAAe4G,QAAkBC,EAAAA,QAAAA,CAAAA;IACzH,MAAMrE,KAAAA,GAAQjX,QAAQub,UAAU,CAAA;IAChC,MAAMC,QAAAA,GAAWxb,QAAQwb,QAAQ,CAAA;IACjC,MAAMC,MAAAA,GAASzb,QAAQyb,MAAM,CAAA;AAC7B,IAAA,IAAIC,GAAM,GAACF,CAAAA,QAAAA,IAAY,CAAA,IAAKtY,WAAAA,CAAAA;IAE5B,IAAI+T,KAAAA,IAAS,OAAOA,KAAAA,KAAU,QAAU,EAAA;AACtCla,QAAAA,IAAAA,GAAOka,MAAM/Z,QAAQ,EAAA,CAAA;QACrB,IAAIH,IAAAA,KAAS,2BAA+BA,IAAAA,IAAAA,KAAS,4BAA8B,EAAA;AACjFua,YAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;YACRxC,GAAIqE,CAAAA,SAAS,CAAC1a,CAAGE,EAAAA,CAAAA,CAAAA,CAAAA;AACjBmW,YAAAA,GAAAA,CAAIsE,MAAM,CAACF,GAAAA,CAAAA,CAAAA;AACXpE,YAAAA,GAAAA,CAAIuE,SAAS,CAAC5E,KAAAA,EAAO,CAACA,KAAAA,CAAMxC,KAAK,GAAG,CAAA,EAAG,CAACwC,KAAAA,CAAM6D,MAAM,GAAG,CAAA,EAAG7D,MAAMxC,KAAK,EAAEwC,MAAM6D,MAAM,CAAA,CAAA;AACnFxD,YAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACX,YAAA,OAAA;SACD;KACF;IAED,IAAIrV,KAAAA,CAAM2W,MAAWA,CAAAA,IAAAA,MAAAA,IAAU,CAAG,EAAA;AAChC,QAAA,OAAA;KACD;AAEDnE,IAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;IAEb,OAAQ7E,KAAAA;;AAEN,QAAA;AACE,YAAA,IAAIgE,CAAG,EAAA;gBACL3D,GAAIyE,CAAAA,OAAO,CAAC9a,CAAGE,EAAAA,CAAAA,EAAG8Z,IAAI,CAAGQ,EAAAA,MAAAA,EAAQ,GAAG,CAAG3Y,EAAAA,GAAAA,CAAAA,CAAAA;aAClC,MAAA;AACLwU,gBAAAA,GAAAA,CAAI0E,GAAG,CAAC/a,CAAGE,EAAAA,CAAAA,EAAGsa,QAAQ,CAAG3Y,EAAAA,GAAAA,CAAAA,CAAAA;aAC1B;AACDwU,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,UAAA;YACHxH,KAAQwG,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIQ,MAAM,CAAA;AAC1BnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAI4B,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAOtT,CAAI0B,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YAC1DC,GAAOrY,IAAAA,aAAAA,CAAAA;AACPiU,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAI4B,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAOtT,CAAI0B,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YAC1DC,GAAOrY,IAAAA,aAAAA,CAAAA;AACPiU,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAI4B,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOjH,CAAAA,GAAAA,KAAAA,EAAOtT,CAAI0B,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;AAC1DnE,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,aAAA;;;;;;;;AAQHb,YAAAA,YAAAA,GAAeK,MAAS,GAAA,KAAA,CAAA;AACxBlZ,YAAAA,IAAAA,GAAOkZ,MAASL,GAAAA,YAAAA,CAAAA;AAChBF,YAAAA,OAAAA,GAAUrY,IAAKsL,CAAAA,GAAG,CAACuN,GAAAA,GAAMtY,UAAcb,CAAAA,GAAAA,IAAAA,CAAAA;YACvC8Y,QAAWxY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAMtY,GAAAA,UAAAA,CAAAA,IAAe6X,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIG,YAAe7Y,GAAAA,IAAI,CAAD,CAAA;AACvE4Y,YAAAA,OAAAA,GAAUtY,IAAKqK,CAAAA,GAAG,CAACwO,GAAAA,GAAMtY,UAAcb,CAAAA,GAAAA,IAAAA,CAAAA;YACvC+Y,QAAWzY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAMtY,GAAAA,UAAAA,CAAAA,IAAe6X,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIG,YAAe7Y,GAAAA,IAAI,CAAD,CAAA;YACvE+U,GAAI0E,CAAAA,GAAG,CAAC/a,CAAIoa,GAAAA,QAAAA,EAAUla,IAAIga,OAASC,EAAAA,YAAAA,EAAcM,GAAM9Y,GAAAA,EAAAA,EAAI8Y,GAAMvY,GAAAA,OAAAA,CAAAA,CAAAA;YACjEmU,GAAI0E,CAAAA,GAAG,CAAC/a,CAAIqa,GAAAA,QAAAA,EAAUna,IAAI+Z,OAASE,EAAAA,YAAAA,EAAcM,MAAMvY,OAASuY,EAAAA,GAAAA,CAAAA,CAAAA;YAChEpE,GAAI0E,CAAAA,GAAG,CAAC/a,CAAIoa,GAAAA,QAAAA,EAAUla,IAAIga,OAASC,EAAAA,YAAAA,EAAcM,KAAKA,GAAMvY,GAAAA,OAAAA,CAAAA,CAAAA;YAC5DmU,GAAI0E,CAAAA,GAAG,CAAC/a,CAAIqa,GAAAA,QAAAA,EAAUna,IAAI+Z,OAASE,EAAAA,YAAAA,EAAcM,GAAMvY,GAAAA,OAAAA,EAASuY,GAAM9Y,GAAAA,EAAAA,CAAAA,CAAAA;AACtE0U,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,MAAA;AACH,YAAA,IAAI,CAACT,QAAU,EAAA;gBACbjZ,IAAOM,GAAAA,IAAAA,CAAKuZ,OAAO,GAAGX,MAAAA,CAAAA;gBACtBhH,KAAQwG,GAAAA,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAI1Y,IAAI,CAAA;gBACxB+U,GAAI+E,CAAAA,IAAI,CAACpb,CAAIwT,GAAAA,KAAAA,EAAOtT,IAAIoB,IAAM,EAAA,CAAA,GAAIkS,OAAO,CAAIlS,GAAAA,IAAAA,CAAAA,CAAAA;gBAC7C,MAAM;aACP;YACDmZ,GAAOtY,IAAAA,UAAAA,CAAAA;AACT,4BACA,KAAK,SAAA;YACHiY,QAAWxY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAUrY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAUtY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAWzY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;QACR,KAAK,UAAA;YACHP,GAAOtY,IAAAA,UAAAA,CAAAA;AACT,4BACA,KAAK,OAAA;YACHiY,QAAWxY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAUrY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAUtY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAWzY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;YAC7B,MAAM;QACR,KAAK,MAAA;YACHG,QAAWxY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAUrY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAUtY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAWzY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;YAC7BQ,GAAOtY,IAAAA,UAAAA,CAAAA;YACPiY,QAAWxY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;YAC7CP,OAAUrY,GAAAA,IAAAA,CAAKsL,GAAG,CAACuN,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BN,OAAUtY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;YAC1BH,QAAWzY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAAA,CAAAA,IAAQT,CAAIA,GAAAA,CAAAA,GAAI,CAAIQ,GAAAA,MAAM,CAAD,CAAA;AAC7CnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIoa,GAAAA,QAAAA,EAAUla,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B7D,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;AAC7B5D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIqa,GAAAA,QAAAA,EAAUna,CAAI+Z,GAAAA,OAAAA,CAAAA,CAAAA;YAC7B,MAAM;QACR,KAAK,MAAA;AACHA,YAAAA,OAAAA,GAAUD,IAAIA,CAAI,GAAA,CAAA,GAAIpY,KAAKsL,GAAG,CAACuN,OAAOD,MAAM,CAAA;YAC5CN,OAAUtY,GAAAA,IAAAA,CAAKqK,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA;AAC1BnE,YAAAA,GAAAA,CAAI4E,MAAM,CAACjb,CAAIia,GAAAA,OAAAA,EAAS/Z,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;AAC5B7D,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIia,GAAAA,OAAAA,EAAS/Z,CAAIga,GAAAA,OAAAA,CAAAA,CAAAA;YAC5B,MAAM;QACR,KAAK,MAAA;YACH7D,GAAI4E,CAAAA,MAAM,CAACjb,CAAGE,EAAAA,CAAAA,CAAAA,CAAAA;AACdmW,YAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAAA,GAAI4B,KAAKsL,GAAG,CAACuN,QAAQT,CAAAA,GAAIA,CAAI,GAAA,CAAA,GAAIQ,MAAM,CAAD,EAAIta,IAAI0B,IAAKqK,CAAAA,GAAG,CAACwO,GAAOD,CAAAA,GAAAA,MAAAA,CAAAA,CAAAA;YACzE,MAAM;AACR,QAAA,KAAK,KAAK;AACRnE,YAAAA,GAAAA,CAAI2E,SAAS,EAAA,CAAA;YACb,MAAM;AACV,KAAA;AAEA3E,IAAAA,GAAAA,CAAIgF,IAAI,EAAA,CAAA;IACR,IAAItc,OAAAA,CAAQuc,WAAW,GAAG,CAAG,EAAA;AAC3BjF,QAAAA,GAAAA,CAAIkF,MAAM,EAAA,CAAA;KACX;AACH,CAAC;AAED;;;;;;IAOO,SAASC,cACdvQ,CAAAA,KAAY,EACZwQ,IAAU,EACVC,MAAe,EACf;IACAA,MAASA,GAAAA,MAAAA,IAAU;AAEnB,IAAA,OAAO,CAACD,IAAAA,IAASxQ,KAASA,IAAAA,KAAAA,CAAMjL,CAAC,GAAGyb,IAAK/R,CAAAA,IAAI,GAAGgS,MAAAA,IAAUzQ,KAAMjL,CAAAA,CAAC,GAAGyb,IAAAA,CAAK9R,KAAK,GAAG+R,MACjFzQ,IAAAA,KAAAA,CAAM/K,CAAC,GAAGub,IAAKpL,CAAAA,GAAG,GAAGqL,MAAAA,IAAUzQ,KAAM/K,CAAAA,CAAC,GAAGub,IAAAA,CAAKnL,MAAM,GAAGoL,MAAAA,CAAAA;AACzD,CAAC;AAEM,SAASC,QAAAA,CAAStF,GAA6B,EAAEoF,IAAU,EAAE;AAClEpF,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;AACRxC,IAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;AACbxE,IAAAA,GAAAA,CAAI+E,IAAI,CAACK,IAAAA,CAAK/R,IAAI,EAAE+R,IAAAA,CAAKpL,GAAG,EAAEoL,IAAAA,CAAK9R,KAAK,GAAG8R,KAAK/R,IAAI,EAAE+R,KAAKnL,MAAM,GAAGmL,KAAKpL,GAAG,CAAA,CAAA;AAC5EgG,IAAAA,GAAAA,CAAI1D,IAAI,EAAA,CAAA;AACV,CAAC;AAEM,SAASiJ,UAAWvF,CAAAA,GAA6B,EAAE;AACxDA,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AAED;;AAEC,IACM,SAAS2C,cACdxF,CAAAA,GAA6B,EAC7B1W,QAAe,EACfpB,MAAa,EACbud,IAAc,EACdpF,IAAa,EACb;AACA,IAAA,IAAI,CAAC/W,QAAU,EAAA;AACb,QAAA,OAAO0W,IAAI6E,MAAM,CAAC3c,OAAOyB,CAAC,EAAEzB,OAAO2B,CAAC,CAAA,CAAA;KACrC;AACD,IAAA,IAAIwW,SAAS,QAAU,EAAA;QACrB,MAAMqF,QAAAA,GAAW,CAACpc,QAAAA,CAASK,CAAC,GAAGzB,MAAAA,CAAOyB,CAAAA,IAAK,GAAA,CAAA;AAC3CqW,QAAAA,GAAAA,CAAI6E,MAAM,CAACa,QAAUpc,EAAAA,QAAAA,CAASO,CAAC,CAAA,CAAA;AAC/BmW,QAAAA,GAAAA,CAAI6E,MAAM,CAACa,QAAUxd,EAAAA,MAAAA,CAAO2B,CAAC,CAAA,CAAA;AAC/B,KAAA,MAAO,IAAIwW,IAAAA,KAAS,OAAY,KAAA,CAAC,CAACoF,IAAM,EAAA;AACtCzF,QAAAA,GAAAA,CAAI6E,MAAM,CAACvb,QAAAA,CAASK,CAAC,EAAEzB,OAAO2B,CAAC,CAAA,CAAA;KAC1B,MAAA;AACLmW,QAAAA,GAAAA,CAAI6E,MAAM,CAAC3c,MAAAA,CAAOyB,CAAC,EAAEL,SAASO,CAAC,CAAA,CAAA;KAChC;AACDmW,IAAAA,GAAAA,CAAI6E,MAAM,CAAC3c,MAAAA,CAAOyB,CAAC,EAAEzB,OAAO2B,CAAC,CAAA,CAAA;AAC/B,CAAC;AAED;;IAGO,SAAS8b,cAAAA,CACd3F,GAA6B,EAC7B1W,QAAqB,EACrBpB,MAAmB,EACnBud,IAAc,EACd;AACA,IAAA,IAAI,CAACnc,QAAU,EAAA;AACb,QAAA,OAAO0W,IAAI6E,MAAM,CAAC3c,OAAOyB,CAAC,EAAEzB,OAAO2B,CAAC,CAAA,CAAA;KACrC;AACDmW,IAAAA,GAAAA,CAAI4F,aAAa,CACfH,IAAOnc,GAAAA,QAAAA,CAASuc,IAAI,GAAGvc,QAAAA,CAASwc,IAAI,EACpCL,OAAOnc,QAASyc,CAAAA,IAAI,GAAGzc,QAAAA,CAAS0c,IAAI,EACpCP,IAAAA,GAAOvd,MAAO4d,CAAAA,IAAI,GAAG5d,MAAAA,CAAO2d,IAAI,EAChCJ,OAAOvd,MAAO8d,CAAAA,IAAI,GAAG9d,MAAAA,CAAO6d,IAAI,EAChC7d,MAAAA,CAAOyB,CAAC,EACRzB,OAAO2B,CAAC,CAAA,CAAA;AACZ,CAAC;AAED,SAASoc,aAAcjG,CAAAA,GAA6B,EAAEkG,IAAoB,EAAE;IAC1E,IAAIA,IAAAA,CAAKC,WAAW,EAAE;QACpBnG,GAAIqE,CAAAA,SAAS,CAAC6B,IAAAA,CAAKC,WAAW,CAAC,EAAE,EAAED,IAAAA,CAAKC,WAAW,CAAC,CAAE,CAAA,CAAA,CAAA;KACvD;AAED,IAAA,IAAI,CAAC/gB,aAAAA,CAAc8gB,IAAKhC,CAAAA,QAAQ,CAAG,EAAA;QACjClE,GAAIsE,CAAAA,MAAM,CAAC4B,IAAAA,CAAKhC,QAAQ,CAAA,CAAA;KACzB;IAED,IAAIgC,IAAAA,CAAKhO,KAAK,EAAE;QACd8H,GAAIoG,CAAAA,SAAS,GAAGF,IAAAA,CAAKhO,KAAK,CAAA;KAC3B;IAED,IAAIgO,IAAAA,CAAKG,SAAS,EAAE;QAClBrG,GAAIqG,CAAAA,SAAS,GAAGH,IAAAA,CAAKG,SAAS,CAAA;KAC/B;IAED,IAAIH,IAAAA,CAAKI,YAAY,EAAE;QACrBtG,GAAIsG,CAAAA,YAAY,GAAGJ,IAAAA,CAAKI,YAAY,CAAA;KACrC;AACH,CAAA;AAEA,SAASC,YAAAA,CACPvG,GAA6B,EAC7BrW,CAAS,EACTE,CAAS,EACT2c,IAAY,EACZN,IAAoB,EACpB;AACA,IAAA,IAAIA,IAAKO,CAAAA,aAAa,IAAIP,IAAAA,CAAKQ,SAAS,EAAE;AACxC;;;;;;AAMC,QACD,MAAMC,OAAAA,GAAU3G,GAAImC,CAAAA,WAAW,CAACqE,IAAAA,CAAAA,CAAAA;QAChC,MAAMnT,IAAAA,GAAO1J,CAAIgd,GAAAA,OAAAA,CAAQC,qBAAqB,CAAA;QAC9C,MAAMtT,KAAAA,GAAQ3J,CAAIgd,GAAAA,OAAAA,CAAQE,sBAAsB,CAAA;QAChD,MAAM7M,GAAAA,GAAMnQ,CAAI8c,GAAAA,OAAAA,CAAQG,uBAAuB,CAAA;QAC/C,MAAM7M,MAAAA,GAASpQ,CAAI8c,GAAAA,OAAAA,CAAQI,wBAAwB,CAAA;QACnD,MAAMC,WAAAA,GAAcd,IAAKO,CAAAA,aAAa,GAAIzM,CAAAA,GAAMC,GAAAA,MAAK,IAAK,CAAA,GAAIA,MAAM,CAAA;QAEpE+F,GAAIiH,CAAAA,WAAW,GAAGjH,GAAAA,CAAIoG,SAAS,CAAA;AAC/BpG,QAAAA,GAAAA,CAAIwE,SAAS,EAAA,CAAA;AACbxE,QAAAA,GAAAA,CAAIvD,SAAS,GAAGyJ,IAAKgB,CAAAA,eAAe,IAAI,CAAA,CAAA;QACxClH,GAAI4E,CAAAA,MAAM,CAACvR,IAAM2T,EAAAA,WAAAA,CAAAA,CAAAA;QACjBhH,GAAI6E,CAAAA,MAAM,CAACvR,KAAO0T,EAAAA,WAAAA,CAAAA,CAAAA;AAClBhH,QAAAA,GAAAA,CAAIkF,MAAM,EAAA,CAAA;KACX;AACH,CAAA;AAEA,SAASiC,YAAanH,CAAAA,GAA6B,EAAEkG,IAAqB,EAAE;IAC1E,MAAMkB,QAAAA,GAAWpH,IAAIoG,SAAS,CAAA;IAE9BpG,GAAIoG,CAAAA,SAAS,GAAGF,IAAAA,CAAKhO,KAAK,CAAA;AAC1B8H,IAAAA,GAAAA,CAAIqH,QAAQ,CAACnB,IAAK7S,CAAAA,IAAI,EAAE6S,IAAAA,CAAKlM,GAAG,EAAEkM,IAAK/I,CAAAA,KAAK,EAAE+I,IAAAA,CAAK1C,MAAM,CAAA,CAAA;AACzDxD,IAAAA,GAAAA,CAAIoG,SAAS,GAAGgB,QAAAA,CAAAA;AAClB,CAAA;AAEA;;AAEC,IACM,SAASE,UAAAA,CACdtH,GAA6B,EAC7B3C,IAAuB,EACvB1T,CAAS,EACTE,CAAS,EACT4V,IAAoB,EACpByG,IAAuB,GAAA,EAAE,EACzB;IACA,MAAMqB,KAAAA,GAAQhiB,OAAQ8X,CAAAA,IAAAA,CAAAA,GAAQA,IAAO,GAAA;AAACA,QAAAA,IAAAA;AAAK,KAAA,CAAA;AAC3C,IAAA,MAAM6H,SAASgB,IAAKsB,CAAAA,WAAW,GAAG,CAAKtB,IAAAA,IAAAA,CAAKuB,WAAW,KAAK,EAAA,CAAA;AAC5D,IAAA,IAAItgB,CAAWqf,EAAAA,IAAAA,CAAAA;AAEfxG,IAAAA,GAAAA,CAAIwC,IAAI,EAAA,CAAA;IACRxC,GAAIP,CAAAA,IAAI,GAAGA,IAAAA,CAAKwC,MAAM,CAAA;AACtBgE,IAAAA,aAAAA,CAAcjG,GAAKkG,EAAAA,IAAAA,CAAAA,CAAAA;AAEnB,IAAA,IAAK/e,IAAI,CAAGA,EAAAA,CAAAA,GAAIogB,MAAMjgB,MAAM,EAAE,EAAEH,CAAG,CAAA;QACjCqf,IAAOe,GAAAA,KAAK,CAACpgB,CAAE,CAAA,CAAA;QAEf,IAAI+e,IAAAA,CAAKwB,QAAQ,EAAE;YACjBP,YAAanH,CAAAA,GAAAA,EAAKkG,KAAKwB,QAAQ,CAAA,CAAA;SAChC;AAED,QAAA,IAAIxC,MAAQ,EAAA;YACV,IAAIgB,IAAAA,CAAKuB,WAAW,EAAE;gBACpBzH,GAAIiH,CAAAA,WAAW,GAAGf,IAAAA,CAAKuB,WAAW,CAAA;aACnC;AAED,YAAA,IAAI,CAACriB,aAAAA,CAAc8gB,IAAKsB,CAAAA,WAAW,CAAG,EAAA;gBACpCxH,GAAIvD,CAAAA,SAAS,GAAGyJ,IAAAA,CAAKsB,WAAW,CAAA;aACjC;AAEDxH,YAAAA,GAAAA,CAAI2H,UAAU,CAACnB,IAAAA,EAAM7c,CAAGE,EAAAA,CAAAA,EAAGqc,KAAK0B,QAAQ,CAAA,CAAA;SACzC;AAED5H,QAAAA,GAAAA,CAAI6H,QAAQ,CAACrB,IAAAA,EAAM7c,CAAGE,EAAAA,CAAAA,EAAGqc,KAAK0B,QAAQ,CAAA,CAAA;QACtCrB,YAAavG,CAAAA,GAAAA,EAAKrW,CAAGE,EAAAA,CAAAA,EAAG2c,IAAMN,EAAAA,IAAAA,CAAAA,CAAAA;QAE9Brc,CAAK5D,IAAAA,MAAAA,CAAOwZ,KAAKG,UAAU,CAAA,CAAA;AAC7B,KAAA;AAEAI,IAAAA,GAAAA,CAAI6C,OAAO,EAAA,CAAA;AACb,CAAC;AAED;;;;AAIC,IACM,SAASiF,kBAAAA,CACd9H,GAA6B,EAC7B+E,IAA2C,EAC3C;IACA,MAAM,EAACpb,CAAC,GAAEE,CAAC,GAAE8Z,CAAC,GAAEoE,CAAC,GAAE5D,MAAM,GAAC,GAAGY,IAAAA,CAAAA;;AAG7B/E,IAAAA,GAAAA,CAAI0E,GAAG,CAAC/a,CAAAA,GAAIwa,MAAO6D,CAAAA,OAAO,EAAEne,CAAIsa,GAAAA,MAAAA,CAAO6D,OAAO,EAAE7D,OAAO6D,OAAO,EAAE,GAAM1c,GAAAA,EAAAA,EAAIA,IAAI,IAAI,CAAA,CAAA;;AAGlF0U,IAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAAA,EAAGE,CAAIke,GAAAA,CAAAA,GAAI5D,OAAO8D,UAAU,CAAA,CAAA;;AAGvCjI,IAAAA,GAAAA,CAAI0E,GAAG,CAAC/a,CAAAA,GAAIwa,MAAO8D,CAAAA,UAAU,EAAEpe,CAAIke,GAAAA,CAAAA,GAAI5D,MAAO8D,CAAAA,UAAU,EAAE9D,MAAO8D,CAAAA,UAAU,EAAE3c,EAAAA,EAAIO,SAAS,IAAI,CAAA,CAAA;;AAG9FmU,IAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAAA,GAAIga,IAAIQ,MAAO+D,CAAAA,WAAW,EAAEre,CAAIke,GAAAA,CAAAA,CAAAA,CAAAA;;AAG3C/H,IAAAA,GAAAA,CAAI0E,GAAG,CAAC/a,CAAAA,GAAIga,CAAIQ,GAAAA,MAAAA,CAAO+D,WAAW,EAAEre,CAAAA,GAAIke,CAAI5D,GAAAA,MAAAA,CAAO+D,WAAW,EAAE/D,MAAAA,CAAO+D,WAAW,EAAErc,OAAAA,EAAS,GAAG,IAAI,CAAA,CAAA;;AAGpGmU,IAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAAA,GAAIga,CAAG9Z,EAAAA,CAAAA,GAAIsa,OAAOgE,QAAQ,CAAA,CAAA;;AAGrCnI,IAAAA,GAAAA,CAAI0E,GAAG,CAAC/a,CAAAA,GAAIga,CAAIQ,GAAAA,MAAAA,CAAOgE,QAAQ,EAAEte,CAAAA,GAAIsa,MAAOgE,CAAAA,QAAQ,EAAEhE,MAAOgE,CAAAA,QAAQ,EAAE,CAAG,EAAA,CAACtc,SAAS,IAAI,CAAA,CAAA;;AAGxFmU,IAAAA,GAAAA,CAAI6E,MAAM,CAAClb,CAAIwa,GAAAA,MAAAA,CAAO6D,OAAO,EAAEne,CAAAA,CAAAA,CAAAA;AACjC;;ACxgBA,MAAMue,WAAc,GAAA,sCAAA,CAAA;AACpB,MAAMC,UAAa,GAAA,uEAAA,CAAA;AAEnB;;;;;;;;;;AAWC,IACM,SAASC,YAAAA,CAAajjB,KAAsB,EAAE4F,IAAY,EAAU;AACzE,IAAA,MAAMsd,UAAU,CAAC,KAAKljB,KAAI,EAAGmjB,KAAK,CAACJ,WAAAA,CAAAA,CAAAA;AACnC,IAAA,IAAI,CAACG,OAAWA,IAAAA,OAAO,CAAC,CAAA,CAAE,KAAK,QAAU,EAAA;AACvC,QAAA,OAAOtd,IAAO,GAAA,GAAA,CAAA;KACf;IAED5F,KAAQ,GAAA,CAACkjB,OAAO,CAAC,CAAE,CAAA,CAAA;IAEnB,OAAQA,OAAO,CAAC,CAAE,CAAA;QAChB,KAAK,IAAA;YACH,OAAOljB,KAAAA,CAAAA;QACT,KAAK,GAAA;YACHA,KAAS,IAAA,GAAA,CAAA;YACT,MAAM;AAGV,KAAA;AAEA,IAAA,OAAO4F,IAAO5F,GAAAA,KAAAA,CAAAA;AAChB,CAAC;AAED,MAAMojB,YAAe,GAAA,CAAC/e,CAAe,GAAA,CAACA,CAAK,IAAA,CAAA,CAAA;AAQpC,SAASgf,iBAAAA,CAAkBrjB,KAAsC,EAAEsjB,KAAwC,EAAE;AAClH,IAAA,MAAMC,MAAM,EAAC,CAAA;AACb,IAAA,MAAMC,WAAW9iB,QAAS4iB,CAAAA,KAAAA,CAAAA,CAAAA;AAC1B,IAAA,MAAMthB,OAAOwhB,QAAWnjB,GAAAA,MAAAA,CAAO2B,IAAI,CAACshB,SAASA,KAAK,CAAA;IAClD,MAAMG,IAAAA,GAAO/iB,QAASV,CAAAA,KAAAA,CAAAA,GAClBwjB,QACEE,GAAAA,CAAAA,OAAQ1iB,cAAehB,CAAAA,KAAK,CAAC0jB,IAAAA,CAAK,EAAE1jB,KAAK,CAACsjB,KAAK,CAACI,IAAK,CAAA,CAAC,CACtDA,GAAAA,CAAAA,IAAQ1jB,GAAAA,KAAK,CAAC0jB,IAAAA,CAAK,GACrB,IAAM1jB,KAAK,CAAA;IAEf,KAAK,MAAM0jB,QAAQ1hB,IAAM,CAAA;AACvBuhB,QAAAA,GAAG,CAACG,IAAAA,CAAK,GAAGN,YAAAA,CAAaK,IAAKC,CAAAA,IAAAA,CAAAA,CAAAA,CAAAA;AAChC,KAAA;IACA,OAAOH,GAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;AAOC,IACM,SAASI,MAAO3jB,CAAAA,KAA4B,EAAE;AACnD,IAAA,OAAOqjB,kBAAkBrjB,KAAO,EAAA;QAAC2U,GAAK,EAAA,GAAA;QAAK1G,KAAO,EAAA,GAAA;QAAK2G,MAAQ,EAAA,GAAA;QAAK5G,IAAM,EAAA,GAAA;AAAG,KAAA,CAAA,CAAA;AAC/E,CAAC;AAED;;;;;;AAMC,IACM,SAAS4V,aAAc5jB,CAAAA,KAA2B,EAAE;AACzD,IAAA,OAAOqjB,kBAAkBrjB,KAAO,EAAA;AAAC,QAAA,SAAA;AAAW,QAAA,UAAA;AAAY,QAAA,YAAA;AAAc,QAAA,aAAA;AAAc,KAAA,CAAA,CAAA;AACtF,CAAC;AAED;;;;;;;AAOC,IACM,SAAS6jB,SAAU7jB,CAAAA,KAAqB,EAAa;AAC1D,IAAA,MAAMgF,MAAM2e,MAAO3jB,CAAAA,KAAAA,CAAAA,CAAAA;AAEnBgF,IAAAA,GAAAA,CAAI8S,KAAK,GAAG9S,GAAAA,CAAIgJ,IAAI,GAAGhJ,IAAIiJ,KAAK,CAAA;AAChCjJ,IAAAA,GAAAA,CAAImZ,MAAM,GAAGnZ,GAAAA,CAAI2P,GAAG,GAAG3P,IAAI4P,MAAM,CAAA;IAEjC,OAAO5P,GAAAA,CAAAA;AACT,CAAC;AAED;;;;;;AAMC,IAEM,SAAS8e,MAAAA,CAAOzgB,OAA0B,EAAE0gB,QAA4B,EAAE;AAC/E1gB,IAAAA,OAAAA,GAAUA,WAAW,EAAC,CAAA;IACtB0gB,QAAWA,GAAAA,QAAAA,IAAYzQ,SAAS8G,IAAI,CAAA;AAEpC,IAAA,IAAIxU,OAAO5E,cAAeqC,CAAAA,OAAAA,CAAQuC,IAAI,EAAEme,SAASne,IAAI,CAAA,CAAA;IAErD,IAAI,OAAOA,SAAS,QAAU,EAAA;AAC5BA,QAAAA,IAAAA,GAAOoe,SAASpe,IAAM,EAAA,EAAA,CAAA,CAAA;KACvB;AACD,IAAA,IAAI0U,QAAQtZ,cAAeqC,CAAAA,OAAAA,CAAQiX,KAAK,EAAEyJ,SAASzJ,KAAK,CAAA,CAAA;IACxD,IAAIA,KAAAA,IAAS,CAAC,CAAC,KAAKA,KAAI,EAAG6I,KAAK,CAACH,UAAa,CAAA,EAAA;QAC5C9e,OAAQC,CAAAA,IAAI,CAAC,iCAAA,GAAoCmW,KAAQ,GAAA,GAAA,CAAA,CAAA;QACzDA,KAAQra,GAAAA,SAAAA,CAAAA;KACT;AAED,IAAA,MAAMma,IAAO,GAAA;AACXC,QAAAA,MAAAA,EAAQrZ,cAAeqC,CAAAA,OAAAA,CAAQgX,MAAM,EAAE0J,SAAS1J,MAAM,CAAA;AACtDE,QAAAA,UAAAA,EAAY0I,aAAajiB,cAAeqC,CAAAA,OAAAA,CAAQkX,UAAU,EAAEwJ,QAAAA,CAASxJ,UAAU,CAAG3U,EAAAA,IAAAA,CAAAA;AAClFA,QAAAA,IAAAA;AACA0U,QAAAA,KAAAA;AACAE,QAAAA,MAAAA,EAAQxZ,cAAeqC,CAAAA,OAAAA,CAAQmX,MAAM,EAAEuJ,SAASvJ,MAAM,CAAA;QACtDoC,MAAQ,EAAA,EAAA;AACV,KAAA,CAAA;IAEAxC,IAAKwC,CAAAA,MAAM,GAAGL,YAAanC,CAAAA,IAAAA,CAAAA,CAAAA;IAC3B,OAAOA,IAAAA,CAAAA;AACT,CAAC;AAED;;;;;;;;;;IAWO,SAAS6J,OAAAA,CAAQC,MAAsB,EAAEnK,OAAgB,EAAEtX,KAAc,EAAE0hB,IAA6B,EAAE;AAC/G,IAAA,IAAIC,YAAY,IAAI,CAAA;AACpB,IAAA,IAAItiB,GAAWO,IAAcrC,EAAAA,KAAAA,CAAAA;IAE7B,IAAK8B,CAAAA,GAAI,GAAGO,IAAO6hB,GAAAA,MAAAA,CAAOjiB,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC/C9B,KAAQkkB,GAAAA,MAAM,CAACpiB,CAAE,CAAA,CAAA;AACjB,QAAA,IAAI9B,UAAUC,SAAW,EAAA;YACvB,SAAS;SACV;AACD,QAAA,IAAI8Z,OAAY9Z,KAAAA,SAAAA,IAAa,OAAOD,KAAAA,KAAU,UAAY,EAAA;AACxDA,YAAAA,KAAAA,GAAQA,KAAM+Z,CAAAA,OAAAA,CAAAA,CAAAA;AACdqK,YAAAA,SAAAA,GAAY,KAAK,CAAA;SAClB;QACD,IAAI3hB,KAAAA,KAAUxC,SAAaC,IAAAA,OAAAA,CAAQF,KAAQ,CAAA,EAAA;AACzCA,YAAAA,KAAAA,GAAQA,KAAK,CAACyC,KAAQzC,GAAAA,KAAAA,CAAMiC,MAAM,CAAC,CAAA;AACnCmiB,YAAAA,SAAAA,GAAY,KAAK,CAAA;SAClB;AACD,QAAA,IAAIpkB,UAAUC,SAAW,EAAA;YACvB,IAAIkkB,IAAAA,IAAQ,CAACC,SAAW,EAAA;gBACtBD,IAAKC,CAAAA,SAAS,GAAG,KAAK,CAAA;aACvB;YACD,OAAOpkB,KAAAA,CAAAA;SACR;AACH,KAAA;AACF,CAAC;AAED;;;;;IAMO,SAASqkB,SAAUC,CAAAA,MAAqC,EAAEpN,KAAsB,EAAEH,WAAoB,EAAE;AAC7G,IAAA,MAAM,EAACtO,GAAAA,GAAKC,GAAAA,GAAI,GAAG4b,MAAAA,CAAAA;AACnB,IAAA,MAAMC,SAASljB,WAAY6V,CAAAA,KAAAA,EAAO,CAACxO,GAAAA,GAAMD,GAAE,IAAK,CAAA,CAAA,CAAA;IAChD,MAAM+b,QAAAA,GAAW,CAACxkB,KAAeykB,EAAAA,GAAAA,GAAgB1N,eAAe/W,KAAU,KAAA,CAAA,GAAI,CAAIA,GAAAA,KAAAA,GAAQykB,GAAG,CAAA;IAC7F,OAAO;AACLhc,QAAAA,GAAAA,EAAK+b,QAAS/b,CAAAA,GAAAA,EAAK,CAACvC,IAAAA,CAAKa,GAAG,CAACwd,MAAAA,CAAAA,CAAAA;AAC7B7b,QAAAA,GAAAA,EAAK8b,SAAS9b,GAAK6b,EAAAA,MAAAA,CAAAA;AACrB,KAAA,CAAA;AACF,CAAC;AAUM,SAASG,aAAAA,CAAcC,aAAqB,EAAE5K,OAAe,EAAE;AACpE,IAAA,OAAO1Z,OAAO8P,MAAM,CAAC9P,MAAOyC,CAAAA,MAAM,CAAC6hB,aAAgB5K,CAAAA,EAAAA,OAAAA,CAAAA,CAAAA;AACrD;;AC7LA;;;;;;;;;AASC,IACM,SAAS6K,eAIdC,CAAAA,MAAS,EACTC,QAAW,GAAA;AAAC,IAAA,EAAA;CAAG,EACfC,UAAc,EACdhB,QAA4B,EAC5BiB,YAAY,IAAMH,MAAM,CAAC,CAAA,CAAE,EAC3B;AACA,IAAA,MAAMI,kBAAkBF,UAAcF,IAAAA,MAAAA,CAAAA;IACtC,IAAI,OAAOd,aAAa,WAAa,EAAA;AACnCA,QAAAA,QAAAA,GAAWmB,SAAS,WAAaL,EAAAA,MAAAA,CAAAA,CAAAA;KAClC;AACD,IAAA,MAAM5H,KAA6B,GAAA;QACjC,CAACjV,MAAAA,CAAOmd,WAAW,GAAG,QAAA;AACtBC,QAAAA,UAAAA,EAAY,IAAI;QAChBC,OAASR,EAAAA,MAAAA;QACTS,WAAaL,EAAAA,eAAAA;QACbrR,SAAWmQ,EAAAA,QAAAA;QACXwB,UAAYP,EAAAA,SAAAA;QACZnJ,QAAU,EAAA,CAAC7X,QAAqB4gB,eAAgB,CAAA;AAAC5gB,gBAAAA,KAAAA;AAAU6gB,gBAAAA,GAAAA,MAAAA;AAAO,aAAA,EAAEC,UAAUG,eAAiBlB,EAAAA,QAAAA,CAAAA;AACjG,KAAA,CAAA;IACA,OAAO,IAAIyB,MAAMvI,KAAO,EAAA;AACtB;;AAEC,QACDwI,cAAe5iB,CAAAA,CAAAA,MAAM,EAAE6gB,IAAY,EAAE;AACnC,YAAA,OAAO7gB,MAAM,CAAC6gB,IAAK,CAAA,CAAA;YACnB,OAAO7gB,MAAAA,CAAO6iB,KAAK,CAAA;AACnB,YAAA,OAAOb,MAAM,CAAC,CAAA,CAAE,CAACnB,IAAAA,CAAK;AACtB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AAEA;;AAEC,QACDrO,GAAIxS,CAAAA,CAAAA,MAAM,EAAE6gB,IAAY,EAAE;AACxB,YAAA,OAAOiC,QAAQ9iB,MAAQ6gB,EAAAA,IAAAA,EACrB,IAAMkC,oBAAqBlC,CAAAA,IAAAA,EAAMoB,UAAUD,MAAQhiB,EAAAA,MAAAA,CAAAA,CAAAA,CAAAA;AACvD,SAAA;AAEA;;;AAGC,QACDgjB,wBAAyBhjB,CAAAA,CAAAA,MAAM,EAAE6gB,IAAI,EAAE;AACrC,YAAA,OAAOoC,QAAQD,wBAAwB,CAAChjB,OAAOwiB,OAAO,CAAC,EAAE,EAAE3B,IAAAA,CAAAA,CAAAA;AAC7D,SAAA;AAEA;;AAEC,QACDqC,cAAiB,CAAA,GAAA;AACf,YAAA,OAAOD,OAAQC,CAAAA,cAAc,CAAClB,MAAM,CAAC,CAAE,CAAA,CAAA,CAAA;AACzC,SAAA;AAEA;;AAEC,QACD/e,GAAIjD,CAAAA,CAAAA,MAAM,EAAE6gB,IAAY,EAAE;YACxB,OAAOsC,oBAAAA,CAAqBnjB,MAAQ8T,CAAAA,CAAAA,QAAQ,CAAC+M,IAAAA,CAAAA,CAAAA;AAC/C,SAAA;AAEA;;QAGAuC,OAAAA,CAAAA,CAAQpjB,MAAM,EAAE;AACd,YAAA,OAAOmjB,oBAAqBnjB,CAAAA,MAAAA,CAAAA,CAAAA;AAC9B,SAAA;AAEA;;AAEC,QACD6J,KAAI7J,MAAM,EAAE6gB,IAAY,EAAE1jB,KAAK,EAAE;YAC/B,MAAMkmB,OAAAA,GAAUrjB,OAAOsjB,QAAQ,KAAKtjB,MAAOsjB,CAAAA,QAAQ,GAAGnB,SAAU,EAAA,CAAA,CAAA;YAChEniB,MAAM,CAAC6gB,KAAK,GAAGwC,OAAO,CAACxC,IAAK,CAAA,GAAG1jB;YAC/B,OAAO6C,MAAAA,CAAO6iB,KAAK,CAAA;AACnB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAED;;;;;;;IAQO,SAASU,cAAAA,CAIdC,KAA0B,EAC1BtM,OAAkB,EAClBuM,QAA8B,EAC9BC,kBAAuC,EACvC;AACA,IAAA,MAAMtJ,KAA4B,GAAA;AAChCmI,QAAAA,UAAAA,EAAY,KAAK;QACjBoB,MAAQH,EAAAA,KAAAA;QACRI,QAAU1M,EAAAA,OAAAA;QACV2M,SAAWJ,EAAAA,QAAAA;AACXK,QAAAA,MAAAA,EAAQ,IAAIha,GAAAA,EAAAA;AACZ8M,QAAAA,YAAAA,EAAcA,aAAa4M,KAAOE,EAAAA,kBAAAA,CAAAA;AAClCK,QAAAA,UAAAA,EAAY,CAACjM,GAAAA,GAAmByL,cAAeC,CAAAA,KAAAA,EAAO1L,KAAK2L,QAAUC,EAAAA,kBAAAA,CAAAA;QACrE1K,QAAU,EAAA,CAAC7X,QAAqBoiB,cAAeC,CAAAA,KAAAA,CAAMxK,QAAQ,CAAC7X,KAAAA,CAAAA,EAAQ+V,SAASuM,QAAUC,EAAAA,kBAAAA,CAAAA;AAC3F,KAAA,CAAA;IACA,OAAO,IAAIf,MAAMvI,KAAO,EAAA;AACtB;;AAEC,QACDwI,cAAe5iB,CAAAA,CAAAA,MAAM,EAAE6gB,IAAI,EAAE;AAC3B,YAAA,OAAO7gB,MAAM,CAAC6gB,IAAK,CAAA,CAAA;AACnB,YAAA,OAAO2C,KAAK,CAAC3C,IAAK,CAAA,CAAA;AAClB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AAEA;;AAEC,QACDrO,KAAIxS,MAAM,EAAE6gB,IAAY,EAAEmD,QAAQ,EAAE;AAClC,YAAA,OAAOlB,QAAQ9iB,MAAQ6gB,EAAAA,IAAAA,EACrB,IAAMoD,mBAAAA,CAAoBjkB,QAAQ6gB,IAAMmD,EAAAA,QAAAA,CAAAA,CAAAA,CAAAA;AAC5C,SAAA;AAEA;;;AAGC,QACDhB,wBAAyBhjB,CAAAA,CAAAA,MAAM,EAAE6gB,IAAI,EAAE;YACrC,OAAO7gB,MAAAA,CAAO4W,YAAY,CAACsN,OAAO,GAC9BjB,OAAQhgB,CAAAA,GAAG,CAACugB,KAAAA,EAAO3C,IAAQ,CAAA,GAAA;AAAC3X,gBAAAA,UAAAA,EAAY,IAAI;AAAED,gBAAAA,YAAAA,EAAc,IAAI;AAAA,aAAA,GAAI7L,SAAS,GAC7E6lB,OAAAA,CAAQD,wBAAwB,CAACQ,OAAO3C,IAAK,CAAA,CAAA;AACnD,SAAA;AAEA;;AAEC,QACDqC,cAAiB,CAAA,GAAA;YACf,OAAOD,OAAAA,CAAQC,cAAc,CAACM,KAAAA,CAAAA,CAAAA;AAChC,SAAA;AAEA;;AAEC,QACDvgB,GAAIjD,CAAAA,CAAAA,MAAM,EAAE6gB,IAAI,EAAE;YAChB,OAAOoC,OAAAA,CAAQhgB,GAAG,CAACugB,KAAO3C,EAAAA,IAAAA,CAAAA,CAAAA;AAC5B,SAAA;AAEA;;AAEC,QACDuC,OAAU,CAAA,GAAA;YACR,OAAOH,OAAAA,CAAQG,OAAO,CAACI,KAAAA,CAAAA,CAAAA;AACzB,SAAA;AAEA;;AAEC,QACD3Z,KAAI7J,MAAM,EAAE6gB,IAAI,EAAE1jB,KAAK,EAAE;AACvBqmB,YAAAA,KAAK,CAAC3C,IAAAA,CAAK,GAAG1jB,KAAAA,CAAAA;AACd,YAAA,OAAO6C,MAAM,CAAC6gB,IAAK,CAAA,CAAA;AACnB,YAAA,OAAO,IAAI,CAAA;AACb,SAAA;AACF,KAAA,CAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACM,SAASjK,YACd4M,CAAAA,KAAoB,EACpB/S,QAA+B,GAAA;AAAC0T,IAAAA,UAAAA,EAAY,IAAI;AAAEC,IAAAA,SAAAA,EAAW,IAAI;AAAA,CAAC,EACtD;AACZ,IAAA,MAAM,EAACnT,WAAcR,EAAAA,QAAAA,CAAS0T,UAAU,GAAEnT,UAAaP,EAAAA,QAAAA,CAAS2T,SAAS,GAAEC,QAAW5T,EAAAA,QAAAA,CAASyT,OAAO,GAAC,GAAGV,KAAAA,CAAAA;IAC1G,OAAO;QACLU,OAASG,EAAAA,QAAAA;QACTF,UAAYlT,EAAAA,WAAAA;QACZmT,SAAWpT,EAAAA,UAAAA;AACXsT,QAAAA,YAAAA,EAAc3hB,UAAWsO,CAAAA,WAAAA,CAAAA,GAAeA,WAAc,GAAA,IAAMA,WAAW;AACvEsT,QAAAA,WAAAA,EAAa5hB,UAAWqO,CAAAA,UAAAA,CAAAA,GAAcA,UAAa,GAAA,IAAMA,UAAU;AACrE,KAAA,CAAA;AACF,CAAC;AAED,MAAMwT,OAAAA,GAAU,CAACC,MAAgBvT,EAAAA,IAAAA,GAAiBuT,SAASA,MAASniB,GAAAA,WAAAA,CAAY4O,QAAQA,IAAI,CAAA;AAC5F,MAAMwT,mBAAmB,CAAC7D,IAAAA,EAAc1jB,QAAmBU,QAASV,CAAAA,KAAAA,CAAAA,IAAU0jB,SAAS,UACpFrjB,KAAAA,MAAO0lB,CAAAA,cAAc,CAAC/lB,KAAW,CAAA,KAAA,IAAI,IAAIA,KAAMwZ,CAAAA,WAAW,KAAKnZ,MAAK,CAAA,CAAA;AAEvE,SAASslB,QACP9iB,MAAiB,EACjB6gB,IAAY,EACZO,OAAsB,EACtB;IACA,IAAI5jB,MAAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAAA,EAAQ6gB,IAASA,CAAAA,IAAAA,IAAAA,KAAS,aAAe,EAAA;QAChF,OAAO7gB,MAAM,CAAC6gB,IAAK,CAAA,CAAA;KACpB;AAED,IAAA,MAAM1jB,KAAQikB,GAAAA,OAAAA,EAAAA,CAAAA;;IAEdphB,MAAM,CAAC6gB,KAAK,GAAG1jB,KAAAA,CAAAA;IACf,OAAOA,KAAAA,CAAAA;AACT,CAAA;AAEA,SAAS8mB,oBACPjkB,MAAoB,EACpB6gB,IAAY,EACZmD,QAAmB,EACnB;IACA,MAAM,EAACL,MAAM,GAAEC,QAAQ,GAAEC,YAAWjN,YAAAA,EAAcN,WAAW,GAAC,GAAGtW,MAAAA,CAAAA;AACjE,IAAA,IAAI7C,KAAQwmB,GAAAA,MAAM,CAAC9C,IAAAA,CAAK;;AAGxB,IAAA,IAAIle,UAAWxF,CAAAA,KAAAA,CAAAA,IAAUmZ,WAAYgO,CAAAA,YAAY,CAACzD,IAAO,CAAA,EAAA;QACvD1jB,KAAQwnB,GAAAA,kBAAAA,CAAmB9D,IAAM1jB,EAAAA,KAAAA,EAAO6C,MAAQgkB,EAAAA,QAAAA,CAAAA,CAAAA;KACjD;AACD,IAAA,IAAI3mB,OAAQF,CAAAA,KAAAA,CAAAA,IAAUA,KAAMiC,CAAAA,MAAM,EAAE;AAClCjC,QAAAA,KAAAA,GAAQynB,aAAc/D,CAAAA,IAAAA,EAAM1jB,KAAO6C,EAAAA,MAAAA,EAAQsW,YAAYiO,WAAW,CAAA,CAAA;KACnE;IACD,IAAIG,gBAAAA,CAAiB7D,MAAM1jB,KAAQ,CAAA,EAAA;;AAEjCA,QAAAA,KAAAA,GAAQomB,eAAepmB,KAAOymB,EAAAA,QAAAA,EAAUC,aAAaA,SAAS,CAAChD,KAAK,EAAEvK,WAAAA,CAAAA,CAAAA;KACvE;IACD,OAAOnZ,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASwnB,kBAAAA,CACP9D,IAAY,EACZgE,QAAqD,EACrD7kB,MAAoB,EACpBgkB,QAAmB,EACnB;IACA,MAAM,EAACL,SAAQC,QAAAA,GAAUC,SAAS,GAAEC,MAAM,GAAC,GAAG9jB,MAAAA,CAAAA;IAC9C,IAAI8jB,MAAAA,CAAO7gB,GAAG,CAAC4d,IAAO,CAAA,EAAA;QACpB,MAAM,IAAIiE,KAAM,CAAA,sBAAA,GAAyBxnB,KAAMyM,CAAAA,IAAI,CAAC+Z,MAAAA,CAAAA,CAAQiB,IAAI,CAAC,IAAQ,CAAA,GAAA,IAAA,GAAOlE,IAAM,CAAA,CAAA;KACvF;AACDiD,IAAAA,MAAAA,CAAOlC,GAAG,CAACf,IAAAA,CAAAA,CAAAA;IACX,IAAI1jB,KAAAA,GAAQ0nB,QAASjB,CAAAA,QAAAA,EAAUC,SAAaG,IAAAA,QAAAA,CAAAA,CAAAA;AAC5CF,IAAAA,MAAAA,CAAOkB,MAAM,CAACnE,IAAAA,CAAAA,CAAAA;IACd,IAAI6D,gBAAAA,CAAiB7D,MAAM1jB,KAAQ,CAAA,EAAA;;AAEjCA,QAAAA,KAAAA,GAAQ8nB,iBAAkBtB,CAAAA,MAAAA,CAAOnB,OAAO,EAAEmB,QAAQ9C,IAAM1jB,EAAAA,KAAAA,CAAAA,CAAAA;KACzD;IACD,OAAOA,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASynB,aAAAA,CACP/D,IAAY,EACZ1jB,KAAgB,EAChB6C,MAAoB,EACpBukB,WAAqC,EACrC;IACA,MAAM,EAACZ,MAAM,GAAEC,QAAQ,GAAEC,YAAWjN,YAAAA,EAAcN,WAAW,GAAC,GAAGtW,MAAAA,CAAAA;AAEjE,IAAA,IAAI,OAAO4jB,QAAShkB,CAAAA,KAAK,KAAK,WAAA,IAAe2kB,YAAY1D,IAAO,CAAA,EAAA;AAC9D,QAAA,OAAO1jB,KAAK,CAACymB,QAAAA,CAAShkB,KAAK,GAAGzC,KAAAA,CAAMiC,MAAM,CAAC,CAAA;AAC7C,KAAA,MAAO,IAAIvB,QAAAA,CAASV,KAAK,CAAC,EAAE,CAAG,EAAA;;AAE7B,QAAA,MAAM+nB,GAAM/nB,GAAAA,KAAAA,CAAAA;QACZ,MAAM6kB,MAAAA,GAAS2B,OAAOnB,OAAO,CAAC2C,MAAM,CAAC5d,CAAAA,IAAKA,CAAM2d,KAAAA,GAAAA,CAAAA,CAAAA;AAChD/nB,QAAAA,KAAAA,GAAQ,EAAE,CAAA;QACV,KAAK,MAAM6F,QAAQkiB,GAAK,CAAA;AACtB,YAAA,MAAM7iB,QAAW4iB,GAAAA,iBAAAA,CAAkBjD,MAAQ2B,EAAAA,MAAAA,EAAQ9C,IAAM7d,EAAAA,IAAAA,CAAAA,CAAAA;YACzD7F,KAAM8E,CAAAA,IAAI,CAACshB,cAAelhB,CAAAA,QAAAA,EAAUuhB,UAAUC,SAAaA,IAAAA,SAAS,CAAChD,IAAAA,CAAK,EAAEvK,WAAAA,CAAAA,CAAAA,CAAAA;AAC9E,SAAA;KACD;IACD,OAAOnZ,KAAAA,CAAAA;AACT,CAAA;AAEA,SAASioB,gBACPlE,QAA8F,EAC9FL,IAAuB,EACvB1jB,KAAc,EACd;AACA,IAAA,OAAOwF,UAAWue,CAAAA,QAAAA,CAAAA,GAAYA,QAASL,CAAAA,IAAAA,EAAM1jB,SAAS+jB,QAAQ,CAAA;AAChE,CAAA;AAEA,MAAM3K,QAAW,GAAA,CAAClW,GAAwBglB,EAAAA,MAAAA,GAAsBhlB,QAAQ,IAAI,GAAGglB,MAC3E,GAAA,OAAOhlB,GAAQ,KAAA,QAAA,GAAW+B,gBAAiBijB,CAAAA,MAAAA,EAAQhlB,OAAOjD,SAAS,CAAA;AAEvE,SAASkoB,SAAAA,CACPzb,GAAmB,EACnB0b,YAAyB,EACzBllB,GAAsB,EACtBmlB,cAAiC,EACjCroB,KAAc,EACd;IACA,KAAK,MAAMkoB,UAAUE,YAAc,CAAA;QACjC,MAAMpkB,KAAAA,GAAQoV,SAASlW,GAAKglB,EAAAA,MAAAA,CAAAA,CAAAA;AAC5B,QAAA,IAAIlkB,KAAO,EAAA;AACT0I,YAAAA,GAAAA,CAAI+X,GAAG,CAACzgB,KAAAA,CAAAA,CAAAA;AACR,YAAA,MAAM+f,QAAWkE,GAAAA,eAAAA,CAAgBjkB,KAAM4P,CAAAA,SAAS,EAAE1Q,GAAKlD,EAAAA,KAAAA,CAAAA,CAAAA;AACvD,YAAA,IAAI,OAAO+jB,QAAa,KAAA,WAAA,IAAeA,QAAa7gB,KAAAA,GAAAA,IAAO6gB,aAAasE,cAAgB,EAAA;;;gBAGtF,OAAOtE,QAAAA,CAAAA;aACR;SACI,MAAA,IAAI/f,UAAU,KAAK,IAAI,OAAOqkB,cAAmB,KAAA,WAAA,IAAenlB,QAAQmlB,cAAgB,EAAA;;;AAG7F,YAAA,OAAO,IAAI,CAAA;SACZ;AACH,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAEA,SAASP,iBAAAA,CACPM,YAAyB,EACzBljB,QAAuB,EACvBwe,IAAuB,EACvB1jB,KAAc,EACd;IACA,MAAM+kB,UAAAA,GAAa7f,SAASogB,WAAW,CAAA;AACvC,IAAA,MAAMvB,QAAWkE,GAAAA,eAAAA,CAAgB/iB,QAAS0O,CAAAA,SAAS,EAAE8P,IAAM1jB,EAAAA,KAAAA,CAAAA,CAAAA;AAC3D,IAAA,MAAMsoB,SAAY,GAAA;AAAIF,QAAAA,GAAAA,YAAAA;AAAiBrD,QAAAA,GAAAA,UAAAA;AAAW,KAAA,CAAA;AAClD,IAAA,MAAMrY,MAAM,IAAIC,GAAAA,EAAAA,CAAAA;AAChBD,IAAAA,GAAAA,CAAI+X,GAAG,CAACzkB,KAAAA,CAAAA,CAAAA;AACR,IAAA,IAAIkD,MAAMqlB,gBAAiB7b,CAAAA,GAAAA,EAAK4b,SAAW5E,EAAAA,IAAAA,EAAMK,YAAYL,IAAM1jB,EAAAA,KAAAA,CAAAA,CAAAA;IACnE,IAAIkD,GAAAA,KAAQ,IAAI,EAAE;AAChB,QAAA,OAAO,KAAK,CAAA;KACb;AACD,IAAA,IAAI,OAAO6gB,QAAAA,KAAa,WAAeA,IAAAA,QAAAA,KAAaL,IAAM,EAAA;AACxDxgB,QAAAA,GAAAA,GAAMqlB,gBAAiB7b,CAAAA,GAAAA,EAAK4b,SAAWvE,EAAAA,QAAAA,EAAU7gB,GAAKlD,EAAAA,KAAAA,CAAAA,CAAAA;QACtD,IAAIkD,GAAAA,KAAQ,IAAI,EAAE;AAChB,YAAA,OAAO,KAAK,CAAA;SACb;KACF;AACD,IAAA,OAAO0hB,eAAgBzkB,CAAAA,KAAAA,CAAMyM,IAAI,CAACF,GAAM,CAAA,EAAA;AAAC,QAAA,EAAA;AAAG,KAAA,EAAEqY,UAAYhB,EAAAA,QAAAA,EACxD,IAAMyE,YAAAA,CAAatjB,UAAUwe,IAAgB1jB,EAAAA,KAAAA,CAAAA,CAAAA,CAAAA;AACjD,CAAA;AAEA,SAASuoB,gBAAAA,CACP7b,GAAmB,EACnB4b,SAAsB,EACtBplB,GAAsB,EACtB6gB,QAA2B,EAC3Ble,IAAa,EACb;AACA,IAAA,MAAO3C,GAAK,CAAA;AACVA,QAAAA,GAAAA,GAAMilB,SAAUzb,CAAAA,GAAAA,EAAK4b,SAAWplB,EAAAA,GAAAA,EAAK6gB,QAAUle,EAAAA,IAAAA,CAAAA,CAAAA;AACjD,KAAA;IACA,OAAO3C,GAAAA,CAAAA;AACT,CAAA;AAEA,SAASslB,aACPtjB,QAAuB,EACvBwe,IAAY,EACZ1jB,KAAc,EACd;IACA,MAAMkoB,MAAAA,GAAShjB,SAASqgB,UAAU,EAAA,CAAA;AAClC,IAAA,IAAI,EAAE7B,IAAQwE,IAAAA,MAAK,CAAI,EAAA;QACrBA,MAAM,CAACxE,IAAK,CAAA,GAAG,EAAC,CAAA;KACjB;IACD,MAAM7gB,MAAAA,GAASqlB,MAAM,CAACxE,IAAK,CAAA,CAAA;IAC3B,IAAIxjB,OAAAA,CAAQ2C,MAAWnC,CAAAA,IAAAA,QAAAA,CAASV,KAAQ,CAAA,EAAA;;QAEtC,OAAOA,KAAAA,CAAAA;KACR;AACD,IAAA,OAAO6C,UAAU,EAAC,CAAA;AACpB,CAAA;AAEA,SAAS+iB,oBAAAA,CACPlC,IAAY,EACZoB,QAAkB,EAClBD,MAAmB,EACnBwB,KAAoB,EACpB;IACA,IAAIrmB,KAAAA,CAAAA;IACJ,KAAK,MAAMsnB,UAAUxC,QAAU,CAAA;QAC7B9kB,KAAQklB,GAAAA,QAAAA,CAASmC,OAAQC,CAAAA,MAAAA,EAAQ5D,IAAOmB,CAAAA,EAAAA,MAAAA,CAAAA,CAAAA;QACxC,IAAI,OAAO7kB,UAAU,WAAa,EAAA;YAChC,OAAOunB,gBAAAA,CAAiB7D,MAAM1jB,KAC1B8nB,CAAAA,GAAAA,iBAAAA,CAAkBjD,QAAQwB,KAAO3C,EAAAA,IAAAA,EAAM1jB,SACvCA,KAAK,CAAA;SACV;AACH,KAAA;AACF,CAAA;AAEA,SAASklB,QAAShiB,CAAAA,GAAW,EAAE2hB,MAAmB,EAAE;IAClD,KAAK,MAAM7gB,SAAS6gB,MAAQ,CAAA;AAC1B,QAAA,IAAI,CAAC7gB,KAAO,EAAA;YACV,SAAS;SACV;QACD,MAAMhE,KAAAA,GAAQgE,KAAK,CAACd,GAAI,CAAA,CAAA;QACxB,IAAI,OAAOlD,UAAU,WAAa,EAAA;YAChC,OAAOA,KAAAA,CAAAA;SACR;AACH,KAAA;AACF,CAAA;AAEA,SAASgmB,oBAAAA,CAAqBnjB,MAAqB,EAAE;IACnD,IAAIb,IAAAA,GAAOa,OAAO6iB,KAAK,CAAA;AACvB,IAAA,IAAI,CAAC1jB,IAAM,EAAA;AACTA,QAAAA,IAAAA,GAAOa,MAAO6iB,CAAAA,KAAK,GAAG+C,wBAAAA,CAAyB5lB,OAAOwiB,OAAO,CAAA,CAAA;KAC9D;IACD,OAAOrjB,IAAAA,CAAAA;AACT,CAAA;AAEA,SAASymB,wBAAAA,CAAyB5D,MAAmB,EAAE;AACrD,IAAA,MAAMnY,MAAM,IAAIC,GAAAA,EAAAA,CAAAA;IAChB,KAAK,MAAM3I,SAAS6gB,MAAQ,CAAA;AAC1B,QAAA,KAAK,MAAM3hB,GAAAA,IAAO7C,MAAO2B,CAAAA,IAAI,CAACgC,KAAOgkB,CAAAA,CAAAA,MAAM,CAAChlB,CAAAA,CAAK,GAAA,CAACA,CAAEiW,CAAAA,UAAU,CAAC,GAAO,CAAA,CAAA,CAAA;AACpEvM,YAAAA,GAAAA,CAAI+X,GAAG,CAACvhB,GAAAA,CAAAA,CAAAA;AACV,SAAA;AACF,KAAA;IACA,OAAO/C,KAAAA,CAAMyM,IAAI,CAACF,GAAAA,CAAAA,CAAAA;AACpB,CAAA;AAEO,SAASgc,4BACdra,IAAmC,EACnCoO,IAAiB,EACjBxS,KAAa,EACbwE,KAAa,EACb;IACA,MAAM,EAACE,MAAM,GAAC,GAAGN,IAAAA,CAAAA;AACjB,IAAA,MAAM,EAACnL,GAAM,EAAA,GAAA,GAAI,GAAG,IAAI,CAACylB,QAAQ,CAAA;IACjC,MAAMC,MAAAA,GAAS,IAAIzoB,KAAoBsO,CAAAA,KAAAA,CAAAA,CAAAA;IACvC,IAAI3M,CAAAA,EAAWO,MAAcI,KAAeoD,EAAAA,IAAAA,CAAAA;IAE5C,IAAK/D,CAAAA,GAAI,GAAGO,IAAOoM,GAAAA,KAAK,EAAE3M,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;AACvCW,QAAAA,KAAAA,GAAQX,CAAImI,GAAAA,KAAAA,CAAAA;QACZpE,IAAO4W,GAAAA,IAAI,CAACha,KAAM,CAAA,CAAA;QAClBmmB,MAAM,CAAC9mB,EAAE,GAAG;AACV+mB,YAAAA,CAAAA,EAAGla,MAAOma,CAAAA,KAAK,CAAC7jB,gBAAAA,CAAiBY,MAAM3C,GAAMT,CAAAA,EAAAA,KAAAA,CAAAA;AAC/C,SAAA,CAAA;AACF,KAAA;IACA,OAAOmmB,MAAAA,CAAAA;AACT;;AClcA,MAAMG,OAAAA,GAAUnoB,MAAOmoB,CAAAA,OAAO,IAAI,KAAA,CAAA;AAGlC,MAAMC,WAAW,CAAC1a,MAAAA,EAAuBxM,CAAmCA,GAAAA,CAAAA,GAAIwM,OAAOrM,MAAM,IAAI,CAACqM,MAAM,CAACxM,CAAE,CAAA,CAACmnB,IAAI,IAAI3a,MAAM,CAACxM,CAAE,CAAA,CAAA;AAC7H,MAAMonB,eAAe,CAACpO,SAAAA,GAAyBA,SAAc,KAAA,GAAA,GAAM,MAAM,GAAG,CAAA;AAErE,SAASqO,YACdC,UAAuB,EACvBC,WAAwB,EACxBC,UAAuB,EACvBjZ,CAAS,EAIP;;;;AAMF,IAAA,MAAMpM,QAAWmlB,GAAAA,UAAAA,CAAWH,IAAI,GAAGI,cAAcD,UAAU,CAAA;AAC3D,IAAA,MAAMzlB,OAAU0lB,GAAAA,WAAAA,CAAAA;AAChB,IAAA,MAAME,IAAOD,GAAAA,UAAAA,CAAWL,IAAI,GAAGI,cAAcC,UAAU,CAAA;IACvD,MAAME,GAAAA,GAAM7f,sBAAsBhG,OAASM,EAAAA,QAAAA,CAAAA,CAAAA;IAC3C,MAAMwlB,GAAAA,GAAM9f,sBAAsB4f,IAAM5lB,EAAAA,OAAAA,CAAAA,CAAAA;AAExC,IAAA,IAAI+lB,GAAMF,GAAAA,GAAAA,IAAOA,GAAAA,GAAMC,GAAE,CAAA,CAAA;AACzB,IAAA,IAAIE,GAAMF,GAAAA,GAAAA,IAAOD,GAAAA,GAAMC,GAAE,CAAA,CAAA;;IAGzBC,GAAMvhB,GAAAA,KAAAA,CAAMuhB,GAAO,CAAA,GAAA,CAAA,GAAIA,GAAG,CAAA;IAC1BC,GAAMxhB,GAAAA,KAAAA,CAAMwhB,GAAO,CAAA,GAAA,CAAA,GAAIA,GAAG,CAAA;IAE1B,MAAMC,EAAAA,GAAKvZ,CAAIqZ,GAAAA,GAAAA,CAAAA;AACf,IAAA,MAAMG,KAAKxZ,CAAIsZ,GAAAA,GAAAA,CAAAA;IAEf,OAAO;QACL1lB,QAAU,EAAA;YACRK,CAAGX,EAAAA,OAAAA,CAAQW,CAAC,GAAGslB,EAAML,IAAAA,KAAKjlB,CAAC,GAAGL,QAASK,CAAAA,CAAC,CAADA;YACvCE,CAAGb,EAAAA,OAAAA,CAAQa,CAAC,GAAGolB,EAAML,IAAAA,KAAK/kB,CAAC,GAAGP,QAASO,CAAAA,CAAC,CAADA;AACzC,SAAA;QACA+kB,IAAM,EAAA;YACJjlB,CAAGX,EAAAA,OAAAA,CAAQW,CAAC,GAAGulB,EAAMN,IAAAA,KAAKjlB,CAAC,GAAGL,QAASK,CAAAA,CAAC,CAADA;YACvCE,CAAGb,EAAAA,OAAAA,CAAQa,CAAC,GAAGqlB,EAAMN,IAAAA,KAAK/kB,CAAC,GAAGP,QAASO,CAAAA,CAAC,CAADA;AACzC,SAAA;AACF,KAAA,CAAA;AACF,CAAC;AAED;;AAEC,IACD,SAASslB,cAAexb,CAAAA,MAAqB,EAAEyb,MAAgB,EAAEC,EAAY,EAAE;IAC7E,MAAMC,SAAAA,GAAY3b,OAAOrM,MAAM,CAAA;IAE/B,IAAIioB,MAAAA,EAAgBC,KAAeC,EAAAA,IAAAA,EAAcC,gBAA0BC,EAAAA,YAAAA,CAAAA;IAC3E,IAAIC,UAAAA,GAAavB,SAAS1a,MAAQ,EAAA,CAAA,CAAA,CAAA;AAClC,IAAA,IAAK,IAAIxM,CAAI,GAAA,CAAA,EAAGA,IAAImoB,SAAY,GAAA,CAAA,EAAG,EAAEnoB,CAAG,CAAA;QACtCwoB,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAAS1a,QAAQxM,CAAI,GAAA,CAAA,CAAA,CAAA;QAClC,IAAI,CAACwoB,YAAgB,IAAA,CAACC,UAAY,EAAA;YAChC,SAAS;SACV;AAED,QAAA,IAAI1jB,aAAakjB,MAAM,CAACjoB,CAAE,CAAA,EAAE,GAAGinB,OAAU,CAAA,EAAA;AACvCiB,YAAAA,EAAE,CAACloB,CAAE,CAAA,GAAGkoB,EAAE,CAACloB,CAAAA,GAAI,EAAE,GAAG,CAAA,CAAA;YACpB,SAAS;SACV;AAEDooB,QAAAA,MAAAA,GAASF,EAAE,CAACloB,CAAAA,CAAE,GAAGioB,MAAM,CAACjoB,CAAE,CAAA,CAAA;AAC1BqoB,QAAAA,KAAAA,GAAQH,EAAE,CAACloB,CAAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE,CAAA,CAAA;QAC7BuoB,gBAAmBnkB,GAAAA,IAAAA,CAAKmB,GAAG,CAAC6iB,MAAAA,EAAQ,KAAKhkB,IAAKmB,CAAAA,GAAG,CAAC8iB,KAAO,EAAA,CAAA,CAAA,CAAA;AACzD,QAAA,IAAIE,oBAAoB,CAAG,EAAA;YACzB,SAAS;SACV;QAEDD,IAAO,GAAA,CAAA,GAAIlkB,IAAKyB,CAAAA,IAAI,CAAC0iB,gBAAAA,CAAAA,CAAAA;AACrBL,QAAAA,EAAE,CAACloB,CAAE,CAAA,GAAGooB,SAASE,IAAOL,GAAAA,MAAM,CAACjoB,CAAE,CAAA,CAAA;QACjCkoB,EAAE,CAACloB,IAAI,CAAE,CAAA,GAAGqoB,QAAQC,IAAOL,GAAAA,MAAM,CAACjoB,CAAE,CAAA,CAAA;AACtC,KAAA;AACF,CAAA;AAEA,SAAS0oB,gBAAgBlc,MAAqB,EAAE0b,EAAY,EAAElP,SAAAA,GAAuB,GAAG,EAAE;AACxF,IAAA,MAAM2P,YAAYvB,YAAapO,CAAAA,SAAAA,CAAAA,CAAAA;IAC/B,MAAMmP,SAAAA,GAAY3b,OAAOrM,MAAM,CAAA;AAC/B,IAAA,IAAIgU,OAAeyU,WAAkCJ,EAAAA,YAAAA,CAAAA;IACrD,IAAIC,UAAAA,GAAavB,SAAS1a,MAAQ,EAAA,CAAA,CAAA,CAAA;AAElC,IAAA,IAAK,IAAIxM,CAAI,GAAA,CAAA,EAAGA,CAAImoB,GAAAA,SAAAA,EAAW,EAAEnoB,CAAG,CAAA;QAClC4oB,WAAcJ,GAAAA,YAAAA,CAAAA;QACdA,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAAS1a,QAAQxM,CAAI,GAAA,CAAA,CAAA,CAAA;AAClC,QAAA,IAAI,CAACwoB,YAAc,EAAA;YACjB,SAAS;SACV;QAED,MAAMK,MAAAA,GAASL,YAAY,CAACxP,SAAU,CAAA,CAAA;QACtC,MAAM8P,MAAAA,GAASN,YAAY,CAACG,SAAU,CAAA,CAAA;AACtC,QAAA,IAAIC,WAAa,EAAA;AACfzU,YAAAA,KAAAA,GAAQ,CAAC0U,MAAAA,GAASD,WAAW,CAAC5P,SAAAA,CAAU,IAAI,CAAA,CAAA;YAC5CwP,YAAY,CAAC,CAAC,GAAG,EAAExP,UAAU,CAAC,CAAC,GAAG6P,MAAS1U,GAAAA,KAAAA,CAAAA;AAC3CqU,YAAAA,YAAY,CAAC,CAAC,GAAG,EAAEG,SAAU,CAAA,CAAC,CAAC,GAAGG,MAAS3U,GAAAA,KAAAA,GAAQ+T,EAAE,CAACloB,CAAE,CAAA,CAAA;SACzD;AACD,QAAA,IAAIyoB,UAAY,EAAA;AACdtU,YAAAA,KAAAA,GAAQ,CAACsU,UAAU,CAACzP,SAAU,CAAA,GAAG6P,MAAK,IAAK,CAAA,CAAA;YAC3CL,YAAY,CAAC,CAAC,GAAG,EAAExP,UAAU,CAAC,CAAC,GAAG6P,MAAS1U,GAAAA,KAAAA,CAAAA;AAC3CqU,YAAAA,YAAY,CAAC,CAAC,GAAG,EAAEG,SAAU,CAAA,CAAC,CAAC,GAAGG,MAAS3U,GAAAA,KAAAA,GAAQ+T,EAAE,CAACloB,CAAE,CAAA,CAAA;SACzD;AACH,KAAA;AACF,CAAA;AAEA;;;;;AAKC,IACM,SAAS+oB,mBAAAA,CAAoBvc,MAAqB,EAAEwM,SAAAA,GAAuB,GAAG,EAAE;AACrF,IAAA,MAAM2P,YAAYvB,YAAapO,CAAAA,SAAAA,CAAAA,CAAAA;IAC/B,MAAMmP,SAAAA,GAAY3b,OAAOrM,MAAM,CAAA;AAC/B,IAAA,MAAM8nB,MAAmB5pB,GAAAA,KAAAA,CAAM8pB,SAAWtK,CAAAA,CAAAA,IAAI,CAAC,CAAA,CAAA,CAAA;AAC/C,IAAA,MAAMqK,KAAe7pB,KAAM8pB,CAAAA,SAAAA,CAAAA,CAAAA;;AAG3B,IAAA,IAAInoB,GAAG4oB,WAAkCJ,EAAAA,YAAAA,CAAAA;IACzC,IAAIC,UAAAA,GAAavB,SAAS1a,MAAQ,EAAA,CAAA,CAAA,CAAA;AAElC,IAAA,IAAKxM,CAAI,GAAA,CAAA,EAAGA,CAAImoB,GAAAA,SAAAA,EAAW,EAAEnoB,CAAG,CAAA;QAC9B4oB,WAAcJ,GAAAA,YAAAA,CAAAA;QACdA,YAAeC,GAAAA,UAAAA,CAAAA;QACfA,UAAavB,GAAAA,QAAAA,CAAS1a,QAAQxM,CAAI,GAAA,CAAA,CAAA,CAAA;AAClC,QAAA,IAAI,CAACwoB,YAAc,EAAA;YACjB,SAAS;SACV;AAED,QAAA,IAAIC,UAAY,EAAA;AACd,YAAA,MAAMO,aAAaP,UAAU,CAACzP,UAAU,GAAGwP,YAAY,CAACxP,SAAU,CAAA,CAAA;;AAGlEiP,YAAAA,MAAM,CAACjoB,CAAE,CAAA,GAAGgpB,UAAe,KAAA,CAAA,GAAI,CAACP,UAAU,CAACE,SAAAA,CAAU,GAAGH,YAAY,CAACG,UAAU,IAAIK,aAAa,CAAC,CAAA;SAClG;AACDd,QAAAA,EAAE,CAACloB,CAAE,CAAA,GAAG,CAAC4oB,WAAcX,GAAAA,MAAM,CAACjoB,CAAE,CAAA,GAC5B,CAACyoB,UAAAA,GAAaR,MAAM,CAACjoB,CAAAA,GAAI,EAAE,GACxB8E,KAAKmjB,MAAM,CAACjoB,CAAI,GAAA,CAAA,CAAE,MAAM8E,IAAKmjB,CAAAA,MAAM,CAACjoB,CAAE,CAAA,CAAA,GAAK,IAC1C,CAACioB,MAAM,CAACjoB,CAAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE,CAAD,IAAK,CAAC,CAAA;AACzC,KAAA;AAEAgoB,IAAAA,cAAAA,CAAexb,QAAQyb,MAAQC,EAAAA,EAAAA,CAAAA,CAAAA;AAE/BQ,IAAAA,eAAAA,CAAgBlc,QAAQ0b,EAAIlP,EAAAA,SAAAA,CAAAA,CAAAA;AAC9B,CAAC;AAED,SAASiQ,gBAAgBC,EAAU,EAAEviB,GAAW,EAAEC,GAAW,EAAE;AAC7D,IAAA,OAAOxC,KAAKwC,GAAG,CAACxC,KAAKuC,GAAG,CAACuiB,IAAItiB,GAAMD,CAAAA,EAAAA,GAAAA,CAAAA,CAAAA;AACrC,CAAA;AAEA,SAASwiB,eAAgB3c,CAAAA,MAAqB,EAAEyR,IAAe,EAAE;IAC/D,IAAIje,CAAAA,EAAGO,IAAMkN,EAAAA,KAAAA,EAAO2b,MAAQC,EAAAA,UAAAA,CAAAA;AAC5B,IAAA,IAAIC,UAAatL,GAAAA,cAAAA,CAAexR,MAAM,CAAC,EAAE,EAAEyR,IAAAA,CAAAA,CAAAA;IAC3C,IAAKje,CAAAA,GAAI,GAAGO,IAAOiM,GAAAA,MAAAA,CAAOrM,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;QAC/CqpB,UAAaD,GAAAA,MAAAA,CAAAA;QACbA,MAASE,GAAAA,UAAAA,CAAAA;QACTA,UAAatpB,GAAAA,CAAAA,GAAIO,OAAO,CAAKyd,IAAAA,cAAAA,CAAexR,MAAM,CAACxM,CAAAA,GAAI,EAAE,EAAEie,IAAAA,CAAAA,CAAAA;AAC3D,QAAA,IAAI,CAACmL,MAAQ,EAAA;YACX,SAAS;SACV;QACD3b,KAAQjB,GAAAA,MAAM,CAACxM,CAAE,CAAA,CAAA;AACjB,QAAA,IAAIqpB,UAAY,EAAA;YACd5b,KAAMiR,CAAAA,IAAI,GAAGuK,eAAAA,CAAgBxb,KAAMiR,CAAAA,IAAI,EAAET,IAAK/R,CAAAA,IAAI,EAAE+R,IAAAA,CAAK9R,KAAK,CAAA,CAAA;YAC9DsB,KAAMmR,CAAAA,IAAI,GAAGqK,eAAAA,CAAgBxb,KAAMmR,CAAAA,IAAI,EAAEX,IAAKpL,CAAAA,GAAG,EAAEoL,IAAAA,CAAKnL,MAAM,CAAA,CAAA;SAC/D;AACD,QAAA,IAAIwW,UAAY,EAAA;YACd7b,KAAMkR,CAAAA,IAAI,GAAGsK,eAAAA,CAAgBxb,KAAMkR,CAAAA,IAAI,EAAEV,IAAK/R,CAAAA,IAAI,EAAE+R,IAAAA,CAAK9R,KAAK,CAAA,CAAA;YAC9DsB,KAAMoR,CAAAA,IAAI,GAAGoK,eAAAA,CAAgBxb,KAAMoR,CAAAA,IAAI,EAAEZ,IAAKpL,CAAAA,GAAG,EAAEoL,IAAAA,CAAKnL,MAAM,CAAA,CAAA;SAC/D;AACH,KAAA;AACF,CAAA;AAEA;;AAEC,IACM,SAASyW,0BACd/c,CAAAA,MAAqB,EACrBjL,OAAO,EACP0c,IAAe,EACftM,IAAa,EACbqH,SAAoB,EACpB;IACA,IAAIhZ,CAAAA,EAAWO,MAAckN,KAAoB+b,EAAAA,aAAAA,CAAAA;;IAGjD,IAAIjoB,OAAAA,CAAQyL,QAAQ,EAAE;AACpBR,QAAAA,MAAAA,GAASA,OAAO0Z,MAAM,CAAC,CAACgD,EAAO,GAAA,CAACA,GAAG/B,IAAI,CAAA,CAAA;KACxC;IAED,IAAI5lB,OAAAA,CAAQkoB,sBAAsB,KAAK,UAAY,EAAA;AACjDV,QAAAA,mBAAAA,CAAoBvc,MAAQwM,EAAAA,SAAAA,CAAAA,CAAAA;KACvB,MAAA;QACL,IAAI0Q,IAAAA,GAAO/X,IAAOnF,GAAAA,MAAM,CAACA,MAAAA,CAAOrM,MAAM,GAAG,CAAE,CAAA,GAAGqM,MAAM,CAAC,CAAE,CAAA,CAAA;QACvD,IAAKxM,CAAAA,GAAI,GAAGO,IAAOiM,GAAAA,MAAAA,CAAOrM,MAAM,EAAEH,CAAAA,GAAIO,IAAM,EAAA,EAAEP,CAAG,CAAA;YAC/CyN,KAAQjB,GAAAA,MAAM,CAACxM,CAAE,CAAA,CAAA;YACjBwpB,aAAgBnC,GAAAA,WAAAA,CACdqC,MACAjc,KACAjB,EAAAA,MAAM,CAACpI,IAAKuC,CAAAA,GAAG,CAAC3G,CAAI,GAAA,CAAA,EAAGO,QAAQoR,IAAAA,GAAO,IAAI,CAAA,KAAMpR,IAAK,CAAA,EACrDgB,QAAQooB,OAAO,CAAA,CAAA;AAEjBlc,YAAAA,KAAAA,CAAMiR,IAAI,GAAG8K,aAAcrnB,CAAAA,QAAQ,CAACK,CAAC,CAAA;AACrCiL,YAAAA,KAAAA,CAAMmR,IAAI,GAAG4K,aAAcrnB,CAAAA,QAAQ,CAACO,CAAC,CAAA;AACrC+K,YAAAA,KAAAA,CAAMkR,IAAI,GAAG6K,aAAc/B,CAAAA,IAAI,CAACjlB,CAAC,CAAA;AACjCiL,YAAAA,KAAAA,CAAMoR,IAAI,GAAG2K,aAAc/B,CAAAA,IAAI,CAAC/kB,CAAC,CAAA;YACjCgnB,IAAOjc,GAAAA,KAAAA,CAAAA;AACT,SAAA;KACD;IAED,IAAIlM,OAAAA,CAAQ4nB,eAAe,EAAE;AAC3BA,QAAAA,eAAAA,CAAgB3c,MAAQyR,EAAAA,IAAAA,CAAAA,CAAAA;KACzB;AACH;;ACzNA;;IAGO,SAAS2L,eAA2B,GAAA;AACzC,IAAA,OAAO,OAAOxe,MAAAA,KAAW,WAAe,IAAA,OAAOye,QAAa,KAAA,WAAA,CAAA;AAC9D,CAAC;AAED;;AAEC,IACM,SAASC,cAAeC,CAAAA,OAA0B,EAAqB;IAC5E,IAAI3D,MAAAA,GAAS2D,QAAQC,UAAU,CAAA;AAC/B,IAAA,IAAI5D,MAAUA,IAAAA,MAAAA,CAAO3nB,QAAQ,EAAA,KAAO,qBAAuB,EAAA;QACzD2nB,MAAS,GAACA,OAAsB6D,IAAI,CAAA;KACrC;IACD,OAAO7D,MAAAA,CAAAA;AACT,CAAC;AAED;;;AAGC,IAED,SAAS8D,aAAcC,CAAAA,UAA2B,EAAE5S,IAAiB,EAAE6S,cAAsB,EAAE;IAC7F,IAAIC,aAAAA,CAAAA;IACJ,IAAI,OAAOF,eAAe,QAAU,EAAA;AAClCE,QAAAA,aAAAA,GAAgBnI,SAASiI,UAAY,EAAA,EAAA,CAAA,CAAA;AAErC,QAAA,IAAIA,UAAW9oB,CAAAA,OAAO,CAAC,GAAA,CAAA,KAAS,CAAC,CAAG,EAAA;;AAElCgpB,YAAAA,aAAAA,GAAgB,aAAiB,GAAA,GAAA,GAAO9S,IAAKyS,CAAAA,UAAU,CAACI,cAAe,CAAA,CAAA;SACxE;KACI,MAAA;QACLC,aAAgBF,GAAAA,UAAAA,CAAAA;KACjB;IAED,OAAOE,aAAAA,CAAAA;AACT,CAAA;AAEA,MAAMC,gBAAAA,GAAmB,CAACC,OAAAA,GACxBA,OAAQC,CAAAA,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAAA,EAAS,IAAI,CAAA,CAAA;AAE3D,SAASG,QAAAA,CAASC,EAAe,EAAEjkB,QAAgB,EAAU;IAClE,OAAO4jB,gBAAAA,CAAiBK,EAAIC,CAAAA,CAAAA,gBAAgB,CAAClkB,QAAAA,CAAAA,CAAAA;AAC/C,CAAC;AAED,MAAMmkB,SAAY,GAAA;AAAC,IAAA,KAAA;AAAO,IAAA,OAAA;AAAS,IAAA,QAAA;AAAU,IAAA,MAAA;AAAO,CAAA,CAAA;AACpD,SAASC,mBAAmBC,MAA2B,EAAEvS,KAAa,EAAEwS,MAAe,EAAa;AAClG,IAAA,MAAMplB,SAAS,EAAC,CAAA;IAChBolB,MAASA,GAAAA,MAAAA,GAAS,GAAMA,GAAAA,MAAAA,GAAS,EAAE,CAAA;AACnC,IAAA,IAAK,IAAIhrB,CAAAA,GAAI,CAAGA,EAAAA,CAAAA,GAAI,GAAGA,CAAK,EAAA,CAAA;QAC1B,MAAMirB,GAAAA,GAAMJ,SAAS,CAAC7qB,CAAE,CAAA,CAAA;QACxB4F,MAAM,CAACqlB,GAAI,CAAA,GAAG3rB,UAAWyrB,CAAAA,MAAM,CAACvS,KAAQ,GAAA,GAAA,GAAMyS,GAAMD,GAAAA,MAAAA,CAAO,CAAK,IAAA,CAAA,CAAA;AAClE,KAAA;AACAplB,IAAAA,MAAAA,CAAOoQ,KAAK,GAAGpQ,MAAAA,CAAOsG,IAAI,GAAGtG,OAAOuG,KAAK,CAAA;AACzCvG,IAAAA,MAAAA,CAAOyW,MAAM,GAAGzW,MAAAA,CAAOiN,GAAG,GAAGjN,OAAOkN,MAAM,CAAA;IAC1C,OAAOlN,MAAAA,CAAAA;AACT,CAAA;AAEA,MAAMslB,eAAe,CAAC1oB,CAAAA,EAAWE,GAAW3B,MAC1C,GAACyB,CAAAA,CAAI,GAAA,CAAA,IAAKE,IAAI,CAAA,MAAO,CAAC3B,MAAAA,IAAU,CAAC,MAACA,CAAuBoqB,UAAU,CAAD,CAAA;AAEpE;;;;AAIC,IACD,SAASC,iBAAAA,CACPlnB,CAAkC,EAClC+X,MAAyB,EAKvB;IACF,MAAMoP,OAAAA,GAAU,CAACnnB,CAAiBmnB,OAAO,CAAA;IACzC,MAAMxqB,MAAAA,GAAUwqB,WAAWA,OAAQlrB,CAAAA,MAAM,GAAGkrB,OAAO,CAAC,CAAE,CAAA,GAAGnnB,CAAC,CAAA;AAC1D,IAAA,MAAM,EAAConB,OAAAA,GAASC,OAAAA,GAAQ,GAAG1qB,MAAAA,CAAAA;AAC3B,IAAA,IAAI2qB,MAAM,KAAK,CAAA;AACf,IAAA,IAAIhpB,CAAGE,EAAAA,CAAAA,CAAAA;AACP,IAAA,IAAIwoB,YAAaI,CAAAA,OAAAA,EAASC,OAASrnB,EAAAA,CAAAA,CAAEnD,MAAM,CAAG,EAAA;QAC5CyB,CAAI8oB,GAAAA,OAAAA,CAAAA;QACJ5oB,CAAI6oB,GAAAA,OAAAA,CAAAA;KACC,MAAA;QACL,MAAM3N,IAAAA,GAAO3B,OAAOwP,qBAAqB,EAAA,CAAA;AACzCjpB,QAAAA,CAAAA,GAAI3B,MAAO6qB,CAAAA,OAAO,GAAG9N,IAAAA,CAAK1R,IAAI,CAAA;AAC9BxJ,QAAAA,CAAAA,GAAI7B,MAAO8qB,CAAAA,OAAO,GAAG/N,IAAAA,CAAK/K,GAAG,CAAA;AAC7B2Y,QAAAA,GAAAA,GAAM,IAAI,CAAA;KACX;IACD,OAAO;AAAChpB,QAAAA,CAAAA;AAAGE,QAAAA,CAAAA;AAAG8oB,QAAAA,GAAAA;AAAG,KAAA,CAAA;AACnB,CAAA;AAEA;;;;;AAKC,IAEM,SAASI,mBAAAA,CACdC,KAAmD,EACnD5X,KAA2B,EACD;AAC1B,IAAA,IAAI,YAAY4X,KAAO,EAAA;QACrB,OAAOA,KAAAA,CAAAA;KACR;AAED,IAAA,MAAM,EAAC5P,MAAAA,GAAQH,uBAAAA,GAAwB,GAAG7H,KAAAA,CAAAA;AAC1C,IAAA,MAAMuE,QAAQ8R,gBAAiBrO,CAAAA,MAAAA,CAAAA,CAAAA;IAC/B,MAAM6P,SAAAA,GAAYtT,KAAMuT,CAAAA,SAAS,KAAK,YAAA,CAAA;IACtC,MAAMC,QAAAA,GAAWlB,mBAAmBtS,KAAO,EAAA,SAAA,CAAA,CAAA;IAC3C,MAAMyT,OAAAA,GAAUnB,kBAAmBtS,CAAAA,KAAAA,EAAO,QAAU,EAAA,OAAA,CAAA,CAAA;IACpD,MAAM,EAAChW,IAAGE,CAAAA,GAAG8oB,GAAG,GAAC,GAAGJ,iBAAAA,CAAkBS,KAAO5P,EAAAA,MAAAA,CAAAA,CAAAA;IAC7C,MAAMQ,OAAAA,GAAUuP,SAAS9f,IAAI,IAAIsf,GAAOS,IAAAA,OAAAA,CAAQ/f,IAAI,CAAD,CAAA;IACnD,MAAMwQ,OAAAA,GAAUsP,SAASnZ,GAAG,IAAI2Y,GAAOS,IAAAA,OAAAA,CAAQpZ,GAAG,CAAD,CAAA;AAEjD,IAAA,IAAI,EAACmD,KAAAA,GAAOqG,MAAAA,GAAO,GAAGpI,KAAAA,CAAAA;AACtB,IAAA,IAAI6X,SAAW,EAAA;AACb9V,QAAAA,KAAAA,IAASgW,QAAShW,CAAAA,KAAK,GAAGiW,OAAAA,CAAQjW,KAAK,CAAA;AACvCqG,QAAAA,MAAAA,IAAU2P,QAAS3P,CAAAA,MAAM,GAAG4P,OAAAA,CAAQ5P,MAAM,CAAA;KAC3C;IACD,OAAO;QACL7Z,CAAG4B,EAAAA,IAAAA,CAAKiB,KAAK,CAAC,CAAC7C,CAAIia,GAAAA,OAAM,IAAKzG,KAAAA,GAAQiG,MAAOjG,CAAAA,KAAK,GAAG8F,uBAAAA,CAAAA;QACrDpZ,CAAG0B,EAAAA,IAAAA,CAAKiB,KAAK,CAAC,CAAC3C,CAAIga,GAAAA,OAAM,IAAKL,MAAAA,GAASJ,MAAOI,CAAAA,MAAM,GAAGP,uBAAAA,CAAAA;AACzD,KAAA,CAAA;AACF,CAAC;AAED,SAASoQ,iBAAiBjQ,MAAyB,EAAEjG,KAAa,EAAEqG,MAAc,EAAkB;AAClG,IAAA,IAAIoE,QAAkB0L,EAAAA,SAAAA,CAAAA;IAEtB,IAAInW,KAAAA,KAAU7X,SAAake,IAAAA,MAAAA,KAAWle,SAAW,EAAA;QAC/C,MAAMiuB,SAAAA,GAAYnQ,UAAU6N,cAAe7N,CAAAA,MAAAA,CAAAA,CAAAA;AAC3C,QAAA,IAAI,CAACmQ,SAAW,EAAA;AACdpW,YAAAA,KAAAA,GAAQiG,OAAOoQ,WAAW,CAAA;AAC1BhQ,YAAAA,MAAAA,GAASJ,OAAOqQ,YAAY,CAAA;SACvB,MAAA;AACL,YAAA,MAAM1O,IAAOwO,GAAAA,SAAAA,CAAUX,qBAAqB,EAAA,CAAA;AAC5C,YAAA,MAAMc,iBAAiBjC,gBAAiB8B,CAAAA,SAAAA,CAAAA,CAAAA;YACxC,MAAMI,eAAAA,GAAkB1B,kBAAmByB,CAAAA,cAAAA,EAAgB,QAAU,EAAA,OAAA,CAAA,CAAA;YACrE,MAAME,gBAAAA,GAAmB3B,mBAAmByB,cAAgB,EAAA,SAAA,CAAA,CAAA;AAC5DvW,YAAAA,KAAAA,GAAQ4H,KAAK5H,KAAK,GAAGyW,iBAAiBzW,KAAK,GAAGwW,gBAAgBxW,KAAK,CAAA;AACnEqG,YAAAA,MAAAA,GAASuB,KAAKvB,MAAM,GAAGoQ,iBAAiBpQ,MAAM,GAAGmQ,gBAAgBnQ,MAAM,CAAA;AACvEoE,YAAAA,QAAAA,GAAWyJ,aAAcqC,CAAAA,cAAAA,CAAe9L,QAAQ,EAAE2L,SAAW,EAAA,aAAA,CAAA,CAAA;AAC7DD,YAAAA,SAAAA,GAAYjC,aAAcqC,CAAAA,cAAAA,CAAeJ,SAAS,EAAEC,SAAW,EAAA,cAAA,CAAA,CAAA;SAChE;KACF;IACD,OAAO;AACLpW,QAAAA,KAAAA;AACAqG,QAAAA,MAAAA;AACAoE,QAAAA,QAAAA,EAAUA,QAAYlc,IAAAA,QAAAA;AACtB4nB,QAAAA,SAAAA,EAAWA,SAAa5nB,IAAAA,QAAAA;AAC1B,KAAA,CAAA;AACF,CAAA;AAEA,MAAMmoB,SAAS,CAACnqB,CAAAA,GAAc6B,KAAKiB,KAAK,CAAC9C,IAAI,EAAM,CAAA,GAAA,EAAA,CAAA;AAEnD;AACO,SAASoqB,eACd1Q,MAAyB,EACzB2Q,OAAgB,EAChBC,QAAiB,EACjBC,WAAoB,EACe;AACnC,IAAA,MAAMtU,QAAQ8R,gBAAiBrO,CAAAA,MAAAA,CAAAA,CAAAA;IAC/B,MAAM8Q,OAAAA,GAAUjC,mBAAmBtS,KAAO,EAAA,QAAA,CAAA,CAAA;AAC1C,IAAA,MAAMiI,WAAWyJ,aAAc1R,CAAAA,KAAAA,CAAMiI,QAAQ,EAAExE,QAAQ,aAAkB1X,CAAAA,IAAAA,QAAAA,CAAAA;AACzE,IAAA,MAAM4nB,YAAYjC,aAAc1R,CAAAA,KAAAA,CAAM2T,SAAS,EAAElQ,QAAQ,cAAmB1X,CAAAA,IAAAA,QAAAA,CAAAA;IAC5E,MAAMyoB,aAAAA,GAAgBd,gBAAiBjQ,CAAAA,MAAAA,EAAQ2Q,OAASC,EAAAA,QAAAA,CAAAA,CAAAA;AACxD,IAAA,IAAI,EAAC7W,KAAAA,GAAOqG,MAAAA,GAAO,GAAG2Q,aAAAA,CAAAA;IAEtB,IAAIxU,KAAAA,CAAMuT,SAAS,KAAK,aAAe,EAAA;QACrC,MAAME,OAAAA,GAAUnB,kBAAmBtS,CAAAA,KAAAA,EAAO,QAAU,EAAA,OAAA,CAAA,CAAA;QACpD,MAAMwT,QAAAA,GAAWlB,mBAAmBtS,KAAO,EAAA,SAAA,CAAA,CAAA;AAC3CxC,QAAAA,KAAAA,IAASgW,QAAShW,CAAAA,KAAK,GAAGiW,OAAAA,CAAQjW,KAAK,CAAA;AACvCqG,QAAAA,MAAAA,IAAU2P,QAAS3P,CAAAA,MAAM,GAAG4P,OAAAA,CAAQ5P,MAAM,CAAA;KAC3C;AACDrG,IAAAA,KAAAA,GAAQ5R,KAAKwC,GAAG,CAAC,CAAGoP,EAAAA,KAAAA,GAAQ+W,QAAQ/W,KAAK,CAAA,CAAA;IACzCqG,MAASjY,GAAAA,IAAAA,CAAKwC,GAAG,CAAC,CAAA,EAAGkmB,cAAc9W,KAAQ8W,GAAAA,WAAAA,GAAczQ,MAAS0Q,GAAAA,OAAAA,CAAQ1Q,MAAM,CAAA,CAAA;AAChFrG,IAAAA,KAAAA,GAAQ0W,OAAOtoB,IAAKuC,CAAAA,GAAG,CAACqP,KAAOyK,EAAAA,QAAAA,EAAUuM,cAAcvM,QAAQ,CAAA,CAAA,CAAA;AAC/DpE,IAAAA,MAAAA,GAASqQ,OAAOtoB,IAAKuC,CAAAA,GAAG,CAAC0V,MAAQ8P,EAAAA,SAAAA,EAAWa,cAAcb,SAAS,CAAA,CAAA,CAAA;IACnE,IAAInW,KAAAA,IAAS,CAACqG,MAAQ,EAAA;;;AAGpBA,QAAAA,MAAAA,GAASqQ,OAAO1W,KAAQ,GAAA,CAAA,CAAA,CAAA;KACzB;IAED,MAAMiX,cAAAA,GAAiBL,OAAYzuB,KAAAA,SAAAA,IAAa0uB,QAAa1uB,KAAAA,SAAAA,CAAAA;IAE7D,IAAI8uB,cAAAA,IAAkBH,eAAeE,aAAc3Q,CAAAA,MAAM,IAAIA,MAAS2Q,GAAAA,aAAAA,CAAc3Q,MAAM,EAAE;AAC1FA,QAAAA,MAAAA,GAAS2Q,cAAc3Q,MAAM,CAAA;AAC7BrG,QAAAA,KAAAA,GAAQ0W,MAAOtoB,CAAAA,IAAAA,CAAKoB,KAAK,CAAC6W,MAASyQ,GAAAA,WAAAA,CAAAA,CAAAA,CAAAA;KACpC;IAED,OAAO;AAAC9W,QAAAA,KAAAA;AAAOqG,QAAAA,MAAAA;AAAM,KAAA,CAAA;AACvB,CAAC;AAED;;;;;IAMO,SAAS6Q,WACdjZ,CAAAA,KAA2B,EAC3BkZ,UAAkB,EAClBC,UAAoB,EACJ;AAChB,IAAA,MAAMC,aAAaF,UAAc,IAAA,CAAA,CAAA;AACjC,IAAA,MAAMG,eAAelpB,IAAKoB,CAAAA,KAAK,CAACyO,KAAAA,CAAMoI,MAAM,GAAGgR,UAAAA,CAAAA,CAAAA;AAC/C,IAAA,MAAME,cAAcnpB,IAAKoB,CAAAA,KAAK,CAACyO,KAAAA,CAAM+B,KAAK,GAAGqX,UAAAA,CAAAA,CAAAA;AAE5CpZ,IAAAA,KAAAA,CAAuBoI,MAAM,GAAGjY,IAAAA,CAAKoB,KAAK,CAACyO,MAAMoI,MAAM,CAAA,CAAA;AACvDpI,IAAAA,KAAAA,CAAuB+B,KAAK,GAAG5R,IAAAA,CAAKoB,KAAK,CAACyO,MAAM+B,KAAK,CAAA,CAAA;IAEtD,MAAMiG,MAAAA,GAAShI,MAAMgI,MAAM,CAAA;;;;AAK3B,IAAA,IAAIA,OAAOzD,KAAK,KAAK4U,UAAAA,IAAe,CAACnR,MAAOzD,CAAAA,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAAA,CAAOzD,KAAK,CAACxC,KAAK,CAAI,EAAA;QACjFiG,MAAOzD,CAAAA,KAAK,CAAC6D,MAAM,GAAG,CAAC,EAAEpI,KAAMoI,CAAAA,MAAM,CAAC,EAAE,CAAC,CAAA;QACzCJ,MAAOzD,CAAAA,KAAK,CAACxC,KAAK,GAAG,CAAC,EAAE/B,KAAM+B,CAAAA,KAAK,CAAC,EAAE,CAAC,CAAA;KACxC;IAED,IAAI/B,KAAAA,CAAM6H,uBAAuB,KAAKuR,UAC/BpR,IAAAA,MAAAA,CAAOI,MAAM,KAAKiR,YAClBrR,IAAAA,MAAAA,CAAOjG,KAAK,KAAKuX,WAAa,EAAA;AAClCtZ,QAAAA,KAAAA,CAAuB6H,uBAAuB,GAAGuR,UAAAA,CAAAA;AAClDpR,QAAAA,MAAAA,CAAOI,MAAM,GAAGiR,YAAAA,CAAAA;AAChBrR,QAAAA,MAAAA,CAAOjG,KAAK,GAAGuX,WAAAA,CAAAA;QACftZ,KAAM4E,CAAAA,GAAG,CAAC2U,YAAY,CAACH,YAAY,CAAG,EAAA,CAAA,EAAGA,YAAY,CAAG,EAAA,CAAA,CAAA,CAAA;AACxD,QAAA,OAAO,IAAI,CAAA;KACZ;AACD,IAAA,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;;;IAKaI,MAAAA,4BAAAA,GAAgC,WAAW;AACtD,IAAA,IAAIC,mBAAmB,KAAK,CAAA;IAC5B,IAAI;AACF,QAAA,MAAMnsB,OAAU,GAAA;AACd,YAAA,IAAIosB,OAAU,CAAA,GAAA;AACZD,gBAAAA,gBAAAA,GAAmB,IAAI,CAAA;AACvB,gBAAA,OAAO,KAAK,CAAA;AACd,aAAA;AACF,SAAA,CAAA;AAEA,QAAA,IAAI9D,eAAmB,EAAA,EAAA;AACrBxe,YAAAA,MAAAA,CAAOwiB,gBAAgB,CAAC,MAAQ,EAAA,IAAI,EAAErsB,OAAAA,CAAAA,CAAAA;AACtC6J,YAAAA,MAAAA,CAAOyiB,mBAAmB,CAAC,MAAQ,EAAA,IAAI,EAAEtsB,OAAAA,CAAAA,CAAAA;SAC1C;AACH,KAAA,CAAE,OAAO2C,CAAG,EAAA;;AAEZ,KAAA;IACA,OAAOwpB,gBAAAA,CAAAA;AACT,CAAK,GAAA;AAEL;;;;;;;;AAQC,IAEM,SAASI,YAAAA,CACdvD,OAAoB,EACpB7jB,QAA4B,EACR;IACpB,MAAMxI,KAAAA,GAAQwsB,SAASH,OAAS7jB,EAAAA,QAAAA,CAAAA,CAAAA;AAChC,IAAA,MAAM0a,OAAUljB,GAAAA,KAAAA,IAASA,KAAMmjB,CAAAA,KAAK,CAAC,mBAAA,CAAA,CAAA;AACrC,IAAA,OAAOD,UAAU,CAACA,OAAO,CAAC,CAAA,CAAE,GAAGjjB,SAAS,CAAA;AAC1C;;ACzRA;;IAGO,SAAS4vB,YAAAA,CAAaC,EAAS,EAAEC,EAAS,EAAE1f,CAAS,EAAE2K,IAAK,EAAE;IACnE,OAAO;QACL1W,CAAGwrB,EAAAA,EAAAA,CAAGxrB,CAAC,GAAG+L,CAAK0f,IAAAA,GAAGzrB,CAAC,GAAGwrB,EAAGxrB,CAAAA,CAAC,CAADA;QACzBE,CAAGsrB,EAAAA,EAAAA,CAAGtrB,CAAC,GAAG6L,CAAK0f,IAAAA,GAAGvrB,CAAC,GAAGsrB,EAAGtrB,CAAAA,CAAC,CAADA;AAC3B,KAAA,CAAA;AACF,CAAC;AAED;;IAGO,SAASwrB,qBAAAA,CACdF,EAAS,EACTC,EAAS,EACT1f,CAAS,EAAE2K,IAAkC,EAC7C;IACA,OAAO;QACL1W,CAAGwrB,EAAAA,EAAAA,CAAGxrB,CAAC,GAAG+L,CAAK0f,IAAAA,GAAGzrB,CAAC,GAAGwrB,EAAGxrB,CAAAA,CAAC,CAADA;QACzBE,CAAGwW,EAAAA,IAAAA,KAAS,QAAW3K,GAAAA,CAAAA,GAAI,GAAMyf,GAAAA,EAAAA,CAAGtrB,CAAC,GAAGurB,EAAGvrB,CAAAA,CAAC,GACxCwW,IAAAA,KAAS,OAAU3K,GAAAA,CAAAA,GAAI,IAAIyf,EAAGtrB,CAAAA,CAAC,GAAGurB,EAAAA,CAAGvrB,CAAC,GACpC6L,CAAI,GAAA,CAAA,GAAI0f,EAAGvrB,CAAAA,CAAC,GAAGsrB,EAAAA,CAAGtrB,CAAC;AAC3B,KAAA,CAAA;AACF,CAAC;AAED;;IAGO,SAASyrB,oBAAAA,CAAqBH,EAAe,EAAEC,EAAe,EAAE1f,CAAS,EAAE2K,IAAK,EAAE;AACvF,IAAA,MAAMkV,GAAM,GAAA;AAAC5rB,QAAAA,CAAAA,EAAGwrB,GAAGrP,IAAI;AAAEjc,QAAAA,CAAAA,EAAGsrB,GAAGnP,IAAI;AAAA,KAAA,CAAA;AACnC,IAAA,MAAMwP,GAAM,GAAA;AAAC7rB,QAAAA,CAAAA,EAAGyrB,GAAGvP,IAAI;AAAEhc,QAAAA,CAAAA,EAAGurB,GAAGrP,IAAI;AAAA,KAAA,CAAA;IACnC,MAAMhb,CAAAA,GAAImqB,YAAaC,CAAAA,EAAAA,EAAII,GAAK7f,EAAAA,CAAAA,CAAAA,CAAAA;IAChC,MAAM1K,CAAAA,GAAIkqB,YAAaK,CAAAA,GAAAA,EAAKC,GAAK9f,EAAAA,CAAAA,CAAAA,CAAAA;IACjC,MAAM+f,CAAAA,GAAIP,YAAaM,CAAAA,GAAAA,EAAKJ,EAAI1f,EAAAA,CAAAA,CAAAA,CAAAA;IAChC,MAAMqC,CAAAA,GAAImd,YAAanqB,CAAAA,CAAAA,EAAGC,CAAG0K,EAAAA,CAAAA,CAAAA,CAAAA;IAC7B,MAAMrK,CAAAA,GAAI6pB,YAAalqB,CAAAA,CAAAA,EAAGyqB,CAAG/f,EAAAA,CAAAA,CAAAA,CAAAA;IAC7B,OAAOwf,YAAAA,CAAand,GAAG1M,CAAGqK,EAAAA,CAAAA,CAAAA,CAAAA;AAC5B;;AChCA,MAAMggB,qBAAwB,GAAA,SAASC,KAAa,EAAExY,KAAa,EAAc;IAC/E,OAAO;AACLxT,QAAAA,CAAAA,CAAAA,CAAEA,CAAC,EAAE;YACH,OAAOgsB,KAAAA,GAAQA,QAAQxY,KAAQxT,GAAAA,CAAAA,CAAAA;AACjC,SAAA;AACAisB,QAAAA,QAAAA,CAAAA,CAASjS,CAAC,EAAE;YACVxG,KAAQwG,GAAAA,CAAAA,CAAAA;AACV,SAAA;AACA0C,QAAAA,SAAAA,CAAAA,CAAUnT,KAAK,EAAE;AACf,YAAA,IAAIA,UAAU,QAAU,EAAA;gBACtB,OAAOA,KAAAA,CAAAA;aACR;YACD,OAAOA,KAAAA,KAAU,OAAU,GAAA,MAAA,GAAS,OAAO,CAAA;AAC7C,SAAA;QACA2iB,KAAMlsB,CAAAA,CAAAA,CAAC,EAAEtE,KAAK,EAAE;AACd,YAAA,OAAOsE,CAAItE,GAAAA,KAAAA,CAAAA;AACb,SAAA;QACAywB,UAAWnsB,CAAAA,CAAAA,CAAC,EAAEosB,SAAS,EAAE;AACvB,YAAA,OAAOpsB,CAAIosB,GAAAA,SAAAA,CAAAA;AACb,SAAA;AACF,KAAA,CAAA;AACF,CAAA,CAAA;AAEA,MAAMC,wBAAwB,WAAuB;IACnD,OAAO;AACLrsB,QAAAA,CAAAA,CAAAA,CAAEA,CAAC,EAAE;YACH,OAAOA,CAAAA,CAAAA;AACT,SAAA;QACAisB,QAASjS,CAAAA,CAAAA,CAAC,EAAE,EACZ;AACA0C,QAAAA,SAAAA,CAAAA,CAAUnT,KAAK,EAAE;YACf,OAAOA,KAAAA,CAAAA;AACT,SAAA;QACA2iB,KAAMlsB,CAAAA,CAAAA,CAAC,EAAEtE,KAAK,EAAE;AACd,YAAA,OAAOsE,CAAItE,GAAAA,KAAAA,CAAAA;AACb,SAAA;QACAywB,UAAWnsB,CAAAA,CAAAA,CAAC,EAAEssB,UAAU,EAAE;YACxB,OAAOtsB,CAAAA,CAAAA;AACT,SAAA;AACF,KAAA,CAAA;AACF,CAAA,CAAA;AAEO,SAASusB,aAAc3iB,CAAAA,GAAY,EAAEoiB,KAAa,EAAExY,KAAa,EAAE;AACxE,IAAA,OAAO5J,GAAMmiB,GAAAA,qBAAAA,CAAsBC,KAAOxY,EAAAA,KAAAA,CAAAA,GAAS6Y,qBAAuB,EAAA,CAAA;AAC5E,CAAC;AAEM,SAASG,qBAAAA,CAAsBnW,GAA6B,EAAEoW,SAAwB,EAAE;AAC7F,IAAA,IAAIzW,KAA4B0W,EAAAA,QAAAA,CAAAA;IAChC,IAAID,SAAAA,KAAc,KAASA,IAAAA,SAAAA,KAAc,KAAO,EAAA;QAC9CzW,KAAQK,GAAAA,GAAAA,CAAIoD,MAAM,CAACzD,KAAK,CAAA;QACxB0W,QAAW,GAAA;AACT1W,YAAAA,KAAAA,CAAMoS,gBAAgB,CAAC,WAAA,CAAA;AACvBpS,YAAAA,KAAAA,CAAM2W,mBAAmB,CAAC,WAAA,CAAA;AAC3B,SAAA,CAAA;QAED3W,KAAM4W,CAAAA,WAAW,CAAC,WAAA,EAAaH,SAAW,EAAA,WAAA,CAAA,CAAA;AACzCpW,QAAAA,GAAAA,CAAiDwW,iBAAiB,GAAGH,QAAAA,CAAAA;KACvE;AACH,CAAC;AAEM,SAASI,oBAAAA,CAAqBzW,GAA6B,EAAEqW,QAA2B,EAAE;AAC/F,IAAA,IAAIA,aAAa/wB,SAAW,EAAA;QAC1B,OAAQ0a,IAAiDwW,iBAAiB,CAAA;AAC1ExW,QAAAA,GAAAA,CAAIoD,MAAM,CAACzD,KAAK,CAAC4W,WAAW,CAAC,WAAaF,EAAAA,QAAQ,CAAC,CAAA,CAAE,EAAEA,QAAQ,CAAC,CAAE,CAAA,CAAA,CAAA;KACnE;AACH;;AC/DA,SAASK,UAAW7oB,CAAAA,QAAQ,EAAE;AAC5B,IAAA,IAAIA,aAAa,OAAS,EAAA;QACxB,OAAO;YACL8oB,OAAStnB,EAAAA,aAAAA;YACTunB,OAASznB,EAAAA,UAAAA;YACT0nB,SAAWznB,EAAAA,eAAAA;AACb,SAAA,CAAA;KACD;IACD,OAAO;QACLunB,OAAS3mB,EAAAA,UAAAA;QACT4mB,OAAS,EAAA,CAAC7rB,CAAGC,EAAAA,CAAAA,GAAMD,CAAIC,GAAAA,CAAAA;AACvB6rB,QAAAA,SAAAA,EAAWltB,CAAAA,CAAKA,GAAAA,CAAAA;AAClB,KAAA,CAAA;AACF,CAAA;AAEA,SAASmtB,gBAAiB,CAAA,EAACxnB,KAAK,GAAEC,GAAG,GAAEuE,KAAK,GAAEgF,IAAI,GAAE6G,KAAK,GAAC,EAAE;IAC1D,OAAO;AACLrQ,QAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AACfvE,QAAAA,GAAAA,EAAKA,GAAMuE,GAAAA,KAAAA;AACXgF,QAAAA,IAAAA,EAAMA,QAAQ,CAACvJ,MAAMD,KAAQ,GAAA,CAAA,IAAKwE,KAAU,KAAA,CAAA;AAC5C6L,QAAAA,KAAAA;AACF,KAAA,CAAA;AACF,CAAA;AAEA,SAASoX,WAAWC,OAAO,EAAErjB,MAAM,EAAE0I,MAAM,EAAE;IAC3C,MAAM,EAACxO,WAAUyB,KAAAA,EAAO2nB,aAAY1nB,GAAAA,EAAK2nB,QAAQ,GAAC,GAAG7a,MAAAA,CAAAA;AACrD,IAAA,MAAM,EAACsa,OAAO,GAAEE,SAAS,GAAC,GAAGH,UAAW7oB,CAAAA,QAAAA,CAAAA,CAAAA;IACxC,MAAMiG,KAAAA,GAAQH,OAAOrM,MAAM,CAAA;AAE3B,IAAA,IAAI,EAACgI,KAAK,GAAEC,MAAKuJ,IAAAA,GAAK,GAAGke,OAAAA,CAAAA;AACzB,IAAA,IAAI7vB,CAAGO,EAAAA,IAAAA,CAAAA;AAEP,IAAA,IAAIoR,IAAM,EAAA;QACRxJ,KAASwE,IAAAA,KAAAA,CAAAA;QACTvE,GAAOuE,IAAAA,KAAAA,CAAAA;QACP,IAAK3M,CAAAA,GAAI,GAAGO,IAAOoM,GAAAA,KAAK,EAAE3M,CAAIO,GAAAA,IAAAA,EAAM,EAAEP,CAAG,CAAA;YACvC,IAAI,CAACwvB,OAAQE,CAAAA,SAAAA,CAAUljB,MAAM,CAACrE,KAAQwE,GAAAA,KAAAA,CAAM,CAACjG,QAAAA,CAAS,CAAGopB,EAAAA,UAAAA,EAAYC,QAAW,CAAA,EAAA;gBAC9E,MAAM;aACP;AACD5nB,YAAAA,KAAAA,EAAAA,CAAAA;AACAC,YAAAA,GAAAA,EAAAA,CAAAA;AACF,SAAA;QACAD,KAASwE,IAAAA,KAAAA,CAAAA;QACTvE,GAAOuE,IAAAA,KAAAA,CAAAA;KACR;AAED,IAAA,IAAIvE,MAAMD,KAAO,EAAA;QACfC,GAAOuE,IAAAA,KAAAA,CAAAA;KACR;IACD,OAAO;AAACxE,QAAAA,KAAAA;AAAOC,QAAAA,GAAAA;AAAKuJ,QAAAA,IAAAA;AAAM6G,QAAAA,KAAAA,EAAOqX,QAAQrX,KAAK;AAAA,KAAA,CAAA;AAChD,CAAA;AAgBA,CAAO,SAASwX,aAAcH,CAAAA,OAAO,EAAErjB,MAAM,EAAE0I,MAAM,EAAE;AACrD,IAAA,IAAI,CAACA,MAAQ,EAAA;QACX,OAAO;AAAC2a,YAAAA,OAAAA;AAAQ,SAAA,CAAA;KACjB;IAED,MAAM,EAACnpB,WAAUyB,KAAAA,EAAO2nB,aAAY1nB,GAAAA,EAAK2nB,QAAQ,GAAC,GAAG7a,MAAAA,CAAAA;IACrD,MAAMvI,KAAAA,GAAQH,OAAOrM,MAAM,CAAA;IAC3B,MAAM,EAACsvB,UAASD,OAAAA,GAASE,SAAS,GAAC,GAAGH,UAAW7oB,CAAAA,QAAAA,CAAAA,CAAAA;AACjD,IAAA,MAAM,EAACyB,KAAAA,GAAOC,GAAAA,GAAKuJ,IAAAA,GAAM6G,KAAAA,GAAM,GAAGoX,UAAWC,CAAAA,OAAAA,EAASrjB,MAAQ0I,EAAAA,MAAAA,CAAAA,CAAAA;AAE9D,IAAA,MAAMtP,SAAS,EAAE,CAAA;AACjB,IAAA,IAAIqqB,SAAS,KAAK,CAAA;AAClB,IAAA,IAAIC,WAAW,IAAI,CAAA;AACnB,IAAA,IAAIhyB,OAAOuP,KAAO0iB,EAAAA,SAAAA,CAAAA;IAElB,MAAMC,aAAAA,GAAgB,IAAMZ,OAAQM,CAAAA,UAAAA,EAAYK,WAAWjyB,KAAUuxB,CAAAA,IAAAA,OAAAA,CAAQK,YAAYK,SAAe,CAAA,KAAA,CAAA,CAAA;IACxG,MAAME,WAAAA,GAAc,IAAMZ,OAAQM,CAAAA,QAAAA,EAAU7xB,WAAW,CAAKsxB,IAAAA,OAAAA,CAAQO,UAAUI,SAAWjyB,EAAAA,KAAAA,CAAAA,CAAAA;IACzF,MAAMoyB,WAAAA,GAAc,IAAML,MAAUG,IAAAA,aAAAA,EAAAA,CAAAA;IACpC,MAAMG,UAAAA,GAAa,IAAM,CAACN,MAAUI,IAAAA,WAAAA,EAAAA,CAAAA;IAEpC,IAAK,IAAIrwB,IAAImI,KAAOuhB,EAAAA,IAAAA,GAAOvhB,OAAOnI,CAAKoI,IAAAA,GAAAA,EAAK,EAAEpI,CAAG,CAAA;QAC/CyN,KAAQjB,GAAAA,MAAM,CAACxM,CAAAA,GAAI2M,KAAM,CAAA,CAAA;QAEzB,IAAIc,KAAAA,CAAM0Z,IAAI,EAAE;YACd,SAAS;SACV;QAEDjpB,KAAQwxB,GAAAA,SAAAA,CAAUjiB,KAAK,CAAC/G,QAAS,CAAA,CAAA,CAAA;AAEjC,QAAA,IAAIxI,UAAUiyB,SAAW,EAAA;YACvB,SAAS;SACV;QAEDF,MAAST,GAAAA,OAAAA,CAAQtxB,OAAO4xB,UAAYC,EAAAA,QAAAA,CAAAA,CAAAA;QAEpC,IAAIG,QAAAA,KAAa,IAAI,IAAII,WAAe,EAAA,EAAA;AACtCJ,YAAAA,QAAAA,GAAWT,OAAQvxB,CAAAA,KAAAA,EAAO4xB,UAAgB,CAAA,KAAA,CAAA,GAAI9vB,IAAI0pB,IAAI,CAAA;SACvD;QAED,IAAIwG,QAAAA,KAAa,IAAI,IAAIK,UAAc,EAAA,EAAA;YACrC3qB,MAAO5C,CAAAA,IAAI,CAAC2sB,gBAAiB,CAAA;gBAACxnB,KAAO+nB,EAAAA,QAAAA;gBAAU9nB,GAAKpI,EAAAA,CAAAA;AAAG2R,gBAAAA,IAAAA;AAAMhF,gBAAAA,KAAAA;AAAO6L,gBAAAA,KAAAA;AAAK,aAAA,CAAA,CAAA,CAAA;AACzE0X,YAAAA,QAAAA,GAAW,IAAI,CAAA;SAChB;QACDxG,IAAO1pB,GAAAA,CAAAA,CAAAA;QACPmwB,SAAYjyB,GAAAA,KAAAA,CAAAA;AACd,KAAA;IAEA,IAAIgyB,QAAAA,KAAa,IAAI,EAAE;QACrBtqB,MAAO5C,CAAAA,IAAI,CAAC2sB,gBAAiB,CAAA;YAACxnB,KAAO+nB,EAAAA,QAAAA;AAAU9nB,YAAAA,GAAAA;AAAKuJ,YAAAA,IAAAA;AAAMhF,YAAAA,KAAAA;AAAO6L,YAAAA,KAAAA;AAAK,SAAA,CAAA,CAAA,CAAA;KACvE;IAED,OAAO5S,MAAAA,CAAAA;AACT,CAAC;AAWA,CACM,SAAS4qB,cAAAA,CAAenR,IAAI,EAAEnK,MAAM,EAAE;AAC3C,IAAA,MAAMtP,SAAS,EAAE,CAAA;IACjB,MAAM6qB,QAAAA,GAAWpR,KAAKoR,QAAQ,CAAA;AAE9B,IAAA,IAAK,IAAIzwB,CAAI,GAAA,CAAA,EAAGA,IAAIywB,QAAStwB,CAAAA,MAAM,EAAEH,CAAK,EAAA,CAAA;QACxC,MAAM0wB,GAAAA,GAAMV,cAAcS,QAAQ,CAACzwB,EAAE,EAAEqf,IAAAA,CAAK7S,MAAM,EAAE0I,MAAAA,CAAAA,CAAAA;QACpD,IAAIwb,GAAAA,CAAIvwB,MAAM,EAAE;AACdyF,YAAAA,MAAAA,CAAO5C,IAAI,CAAI0tB,GAAAA,GAAAA,CAAAA,CAAAA;SAChB;AACH,KAAA;IACA,OAAO9qB,MAAAA,CAAAA;AACT,CAAC;AAKD,CAAA,SAAS+qB,gBAAgBnkB,MAAM,EAAEG,KAAK,EAAEgF,IAAI,EAAE3E,QAAQ,EAAE;AACtD,IAAA,IAAI7E,KAAQ,GAAA,CAAA,CAAA;AACZ,IAAA,IAAIC,MAAMuE,KAAQ,GAAA,CAAA,CAAA;IAElB,IAAIgF,IAAAA,IAAQ,CAAC3E,QAAU,EAAA;QAErB,MAAO7E,KAAAA,GAAQwE,SAAS,CAACH,MAAM,CAACrE,KAAM,CAAA,CAACgf,IAAI,CAAE;AAC3Chf,YAAAA,KAAAA,EAAAA,CAAAA;AACF,SAAA;KACD;AAGD,IAAA,MAAOA,QAAQwE,KAASH,IAAAA,MAAM,CAACrE,KAAM,CAAA,CAACgf,IAAI,CAAE;AAC1Chf,QAAAA,KAAAA,EAAAA,CAAAA;AACF,KAAA;IAGAA,KAASwE,IAAAA,KAAAA,CAAAA;AAET,IAAA,IAAIgF,IAAM,EAAA;QAERvJ,GAAOD,IAAAA,KAAAA,CAAAA;KACR;IAED,MAAOC,GAAAA,GAAMD,SAASqE,MAAM,CAACpE,MAAMuE,KAAM,CAAA,CAACwa,IAAI,CAAE;AAC9C/e,QAAAA,GAAAA,EAAAA,CAAAA;AACF,KAAA;IAGAA,GAAOuE,IAAAA,KAAAA,CAAAA;IAEP,OAAO;AAACxE,QAAAA,KAAAA;AAAOC,QAAAA,GAAAA;AAAG,KAAA,CAAA;AACpB,CAAA;AASA,CAAA,SAASwoB,cAAcpkB,MAAM,EAAErE,KAAK,EAAEvB,GAAG,EAAE+K,IAAI,EAAE;IAC/C,MAAMhF,KAAAA,GAAQH,OAAOrM,MAAM,CAAA;AAC3B,IAAA,MAAMyF,SAAS,EAAE,CAAA;AACjB,IAAA,IAAIyD,IAAOlB,GAAAA,KAAAA,CAAAA;IACX,IAAIuhB,IAAAA,GAAOld,MAAM,CAACrE,KAAM,CAAA,CAAA;IACxB,IAAIC,GAAAA,CAAAA;AAEJ,IAAA,IAAKA,MAAMD,KAAQ,GAAA,CAAA,EAAGC,GAAOxB,IAAAA,GAAAA,EAAK,EAAEwB,GAAK,CAAA;AACvC,QAAA,MAAMyoB,GAAMrkB,GAAAA,MAAM,CAACpE,GAAAA,GAAMuE,KAAM,CAAA,CAAA;AAC/B,QAAA,IAAIkkB,GAAI1J,CAAAA,IAAI,IAAI0J,GAAAA,CAAIC,IAAI,EAAE;YACxB,IAAI,CAACpH,IAAKvC,CAAAA,IAAI,EAAE;AACdxV,gBAAAA,IAAAA,GAAO,KAAK,CAAA;AACZ/L,gBAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACmF,oBAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AAAOvE,oBAAAA,GAAAA,EAAK,CAACA,GAAM,GAAA,CAAA,IAAKuE,KAAAA;AAAOgF,oBAAAA,IAAAA;AAAI,iBAAA,CAAA,CAAA;AAE/DxJ,gBAAAA,KAAAA,GAAQkB,IAAOwnB,GAAAA,GAAAA,CAAIC,IAAI,GAAG1oB,MAAM,IAAI,CAAA;aACrC;SACI,MAAA;YACLiB,IAAOjB,GAAAA,GAAAA,CAAAA;YACP,IAAIshB,IAAAA,CAAKvC,IAAI,EAAE;gBACbhf,KAAQC,GAAAA,GAAAA,CAAAA;aACT;SACF;QACDshB,IAAOmH,GAAAA,GAAAA,CAAAA;AACT,KAAA;IAEA,IAAIxnB,IAAAA,KAAS,IAAI,EAAE;AACjBzD,QAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACmF,YAAAA,KAAAA,EAAOA,KAAQwE,GAAAA,KAAAA;AAAOvE,YAAAA,GAAAA,EAAKiB,IAAOsD,GAAAA,KAAAA;AAAOgF,YAAAA,IAAAA;AAAI,SAAA,CAAA,CAAA;KAC3D;IAED,OAAO/L,MAAAA,CAAAA;AACT,CAAA;AASC,CACM,SAASmrB,gBAAAA,CAAiB1R,IAAI,EAAE2R,cAAc,EAAE;IACrD,MAAMxkB,MAAAA,GAAS6S,KAAK7S,MAAM,CAAA;AAC1B,IAAA,MAAMQ,QAAWqS,GAAAA,IAAAA,CAAK9d,OAAO,CAACyL,QAAQ,CAAA;IACtC,MAAML,KAAAA,GAAQH,OAAOrM,MAAM,CAAA;AAE3B,IAAA,IAAI,CAACwM,KAAO,EAAA;AACV,QAAA,OAAO,EAAE,CAAA;KACV;AAED,IAAA,MAAMgF,IAAO,GAAA,CAAC,CAAC0N,IAAAA,CAAK4R,KAAK,CAAA;IACzB,MAAM,EAAC9oB,QAAOC,GAAAA,GAAI,GAAGuoB,eAAAA,CAAgBnkB,MAAQG,EAAAA,KAAAA,EAAOgF,IAAM3E,EAAAA,QAAAA,CAAAA,CAAAA;IAE1D,IAAIA,QAAAA,KAAa,IAAI,EAAE;AACrB,QAAA,OAAOkkB,cAAc7R,IAAM,EAAA;AAAC,YAAA;AAAClX,gBAAAA,KAAAA;AAAOC,gBAAAA,GAAAA;AAAKuJ,gBAAAA,IAAAA;AAAI,aAAA;AAAE,SAAA,EAAEnF,MAAQwkB,EAAAA,cAAAA,CAAAA,CAAAA;KAC1D;AAED,IAAA,MAAMpqB,GAAMwB,GAAAA,GAAAA,GAAMD,KAAQC,GAAAA,GAAAA,GAAMuE,QAAQvE,GAAG,CAAA;IAC3C,MAAM+oB,YAAAA,GAAe,CAAC,CAAC9R,IAAAA,CAAK+R,SAAS,IAAIjpB,KAAAA,KAAU,CAAKC,IAAAA,GAAAA,KAAQuE,KAAQ,GAAA,CAAA,CAAA;AACxE,IAAA,OAAOukB,cAAc7R,IAAMuR,EAAAA,aAAAA,CAAcpkB,QAAQrE,KAAOvB,EAAAA,GAAAA,EAAKuqB,eAAe3kB,MAAQwkB,EAAAA,cAAAA,CAAAA,CAAAA;AACtF,CAAC;AAQD,CAAA,SAASE,cAAc7R,IAAI,EAAEoR,QAAQ,EAAEjkB,MAAM,EAAEwkB,cAAc,EAAE;AAC7D,IAAA,IAAI,CAACA,cAAkB,IAAA,CAACA,eAAelM,UAAU,IAAI,CAACtY,MAAQ,EAAA;QAC5D,OAAOikB,QAAAA,CAAAA;KACR;IACD,OAAOY,eAAAA,CAAgBhS,IAAMoR,EAAAA,QAAAA,EAAUjkB,MAAQwkB,EAAAA,cAAAA,CAAAA,CAAAA;AACjD,CAAA;AASA,CAAA,SAASK,gBAAgBhS,IAAI,EAAEoR,QAAQ,EAAEjkB,MAAM,EAAEwkB,cAAc,EAAE;AAC/D,IAAA,MAAMM,YAAejS,GAAAA,IAAAA,CAAKkS,MAAM,CAACrV,UAAU,EAAA,CAAA;IAC3C,MAAMsV,SAAAA,GAAYC,SAAUpS,CAAAA,IAAAA,CAAK9d,OAAO,CAAA,CAAA;IACxC,MAAM,EAACmwB,aAAehxB,EAAAA,YAAAA,GAAca,OAAAA,EAAS,EAACyL,QAAQ,GAAC,GAAC,GAAGqS,IAAAA,CAAAA;IAC3D,MAAM1S,KAAAA,GAAQH,OAAOrM,MAAM,CAAA;AAC3B,IAAA,MAAMyF,SAAS,EAAE,CAAA;AACjB,IAAA,IAAI+rB,SAAYH,GAAAA,SAAAA,CAAAA;AAChB,IAAA,IAAIrpB,KAAQsoB,GAAAA,QAAQ,CAAC,CAAA,CAAE,CAACtoB,KAAK,CAAA;AAC7B,IAAA,IAAInI,CAAImI,GAAAA,KAAAA,CAAAA;IAER,SAASypB,QAAAA,CAAStpB,CAAC,EAAEpE,CAAC,EAAE2tB,CAAC,EAAEC,EAAE,EAAE;AAC7B,QAAA,MAAMC,GAAM/kB,GAAAA,QAAAA,GAAW,CAAC,CAAA,GAAI,CAAC,CAAA;AAC7B,QAAA,IAAI1E,MAAMpE,CAAG,EAAA;AACX,YAAA,OAAA;SACD;QAEDoE,CAAKqE,IAAAA,KAAAA,CAAAA;AACL,QAAA,MAAOH,MAAM,CAAClE,CAAAA,GAAIqE,KAAM,CAAA,CAACwa,IAAI,CAAE;YAC7B7e,CAAKypB,IAAAA,GAAAA,CAAAA;AACP,SAAA;AACA,QAAA,MAAOvlB,MAAM,CAACtI,CAAAA,GAAIyI,KAAM,CAAA,CAACwa,IAAI,CAAE;YAC7BjjB,CAAK6tB,IAAAA,GAAAA,CAAAA;AACP,SAAA;QACA,IAAIzpB,CAAAA,GAAIqE,KAAUzI,KAAAA,CAAAA,GAAIyI,KAAO,EAAA;AAC3B/G,YAAAA,MAAAA,CAAO5C,IAAI,CAAC;AAACmF,gBAAAA,KAAAA,EAAOG,CAAIqE,GAAAA,KAAAA;AAAOvE,gBAAAA,GAAAA,EAAKlE,CAAIyI,GAAAA,KAAAA;gBAAOgF,IAAMkgB,EAAAA,CAAAA;gBAAGrZ,KAAOsZ,EAAAA,EAAAA;AAAE,aAAA,CAAA,CAAA;YACjEH,SAAYG,GAAAA,EAAAA,CAAAA;AACZ3pB,YAAAA,KAAAA,GAAQjE,CAAIyI,GAAAA,KAAAA,CAAAA;SACb;AACH,KAAA;IAEA,KAAK,MAAMkjB,WAAWY,QAAU,CAAA;QAC9BtoB,KAAQ6E,GAAAA,QAAAA,GAAW7E,KAAQ0nB,GAAAA,OAAAA,CAAQ1nB,KAAK,CAAA;AACxC,QAAA,IAAIuhB,IAAOld,GAAAA,MAAM,CAACrE,KAAAA,GAAQwE,KAAM,CAAA,CAAA;QAChC,IAAI6L,KAAAA,CAAAA;AACJ,QAAA,IAAKxY,IAAImI,KAAQ,GAAA,CAAA,EAAGnI,KAAK6vB,OAAQznB,CAAAA,GAAG,EAAEpI,CAAK,EAAA,CAAA;AACzC,YAAA,MAAMkpB,EAAK1c,GAAAA,MAAM,CAACxM,CAAAA,GAAI2M,KAAM,CAAA,CAAA;AAC5B6L,YAAAA,KAAAA,GAAQiZ,SAAUT,CAAAA,cAAAA,CAAelM,UAAU,CAAClC,cAAc0O,YAAc,EAAA;gBACtEhzB,IAAM,EAAA,SAAA;gBACN0zB,EAAItI,EAAAA,IAAAA;gBACJsE,EAAI9E,EAAAA,EAAAA;AACJ+I,gBAAAA,WAAAA,EAAa,CAACjyB,CAAI,GAAA,CAAA,IAAK2M,KAAAA;AACvBulB,gBAAAA,WAAAA,EAAalyB,CAAI2M,GAAAA,KAAAA;AACjBjM,gBAAAA,YAAAA;AACF,aAAA,CAAA,CAAA,CAAA,CAAA;YACA,IAAIyxB,YAAAA,CAAa3Z,OAAOmZ,SAAY,CAAA,EAAA;AAClCC,gBAAAA,QAAAA,CAASzpB,KAAOnI,EAAAA,CAAAA,GAAI,CAAG6vB,EAAAA,OAAAA,CAAQle,IAAI,EAAEggB,SAAAA,CAAAA,CAAAA;aACtC;YACDjI,IAAOR,GAAAA,EAAAA,CAAAA;YACPyI,SAAYnZ,GAAAA,KAAAA,CAAAA;AACd,SAAA;QACA,IAAIrQ,KAAAA,GAAQnI,IAAI,CAAG,EAAA;AACjB4xB,YAAAA,QAAAA,CAASzpB,KAAOnI,EAAAA,CAAAA,GAAI,CAAG6vB,EAAAA,OAAAA,CAAQle,IAAI,EAAEggB,SAAAA,CAAAA,CAAAA;SACtC;AACH,KAAA;IAEA,OAAO/rB,MAAAA,CAAAA;AACT,CAAA;AAEA,SAAS6rB,SAAAA,CAAUlwB,OAAO,EAAE;IAC1B,OAAO;AACLsW,QAAAA,eAAAA,EAAiBtW,QAAQsW,eAAe;AACxCua,QAAAA,cAAAA,EAAgB7wB,QAAQ6wB,cAAc;AACtCC,QAAAA,UAAAA,EAAY9wB,QAAQ8wB,UAAU;AAC9BC,QAAAA,gBAAAA,EAAkB/wB,QAAQ+wB,gBAAgB;AAC1CC,QAAAA,eAAAA,EAAiBhxB,QAAQgxB,eAAe;AACxCzU,QAAAA,WAAAA,EAAavc,QAAQuc,WAAW;AAChChG,QAAAA,WAAAA,EAAavW,QAAQuW,WAAW;AAClC,KAAA,CAAA;AACF,CAAA;AAEA,SAASqa,YAAa3Z,CAAAA,KAAK,EAAEmZ,SAAS,EAAE;AACtC,IAAA,IAAI,CAACA,SAAW,EAAA;AACd,QAAA,OAAO,KAAK,CAAA;KACb;AACD,IAAA,MAAMxW,QAAQ,EAAE,CAAA;AAChB,IAAA,MAAMqX,QAAW,GAAA,SAASpxB,GAAG,EAAElD,KAAK,EAAE;QACpC,IAAI,CAAC4S,oBAAoB5S,KAAQ,CAAA,EAAA;YAC/B,OAAOA,KAAAA,CAAAA;SACR;AACD,QAAA,IAAI,CAACid,KAAAA,CAAMtG,QAAQ,CAAC3W,KAAQ,CAAA,EAAA;AAC1Bid,YAAAA,KAAAA,CAAMnY,IAAI,CAAC9E,KAAAA,CAAAA,CAAAA;SACZ;QACD,OAAOid,KAAAA,CAAM9Z,OAAO,CAACnD,KAAAA,CAAAA,CAAAA;AACvB,KAAA,CAAA;IACA,OAAOkV,IAAAA,CAAKC,SAAS,CAACmF,KAAAA,EAAOga,cAAcpf,IAAKC,CAAAA,SAAS,CAACse,SAAWa,EAAAA,QAAAA,CAAAA,CAAAA;AACvE;;ACzWA,SAASC,eAAe9Y,KAAY,EAAE+Y,SAAoB,EAAEC,KAAsB,EAAE;IAClF,OAAOhZ,KAAAA,CAAMpY,OAAO,CAAC4T,IAAI,GAAGwE,KAAK,CAACgZ,KAAM,CAAA,GAAGD,SAAS,CAACC,KAAM,CAAA,CAAA;AAC7D,CAAA;AAEA,SAASC,cAAermB,CAAAA,IAAe,EAAEmmB,SAAoB,EAAQ;AACnE,IAAA,MAAM,EAAC9kB,MAAAA,GAAQC,MAAAA,GAAO,GAAGtB,IAAAA,CAAAA;AACzB,IAAA,IAAIqB,UAAUC,MAAQ,EAAA;QACpB,OAAO;YACL3B,IAAMumB,EAAAA,cAAAA,CAAe7kB,QAAQ8kB,SAAW,EAAA,MAAA,CAAA;YACxCvmB,KAAOsmB,EAAAA,cAAAA,CAAe7kB,QAAQ8kB,SAAW,EAAA,OAAA,CAAA;YACzC7f,GAAK4f,EAAAA,cAAAA,CAAe5kB,QAAQ6kB,SAAW,EAAA,KAAA,CAAA;YACvC5f,MAAQ2f,EAAAA,cAAAA,CAAe5kB,QAAQ6kB,SAAW,EAAA,QAAA,CAAA;AAC5C,SAAA,CAAA;KACD;IACD,OAAOA,SAAAA,CAAAA;AACT,CAAA;AAEO,SAASG,kBAAAA,CAAmB5e,KAAY,EAAE1H,IAAe,EAAgB;IAC9E,MAAM4I,IAAAA,GAAO5I,KAAKumB,KAAK,CAAA;IACvB,IAAI3d,IAAAA,CAAK4d,QAAQ,EAAE;AACjB,QAAA,OAAO,KAAK,CAAA;KACb;AACD,IAAA,MAAM9U,IAAO2U,GAAAA,cAAAA,CAAermB,IAAM0H,EAAAA,KAAAA,CAAMye,SAAS,CAAA,CAAA;IAEjD,OAAO;AACLxmB,QAAAA,IAAAA,EAAMiJ,KAAKjJ,IAAI,KAAK,KAAK,GAAG,CAAA,GAAI+R,KAAK/R,IAAI,IAAIiJ,IAAKjJ,CAAAA,IAAI,KAAK,IAAI,GAAG,IAAIiJ,IAAKjJ,CAAAA,IAAI,CAAC;QAChFC,KAAOgJ,EAAAA,IAAAA,CAAKhJ,KAAK,KAAK,KAAK,GAAG8H,KAAM+B,CAAAA,KAAK,GAAGiI,IAAK9R,CAAAA,KAAK,IAAIgJ,IAAAA,CAAKhJ,KAAK,KAAK,IAAI,GAAG,CAAIgJ,GAAAA,IAAAA,CAAKhJ,KAAI,CAAE;AAC/F0G,QAAAA,GAAAA,EAAKsC,KAAKtC,GAAG,KAAK,KAAK,GAAG,CAAA,GAAIoL,KAAKpL,GAAG,IAAIsC,IAAKtC,CAAAA,GAAG,KAAK,IAAI,GAAG,IAAIsC,IAAKtC,CAAAA,GAAG,CAAC;QAC3EC,MAAQqC,EAAAA,IAAAA,CAAKrC,MAAM,KAAK,KAAK,GAAGmB,KAAMoI,CAAAA,MAAM,GAAG4B,IAAKnL,CAAAA,MAAM,IAAIqC,IAAAA,CAAKrC,MAAM,KAAK,IAAI,GAAG,CAAIqC,GAAAA,IAAAA,CAAKrC,MAAK,CAAE;AACvG,KAAA,CAAA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}