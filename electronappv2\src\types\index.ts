import { MessageType, PropertyType, TransactionType } from '../shared/constants';

/**
 * User related types
 */
export interface User {
  id: number;
  name: string;
  email?: string;
  preferences: UserPreferences;
  apiKey?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  language: string;
  currency: string;
  notifications: boolean;
  theme: 'light' | 'dark' | 'system';
  autoSave: boolean;
  maxBudget?: number;
  preferredLocalities: string[];
  propertyTypes: PropertyType[];
  transactionType: TransactionType;
}

/**
 * Chat related types
 */
export interface ChatMessage {
  id: string;
  userId?: number;
  message: string;
  response?: string;
  type: MessageType;
  timestamp: string;
  tokensUsed?: number;
  metadata?: {
    propertyIds?: string[];
    localityMentioned?: string[];
    priceRange?: {
      min: number;
      max: number;
    };
  };
}

export interface ChatSession {
  id: string;
  userId: number;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
  title?: string;
}

/**
 * Property related types
 */
export interface Property {
  id: string;
  title: string;
  description: string;
  type: PropertyType;
  transactionType: TransactionType;
  price: number;
  area: number; // in sq ft
  bedrooms?: number;
  bathrooms?: number;
  locality: string;
  address: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  amenities: string[];
  images: string[];
  contactInfo: {
    name: string;
    phone: string;
    email?: string;
  };
  features: {
    parking?: boolean;
    furnished?: 'fully' | 'semi' | 'unfurnished';
    balcony?: number;
    floor?: number;
    totalFloors?: number;
    age?: number; // in years
    facing?: string;
  };
  legalInfo: {
    rera?: string;
    approvals: string[];
    ownership: 'freehold' | 'leasehold';
  };
  createdAt: string;
  updatedAt: string;
}

export interface SavedProperty {
  id: number;
  userId: number;
  propertyId: string;
  propertyData: Property;
  notes?: string;
  savedAt: string;
}

/**
 * Locality related types
 */
export interface Locality {
  id: number;
  name: string;
  area: string;
  zone: string;
  pincode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  description: string;
  amenities: string[];
  connectivity: {
    metro?: string[];
    bus?: string[];
    railway?: string[];
    airport?: number; // distance in km
  };
  avgPricePerSqft: number;
  priceRange: {
    min: number;
    max: number;
  };
  marketTrends: {
    growth: number; // percentage
    demand: 'high' | 'medium' | 'low';
    supply: 'high' | 'medium' | 'low';
  };
}

/**
 * Search related types
 */
export interface PropertySearchQuery {
  query: string;
  filters: {
    type?: PropertyType[];
    transactionType?: TransactionType;
    priceRange?: {
      min: number;
      max: number;
    };
    areaRange?: {
      min: number;
      max: number;
    };
    bedrooms?: number[];
    localities?: string[];
    amenities?: string[];
    furnished?: string[];
  };
  sortBy?: 'price' | 'area' | 'date' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface PropertySearchResult {
  properties: Property[];
  total: number;
  filters: any;
  suggestions: string[];
  relatedLocalities: string[];
}

/**
 * AI related types
 */
export interface GeminiRequest {
  prompt: string;
  context?: {
    userId?: number;
    conversationHistory?: ChatMessage[];
    userPreferences?: UserPreferences;
    currentLocation?: string;
  };
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
  };
}

export interface GeminiResponse {
  response: string;
  tokensUsed: number;
  model: string;
  timestamp: string;
  metadata?: {
    confidence?: number;
    sources?: string[];
    relatedQueries?: string[];
  };
}

/**
 * Market analysis types
 */
export interface MarketTrend {
  locality: string;
  propertyType: PropertyType;
  period: string; // e.g., "2024-Q1"
  avgPrice: number;
  priceChange: number; // percentage
  volume: number; // number of transactions
  demandSupplyRatio: number;
  predictions: {
    nextQuarter: number;
    nextYear: number;
    confidence: number;
  };
}

/**
 * Settings types
 */
export interface AppSettings {
  general: {
    language: string;
    theme: 'light' | 'dark' | 'system';
    autoUpdate: boolean;
    startMinimized: boolean;
  };
  ai: {
    model: string;
    temperature: number;
    maxTokens: number;
    contextLength: number;
  };
  privacy: {
    saveConversations: boolean;
    shareUsageData: boolean;
    autoDeleteOldChats: boolean;
    retentionDays: number;
  };
  notifications: {
    priceAlerts: boolean;
    newListings: boolean;
    marketUpdates: boolean;
    systemUpdates: boolean;
  };
}

/**
 * API response types
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

/**
 * Database types
 */
export interface DatabaseBackup {
  filename: string;
  path: string;
  size: number;
  createdAt: string;
  version: string;
}
