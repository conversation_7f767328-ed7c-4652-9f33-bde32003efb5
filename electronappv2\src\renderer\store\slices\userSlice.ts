import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User, UserPreferences } from '@types/index';

interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  preferences: UserPreferences | null;
}

const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  preferences: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.isAuthenticated = true;
      state.preferences = action.payload.preferences;
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      if (state.preferences) {
        state.preferences = { ...state.preferences, ...action.payload };
      }
      if (state.currentUser) {
        state.currentUser.preferences = { ...state.currentUser.preferences, ...action.payload };
      }
    },
    logout: (state) => {
      state.currentUser = null;
      state.isAuthenticated = false;
      state.preferences = null;
    },
  },
});

export const { setUser, updateUserPreferences, logout } = userSlice.actions;
export default userSlice.reducer;
