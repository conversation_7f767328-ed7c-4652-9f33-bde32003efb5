@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-gray-600;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100;
  }

  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-6 text-base;
  }

  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Card styles */
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-gray-500;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Chat message styles */
  .message-user {
    @apply ml-auto max-w-xs lg:max-w-md bg-primary-600 text-white rounded-lg px-4 py-2 mb-2;
  }

  .message-ai {
    @apply mr-auto max-w-xs lg:max-w-md bg-gray-100 text-gray-900 rounded-lg px-4 py-2 mb-2;
  }

  .message-system {
    @apply mx-auto max-w-xs text-center text-sm text-gray-500 italic py-2;
  }

  /* Loading animation */
  .loading-dots {
    @apply inline-flex space-x-1;
  }

  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full;
    animation: loading-dot 1.4s infinite ease-in-out both;
  }

  .loading-dots > div:nth-child(2) {
    animation-delay: -0.32s;
  }

  .loading-dots > div:nth-child(3) {
    animation-delay: -0.16s;
  }

  @keyframes loading-dot {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }

  /* Property card styles */
  .property-card {
    @apply card hover:shadow-md transition-shadow cursor-pointer;
  }

  .property-card:hover {
    @apply border-primary-300;
  }

  /* Sidebar styles */
  .sidebar-item {
    @apply flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md transition-colors cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-600;
  }

  /* Dark mode styles */
  .dark .card {
    @apply border-gray-800 bg-gray-900 text-gray-50;
  }

  .dark .input {
    @apply border-gray-700 bg-gray-800 text-gray-50 placeholder:text-gray-400;
  }

  .dark .message-ai {
    @apply bg-gray-800 text-gray-100;
  }

  .dark .sidebar-item {
    @apply text-gray-300 hover:bg-gray-800;
  }

  .dark .sidebar-item.active {
    @apply bg-primary-900 text-primary-300;
  }

  /* PropertyClub Interface Styles */
  .property-card-hover {
    @apply transform transition-all duration-300 hover:scale-105 hover:shadow-xl;
  }

  .gradient-bg-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-bg-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-bg-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-bg-4 {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .gradient-bg-5 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .gradient-bg-6 {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  .search-input-focus {
    @apply focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-200;
  }

  .chat-message-user {
    @apply bg-blue-600 text-white rounded-2xl px-4 py-3 max-w-xs lg:max-w-2xl ml-auto;
  }

  .chat-message-ai {
    @apply bg-gray-100 text-gray-900 rounded-2xl px-4 py-3 max-w-xs lg:max-w-2xl mr-auto;
  }

  .inspiration-card {
    @apply bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer;
  }

  .inspiration-card:hover {
    @apply transform -translate-y-1;
  }

  .trending-card {
    @apply bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer;
  }

  .trending-card:hover {
    @apply transform -translate-y-2;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
