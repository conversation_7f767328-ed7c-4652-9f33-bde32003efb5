"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
exports.getDatabase = getDatabase;
exports.closeDatabase = closeDatabase;
const sqlite3 = __importStar(require("sqlite3"));
const sqlite_1 = require("sqlite");
const path = __importStar(require("path"));
const electron_1 = require("electron");
const constants_1 = require("@shared/constants");
let db = null;
/**
 * Initialize the SQLite database
 */
async function initializeDatabase() {
    try {
        const dbPath = path.join(electron_1.app.getPath('userData'), constants_1.APP_CONSTANTS.DB_NAME);
        db = await (0, sqlite_1.open)({
            filename: dbPath,
            driver: sqlite3.Database
        });
        // Enable foreign keys
        await db.exec('PRAGMA foreign_keys = ON');
        // Create tables
        await createTables();
        console.log('Database initialized successfully');
    }
    catch (error) {
        console.error('Failed to initialize database:', error);
        throw error;
    }
}
/**
 * Get the database instance
 */
function getDatabase() {
    if (!db) {
        throw new Error('Database not initialized. Call initializeDatabase() first.');
    }
    return db;
}
/**
 * Create all necessary tables
 */
async function createTables() {
    if (!db)
        return;
    // Users table
    await db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE,
      preferences TEXT DEFAULT '{}',
      api_key TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Conversations table
    await db.exec(`
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      message TEXT NOT NULL,
      response TEXT,
      message_type TEXT DEFAULT 'user',
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      tokens_used INTEGER DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);
    // Saved properties table
    await db.exec(`
    CREATE TABLE IF NOT EXISTS saved_properties (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      property_id TEXT,
      property_data TEXT NOT NULL,
      notes TEXT,
      saved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);
    // Bangalore localities table (pre-populated data)
    await db.exec(`
    CREATE TABLE IF NOT EXISTS localities (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      area TEXT,
      zone TEXT,
      pincode TEXT,
      coordinates TEXT,
      description TEXT,
      amenities TEXT,
      connectivity TEXT,
      avg_price_per_sqft REAL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Property searches table (for analytics)
    await db.exec(`
    CREATE TABLE IF NOT EXISTS property_searches (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      search_query TEXT NOT NULL,
      filters TEXT,
      results_count INTEGER DEFAULT 0,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);
    // Settings table
    await db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER,
      key TEXT NOT NULL,
      value TEXT NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      UNIQUE(user_id, key)
    )
  `);
    // Create indexes for better performance
    await db.exec(`
    CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
    CREATE INDEX IF NOT EXISTS idx_conversations_timestamp ON conversations(timestamp);
    CREATE INDEX IF NOT EXISTS idx_saved_properties_user_id ON saved_properties(user_id);
    CREATE INDEX IF NOT EXISTS idx_localities_name ON localities(name);
    CREATE INDEX IF NOT EXISTS idx_property_searches_user_id ON property_searches(user_id);
    CREATE INDEX IF NOT EXISTS idx_settings_user_key ON settings(user_id, key);
  `);
    console.log('Database tables created successfully');
}
/**
 * Close the database connection
 */
async function closeDatabase() {
    if (db) {
        await db.close();
        db = null;
        console.log('Database connection closed');
    }
}
//# sourceMappingURL=database.js.map