import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AppState {
  name: string;
  version: string;
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
}

const initialState: AppState = {
  name: 'PropertyClub AI',
  version: '1.0.0',
  isLoading: false,
  error: null,
  theme: 'system',
  sidebarCollapsed: false,
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setAppInfo: (state, action: PayloadAction<{ name: string; version: string }>) => {
      state.name = action.payload.name;
      state.version = action.payload.version;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
  },
});

export const {
  setAppInfo,
  setLoading,
  setError,
  setTheme,
  toggleSidebar,
  setSidebarCollapsed,
} = appSlice.actions;

export default appSlice.reducer;
