"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const utils_1 = require("../shared/utils");
const mainWindow_1 = require("./windows/mainWindow");
const ipcHandlers_1 = require("./ipc/ipcHandlers");
const database_1 = require("../database/database");
const authService_1 = require("../services/authService");
const knowledgeService_1 = require("../services/knowledgeService");
const propertySearchService_1 = require("../services/propertySearchService");
const marketAnalysisService_1 = require("../services/marketAnalysisService");
const dataVisualizationService_1 = require("../services/dataVisualizationService");
const externalApiService_1 = require("../services/externalApiService");
const advancedAnalyticsService_1 = require("../services/advancedAnalyticsService");
// Suppress DevTools warnings in console
const originalConsoleError = console.error;
console.error = (...args) => {
    const message = args.join(' ');
    if (message.includes('Autofill.enable') ||
        message.includes('Autofill.setAddresses') ||
        message.includes("wasn't found")) {
        return; // Don't log these specific DevTools warnings
    }
    originalConsoleError.apply(console, args);
};
// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
    electron_1.app.quit();
}
class PropertyClubAI {
    constructor() {
        this.mainWindow = null;
        this.initializeApp();
    }
    async initializeApp() {
        // Initialize database
        await (0, database_1.initializeDatabase)();
        // Set up IPC handlers
        (0, ipcHandlers_1.setupIpcHandlers)();
        // Handle app events
        this.setupAppEvents();
        // Create application menu
        this.createMenu();
    }
    setupAppEvents() {
        // This method will be called when Electron has finished initialization
        electron_1.app.whenReady().then(async () => {
            await this.initializeServices();
            this.createMainWindow();
            // On OS X it's common to re-create a window in the app when the
            // dock icon is clicked and there are no other windows open.
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        // Quit when all windows are closed, except on macOS
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        // Security: Prevent new window creation
        electron_1.app.on('web-contents-created', (event, contents) => {
            contents.setWindowOpenHandler(() => {
                return { action: 'deny' };
            });
        });
    }
    async initializeServices() {
        try {
            console.log('Initializing services...');
            // Initialize database
            await (0, database_1.initializeDatabase)();
            // Initialize authentication service
            await authService_1.authService.initialize();
            // Initialize knowledge service
            await knowledgeService_1.knowledgeService.initialize();
            // Initialize property search service
            await propertySearchService_1.propertySearchService.initialize();
            // Initialize market analysis service
            await marketAnalysisService_1.marketAnalysisService.initialize();
            // Initialize data visualization service
            await dataVisualizationService_1.dataVisualizationService.initialize();
            // Initialize external API service
            await externalApiService_1.externalApiService.initialize();
            // Initialize advanced analytics service
            await advancedAnalyticsService_1.advancedAnalyticsService.initialize();
            // Setup IPC handlers
            (0, ipcHandlers_1.setupIpcHandlers)();
            console.log('Services initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize services:', error);
            throw error;
        }
    }
    createMainWindow() {
        this.mainWindow = (0, mainWindow_1.createMainWindow)();
    }
    createMenu() {
        if ((0, utils_1.isDev)()) {
            // Development menu with DevTools
            const template = [
                {
                    label: 'View',
                    submenu: [
                        { role: 'reload' },
                        { role: 'forceReload' },
                        { role: 'toggleDevTools' },
                        { type: 'separator' },
                        { role: 'resetZoom' },
                        { role: 'zoomIn' },
                        { role: 'zoomOut' },
                        { type: 'separator' },
                        { role: 'togglefullscreen' }
                    ]
                }
            ];
            const menu = electron_1.Menu.buildFromTemplate(template);
            electron_1.Menu.setApplicationMenu(menu);
        }
        else {
            // Production menu (minimal)
            electron_1.Menu.setApplicationMenu(null);
        }
    }
}
// Initialize PropertyClubAI application
new PropertyClubAI();
//# sourceMappingURL=main.js.map