{"version": 3, "file": "mainWindow.js", "sourceRoot": "", "sources": ["../../../../src/main/windows/mainWindow.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,4CA0EC;AA9ED,uCAAiD;AACjD,2CAA6B;AAC7B,8CAA2C;AAE3C,SAAgB,gBAAgB;IAC9B,iCAAiC;IACjC,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAC;IAClD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC;IAEtD,+DAA+D;IAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAE7D,4BAA4B;IAC5B,MAAM,UAAU,GAAG,IAAI,wBAAa,CAAC;QACnC,KAAK,EAAE,WAAW;QAClB,MAAM,EAAE,YAAY;QACpB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,GAAG;QACd,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,KAAK,EAAE,iCAAiC;QAC9C,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,oCAAoC,CAAC;QAChE,aAAa,EAAE,SAAS;QACxB,cAAc,EAAE;YACd,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,IAAI;YAEtB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC;YACtD,0CAA0C;YAC1C,2BAA2B,EAAE,IAAA,aAAK,GAAE;SACrC;KACF,CAAC,CAAC;IAEH,eAAe;IACf,IAAI,IAAA,aAAK,GAAE,EAAE,CAAC;QACZ,4CAA4C;QAC5C,UAAU,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAE5C,+BAA+B;QAC/B,UAAU,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;SAAM,CAAC;QACN,oCAAoC;QACpC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,iDAAiD;IACjD,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,IAAI,EAAE,CAAC;QAElB,mBAAmB;QACnB,IAAI,IAAA,aAAK,GAAE,EAAE,CAAC;YACZ,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QAC3B,gCAAgC;IAClC,CAAC,CAAC,CAAC;IAEH,sCAAsC;IACtC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;QAClE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;QAEzC,IAAI,SAAS,CAAC,MAAM,KAAK,uBAAuB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACzF,KAAK,CAAC,cAAc,EAAE,CAAC;QACzB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,UAAU,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE;QACtD,yCAAyC;QACzC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC"}