import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Property, SavedProperty, PropertySearchQuery, PropertySearchResult } from '@types/index';

interface PropertyState {
  searchResults: PropertySearchResult | null;
  savedProperties: SavedProperty[];
  currentProperty: Property | null;
  searchQuery: PropertySearchQuery | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: PropertyState = {
  searchResults: null,
  savedProperties: [],
  currentProperty: null,
  searchQuery: null,
  isLoading: false,
  error: null,
};

const propertySlice = createSlice({
  name: 'property',
  initialState,
  reducers: {
    setSearchResults: (state, action: PayloadAction<PropertySearchResult>) => {
      state.searchResults = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<PropertySearchQuery>) => {
      state.searchQuery = action.payload;
    },
    setSavedProperties: (state, action: PayloadAction<SavedProperty[]>) => {
      state.savedProperties = action.payload;
    },
    addSavedProperty: (state, action: PayloadAction<SavedProperty>) => {
      state.savedProperties.push(action.payload);
    },
    removeSavedProperty: (state, action: PayloadAction<string>) => {
      state.savedProperties = state.savedProperties.filter(
        prop => prop.propertyId !== action.payload
      );
    },
    setCurrentProperty: (state, action: PayloadAction<Property | null>) => {
      state.currentProperty = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setSearchResults,
  setSearchQuery,
  setSavedProperties,
  addSavedProperty,
  removeSavedProperty,
  setCurrentProperty,
  setLoading,
  setError,
  clearError,
} = propertySlice.actions;

export default propertySlice.reducer;
