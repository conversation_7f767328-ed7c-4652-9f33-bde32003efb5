{"name": "memfs", "version": "4.17.2", "description": "In-memory file-system with Node's fs API.", "keywords": ["fs", "filesystem", "fs.js", "memory-fs", "memfs", "file", "file system", "mount", "memory", "in-memory", "virtual", "test", "testing", "mock", "fsa", "file system access", "native file system", "webfs", "crudfs", "opfs", "casfs", "content addressable storage"], "homepage": "https://github.com/streamich/memfs", "repository": {"type": "git", "url": "https://github.com/streamich/memfs.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "license": "Apache-2.0", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib", "dist", "README.md", "LICENSE", "demo/runkit.js"], "scripts": {"build": "tsc -p . && rimraf --glob lib/__tests__ 'lib/**/__tests__'", "build:webfs": "NODE_ENV=production webpack --config ./src/webfs/webpack.config.js", "clean": "rimraf lib types typedocs gh-pages coverage", "demo:crud-and-cas": "webpack serve --config ./demo/crud-and-cas/webpack.config.js", "demo:fsa-to-node-sync-tests": "webpack serve --config ./demo/fsa-to-node-sync-tests/webpack.config.js", "demo:fsa-to-node-zipfile": "webpack serve --config ./demo/fsa-to-node-zipfile/webpack.config.js", "demo:git-fsa": "webpack serve --config ./demo/git-fsa/webpack.config.js", "demo:git-opfs": "webpack serve --config ./demo/git-opfs/webpack.config.js", "demo:webfs": "webpack serve --config ./src/webfs/webpack.config.js", "prettier": "prettier --write .", "prettier:check": "prettier --check .", "test": "jest --maxWorkers 2", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "tslint": "tslint \"src/**/*.ts\" -t verbose", "typecheck": "tsc -p .", "watch": "watch \"npm run build\" ./src", "coverage": "yarn test --collectCoverage", "typedoc": "npx typedoc@0.25.13 --tsconfig tsconfig.json", "build:pages": "npx rimraf@5.0.5 gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "git-cz"}}, "prettier": {"arrowParens": "avoid", "bracketSpacing": true, "printWidth": 120, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "useTabs": false}, "release": {"branches": ["master", {"name": "next", "prerelease": true}], "prepare": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"], "verifyConditions": ["@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git"]}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "jsx"], "testEnvironment": "node", "testRegex": ".*/__tests__/.*\\.(test|spec)\\.(jsx?|tsx?)$", "transform": {"^.+\\.tsx?$": "ts-jest"}}, "dependencies": {"@jsonjoy.com/json-pack": "^1.0.3", "@jsonjoy.com/util": "^1.3.0", "tree-dump": "^1.0.1", "tslib": "^2.0.0"}, "devDependencies": {"@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^9.0.1", "@types/jest": "^29.0.0", "@types/mime": "^3.0.0", "@types/node": "^10.17.60", "app-root-path": "^3.1.0", "assert": "^2.0.0", "buffer": "^6.0.3", "html-webpack-plugin": "^5.5.3", "husky": "^8.0.1", "isomorphic-git": "^1.24.2", "jest": "^29.0.0", "path-browserify": "^1.0.1", "prettier": "^3.0.0", "process": "^0.11.10", "readable-stream": "^4.4.0", "rimraf": "^5.0.0", "semantic-release": "^19.0.3", "tar-stream": "^3.1.2", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tslint": "^6.1.3", "tslint-config-common": "^1.6.2", "typescript": "^5.1.3", "url": "^0.11.1", "util": "^0.12.5", "webpack": "^5.87.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">= 4.0.0"}}