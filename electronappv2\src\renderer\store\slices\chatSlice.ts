import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatMessage, ChatSession } from '@types/index';

interface ChatState {
  currentSession: ChatSession | null;
  messages: ChatMessage[];
  isTyping: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: ChatState = {
  currentSession: null,
  messages: [],
  isTyping: false,
  isLoading: false,
  error: null,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<ChatSession>) => {
      state.currentSession = action.payload;
      state.messages = action.payload.messages;
    },
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages.push(action.payload);
    },
    updateMessage: (state, action: PayloadAction<{ id: string; updates: Partial<ChatMessage> }>) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.id);
      if (messageIndex !== -1) {
        state.messages[messageIndex] = { ...state.messages[messageIndex], ...action.payload.updates };
      }
    },
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setCurrentSession,
  addMessage,
  updateMessage,
  setTyping,
  setLoading,
  setError,
  clearMessages,
  clearError,
} = chatSlice.actions;

export default chatSlice.reducer;
