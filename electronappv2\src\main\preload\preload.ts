import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IpcChannels } from '../../shared/constants';

/**
 * Expose protected methods that allow the renderer process to use
 * the ipcRenderer without exposing the entire object
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke(IpcChannels.GET_APP_VERSION),
  getAppName: () => ipcRenderer.invoke(IpcChannels.GET_APP_NAME),

  // User management
  getUserProfile: () => ipcRenderer.invoke(IpcChannels.GET_USER_PROFILE),
  saveUserProfile: (profile: any) => ipcRenderer.invoke(IpcChannels.SAVE_USER_PROFILE, profile),
  deleteUserProfile: () => ipcRenderer.invoke(IpcChannels.DELETE_USER_PROFILE),

  // Authentication
  loginUser: (credentials: any) => ipcRenderer.invoke(IpcChannels.LOGIN_USER, credentials),
  logoutUser: () => ipcRenderer.invoke(IpcChannels.LOGOUT_USER),
  registerUser: (userData: any) => ipcRenderer.invoke(IpcChannels.REGISTER_USER, userData),

  // Chat functionality
  sendChatMessage: (message: string) => ipcRenderer.invoke(IpcChannels.SEND_CHAT_MESSAGE, message),
  getChatHistory: (userId?: number) => ipcRenderer.invoke(IpcChannels.GET_CHAT_HISTORY, userId),
  clearChatHistory: (userId?: number) => ipcRenderer.invoke(IpcChannels.CLEAR_CHAT_HISTORY, userId),
  deleteChatMessage: (messageId: number) => ipcRenderer.invoke(IpcChannels.DELETE_CHAT_MESSAGE, messageId),

  // Property search
  searchProperties: (query: any) => ipcRenderer.invoke(IpcChannels.SEARCH_PROPERTIES, query),
  getPropertyDetails: (propertyId: string) => ipcRenderer.invoke(IpcChannels.GET_PROPERTY_DETAILS, propertyId),
  getPropertySuggestions: (criteria: any) => ipcRenderer.invoke(IpcChannels.GET_PROPERTY_SUGGESTIONS, criteria),
  getMarketTrends: (area: string) => ipcRenderer.invoke(IpcChannels.GET_MARKET_TRENDS, area),

  // Saved properties
  saveProperty: (property: any) => ipcRenderer.invoke(IpcChannels.SAVE_PROPERTY, property),
  unsaveProperty: (propertyId: string) => ipcRenderer.invoke(IpcChannels.UNSAVE_PROPERTY, propertyId),
  getSavedProperties: (userId?: number) => ipcRenderer.invoke(IpcChannels.GET_SAVED_PROPERTIES, userId),

  // Bangalore localities
  getLocalities: () => ipcRenderer.invoke(IpcChannels.GET_LOCALITIES),
  getLocalityInfo: (localityName: string) => ipcRenderer.invoke(IpcChannels.GET_LOCALITY_INFO, localityName),
  getLocalityPrices: (localityName: string) => ipcRenderer.invoke(IpcChannels.GET_LOCALITY_PRICES, localityName),

  // AI services
  queryGeminiAI: (prompt: string, context?: any) => ipcRenderer.invoke(IpcChannels.QUERY_GEMINI_AI, prompt, context),
  getAISuggestions: (userInput: string) => ipcRenderer.invoke(IpcChannels.GET_AI_SUGGESTIONS, userInput),
  analyzeProperty: (propertyData: any) => ipcRenderer.invoke(IpcChannels.ANALYZE_PROPERTY, propertyData),

  // Settings
  getSettings: () => ipcRenderer.invoke(IpcChannels.GET_SETTINGS),
  saveSettings: (settings: any) => ipcRenderer.invoke(IpcChannels.SAVE_SETTINGS, settings),
  resetSettings: () => ipcRenderer.invoke(IpcChannels.RESET_SETTINGS),

  // Database
  backupDatabase: () => ipcRenderer.invoke(IpcChannels.BACKUP_DATABASE),
  restoreDatabase: (backupPath: string) => ipcRenderer.invoke(IpcChannels.RESTORE_DATABASE, backupPath),
  clearDatabase: () => ipcRenderer.invoke(IpcChannels.CLEAR_DATABASE),

  // External APIs
  fetchExternalData: (apiName: string, params: any) => ipcRenderer.invoke(IpcChannels.FETCH_EXTERNAL_DATA, apiName, params),
  updateMarketData: () => ipcRenderer.invoke(IpcChannels.UPDATE_MARKET_DATA),

  // API key management
  getApiKey: () => ipcRenderer.invoke('get-api-key'),
  setApiKey: (apiKey: string) => ipcRenderer.invoke('set-api-key', apiKey),
  removeApiKey: () => ipcRenderer.invoke('remove-api-key'),
});

/**
 * Type definitions for the exposed API
 */
declare global {
  interface Window {
    electronAPI: {
      // App info
      getAppVersion: () => Promise<string>;
      getAppName: () => Promise<string>;

      // User management
      getUserProfile: () => Promise<any>;
      saveUserProfile: (profile: any) => Promise<boolean>;
      deleteUserProfile: () => Promise<boolean>;

      // Authentication
      loginUser: (credentials: any) => Promise<any>;
      logoutUser: () => Promise<boolean>;
      registerUser: (userData: any) => Promise<any>;

      // Chat functionality
      sendChatMessage: (message: string) => Promise<any>;
      getChatHistory: (userId?: number) => Promise<any[]>;
      clearChatHistory: (userId?: number) => Promise<boolean>;
      deleteChatMessage: (messageId: number) => Promise<boolean>;

      // Property search
      searchProperties: (query: any) => Promise<any[]>;
      getPropertyDetails: (propertyId: string) => Promise<any>;
      getPropertySuggestions: (criteria: any) => Promise<any[]>;
      getMarketTrends: (area: string) => Promise<any>;

      // Saved properties
      saveProperty: (property: any) => Promise<boolean>;
      unsaveProperty: (propertyId: string) => Promise<boolean>;
      getSavedProperties: (userId?: number) => Promise<any[]>;

      // Bangalore localities
      getLocalities: () => Promise<any[]>;
      getLocalityInfo: (localityName: string) => Promise<any>;
      getLocalityPrices: (localityName: string) => Promise<any>;

      // AI services
      queryGeminiAI: (prompt: string, context?: any) => Promise<any>;
      getAISuggestions: (userInput: string) => Promise<any[]>;
      analyzeProperty: (propertyData: any) => Promise<any>;

      // Settings
      getSettings: () => Promise<any>;
      saveSettings: (settings: any) => Promise<boolean>;
      resetSettings: () => Promise<boolean>;

      // Database
      backupDatabase: () => Promise<string>;
      restoreDatabase: (backupPath: string) => Promise<boolean>;
      clearDatabase: () => Promise<boolean>;

      // External APIs
      fetchExternalData: (apiName: string, params: any) => Promise<any>;
      updateMarketData: () => Promise<boolean>;

      // API key management
      getApiKey: () => Promise<string | null>;
      setApiKey: (apiKey: string) => Promise<boolean>;
      removeApiKey: () => Promise<boolean>;
    };
  }
}
