import { PropertyType, TransactionType } from '../shared/constants';
import { Property } from '../types/index';

/**
 * External API Service
 * Integrates with external real estate APIs, property listing services, and market data providers
 */
class ExternalApiService {
  private apiKeys: Map<string, string> = new Map();
  private rateLimits: Map<string, { requests: number; resetTime: number }> = new Map();

  /**
   * Initialize the external API service
   */
  async initialize(): Promise<void> {
    try {
      // Load API keys from secure storage
      await this.loadApiKeys();
      console.log('External API service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize external API service:', error);
      throw error;
    }
  }

  /**
   * Set API key for a specific service
   */
  setApiKey(service: string, apiKey: string): void {
    this.apiKeys.set(service, apiKey);
  }

  /**
   * Get API key for a specific service
   */
  getApiKey(service: string): string | undefined {
    return this.apiKeys.get(service);
  }

  /**
   * Fetch properties from external APIs
   */
  async fetchExternalProperties(criteria: {
    locality?: string;
    propertyType?: PropertyType;
    transactionType?: TransactionType;
    minPrice?: number;
    maxPrice?: number;
    limit?: number;
  }): Promise<Property[]> {
    try {
      const properties: Property[] = [];

      // Fetch from multiple sources in parallel
      const sources = [
        this.fetchFromMagicBricks(criteria),
        this.fetch99Acres(criteria),
        this.fetchFromHousing(criteria)
      ];

      const results = await Promise.allSettled(sources);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          properties.push(...result.value);
        } else {
          console.warn(`Failed to fetch from source ${index}:`, result.reason);
        }
      });

      // Remove duplicates and sort by relevance
      return this.deduplicateAndSort(properties, criteria);
    } catch (error) {
      console.error('Error fetching external properties:', error);
      throw error;
    }
  }

  /**
   * Fetch market data from external sources
   */
  async fetchMarketData(locality: string): Promise<any> {
    try {
      const marketData = await Promise.allSettled([
        this.fetchPriceIndexData(locality),
        this.fetchDemandSupplyData(locality),
        this.fetchInfrastructureData(locality)
      ]);

      return {
        priceIndex: marketData[0].status === 'fulfilled' ? marketData[0].value : null,
        demandSupply: marketData[1].status === 'fulfilled' ? marketData[1].value : null,
        infrastructure: marketData[2].status === 'fulfilled' ? marketData[2].value : null,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching market data:', error);
      throw error;
    }
  }

  /**
   * Fetch locality information from external sources
   */
  async fetchLocalityInfo(locality: string): Promise<any> {
    try {
      const localityData = await Promise.allSettled([
        this.fetchGooglePlacesData(locality),
        this.fetchGovernmentData(locality),
        this.fetchTransportData(locality)
      ]);

      return {
        places: localityData[0].status === 'fulfilled' ? localityData[0].value : null,
        government: localityData[1].status === 'fulfilled' ? localityData[1].value : null,
        transport: localityData[2].status === 'fulfilled' ? localityData[2].value : null,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching locality info:', error);
      throw error;
    }
  }

  /**
   * Fetch news and updates related to real estate
   */
  async fetchRealEstateNews(locality?: string): Promise<any[]> {
    try {
      const newsData = await Promise.allSettled([
        this.fetchNewsFromAPI('real estate bangalore', locality),
        this.fetchNewsFromAPI('property market bangalore', locality),
        this.fetchNewsFromAPI('bangalore infrastructure', locality)
      ]);

      const allNews: any[] = [];
      newsData.forEach(result => {
        if (result.status === 'fulfilled') {
          allNews.push(...result.value);
        }
      });

      // Sort by date and remove duplicates
      return this.deduplicateNews(allNews)
        .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
        .slice(0, 20);
    } catch (error) {
      console.error('Error fetching real estate news:', error);
      throw error;
    }
  }

  /**
   * Validate property data from external sources
   */
  async validatePropertyData(property: Property): Promise<any> {
    try {
      const validationResults = await Promise.allSettled([
        this.validateWithRERA(property),
        this.validateWithLocalAuthority(property),
        this.validatePricing(property)
      ]);

      return {
        rera: validationResults[0].status === 'fulfilled' ? validationResults[0].value : null,
        authority: validationResults[1].status === 'fulfilled' ? validationResults[1].value : null,
        pricing: validationResults[2].status === 'fulfilled' ? validationResults[2].value : null,
        overallScore: this.calculateValidationScore(validationResults),
        lastValidated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error validating property data:', error);
      throw error;
    }
  }

  /**
   * Mock implementation for MagicBricks API
   */
  private async fetchFromMagicBricks(criteria: any): Promise<Property[]> {
    // In a real implementation, this would make actual API calls
    await this.checkRateLimit('magicbricks');
    
    // Mock data for demonstration
    return [
      {
        id: 'mb_001',
        title: '3BHK Apartment in Koramangala',
        description: 'Spacious apartment with modern amenities',
        type: PropertyType.APARTMENT,
        transactionType: TransactionType.BUY,
        price: 12500000,
        area: 1450,
        bedrooms: 3,
        bathrooms: 3,
        locality: 'Koramangala',
        address: 'Koramangala 5th Block, Bangalore',
        amenities: ['Gym', 'Swimming Pool', 'Parking'],
        images: [],
        contactInfo: {
          name: 'MagicBricks Agent',
          phone: '+91 9876543210'
        },
        features: {
          parking: true,
          furnished: 'semi',
          balcony: 2,
          floor: 4,
          totalFloors: 8,
          age: 2
        },
        legalInfo: {
          rera: 'PRM/KA/RERA/1251/446/PR/010119/002056',
          approvals: ['BBMP', 'BWSSB'],
          ownership: 'freehold'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  /**
   * Mock implementation for 99Acres API
   */
  private async fetch99Acres(criteria: any): Promise<Property[]> {
    await this.checkRateLimit('99acres');
    
    // Mock data for demonstration
    return [
      {
        id: '99_001',
        title: '2BHK Apartment in Indiranagar',
        description: 'Well-maintained apartment in prime location',
        type: PropertyType.APARTMENT,
        transactionType: TransactionType.RENT,
        price: 40000,
        area: 1200,
        bedrooms: 2,
        bathrooms: 2,
        locality: 'Indiranagar',
        address: 'Indiranagar 1st Stage, Bangalore',
        amenities: ['Parking', 'Security'],
        images: [],
        contactInfo: {
          name: '99Acres Agent',
          phone: '+91 9876543211'
        },
        features: {
          parking: true,
          furnished: 'fully',
          balcony: 1,
          floor: 3,
          totalFloors: 6,
          age: 5
        },
        legalInfo: {
          rera: 'PRM/KA/RERA/1251/446/PR/010119/002057',
          approvals: ['BBMP'],
          ownership: 'freehold'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  /**
   * Mock implementation for Housing.com API
   */
  private async fetchFromHousing(criteria: any): Promise<Property[]> {
    await this.checkRateLimit('housing');
    
    // Mock data for demonstration
    return [];
  }

  /**
   * Mock implementation for price index data
   */
  private async fetchPriceIndexData(locality: string): Promise<any> {
    return {
      currentIndex: 145.6,
      yearOverYearChange: 8.2,
      quarterOverQuarterChange: 2.1,
      source: 'Property Index API'
    };
  }

  /**
   * Mock implementation for demand-supply data
   */
  private async fetchDemandSupplyData(locality: string): Promise<any> {
    return {
      demandIndex: 78,
      supplyIndex: 65,
      ratio: 1.2,
      trend: 'increasing_demand',
      source: 'Market Research API'
    };
  }

  /**
   * Mock implementation for infrastructure data
   */
  private async fetchInfrastructureData(locality: string): Promise<any> {
    return {
      metroConnectivity: true,
      busConnectivity: 'excellent',
      roadQuality: 'good',
      powerSupply: 'stable',
      waterSupply: 'adequate',
      score: 85,
      source: 'Infrastructure API'
    };
  }

  /**
   * Mock implementation for Google Places data
   */
  private async fetchGooglePlacesData(locality: string): Promise<any> {
    return {
      schools: 15,
      hospitals: 8,
      malls: 3,
      restaurants: 45,
      banks: 12,
      score: 88
    };
  }

  /**
   * Mock implementation for government data
   */
  private async fetchGovernmentData(locality: string): Promise<any> {
    return {
      pincode: '560095',
      ward: 'Ward 185',
      assembly: 'Koramangala',
      parliament: 'Bangalore Central',
      corporator: 'John Doe'
    };
  }

  /**
   * Mock implementation for transport data
   */
  private async fetchTransportData(locality: string): Promise<any> {
    return {
      nearestMetro: 'Koramangala Metro Station',
      metroDistance: 0.8,
      busStops: 5,
      connectivity: 'excellent'
    };
  }

  /**
   * Mock implementation for news API
   */
  private async fetchNewsFromAPI(query: string, locality?: string): Promise<any[]> {
    return [
      {
        title: 'Bangalore Real Estate Market Shows Strong Growth',
        description: 'Property prices in Bangalore continue to rise...',
        url: 'https://example.com/news/1',
        publishedAt: new Date().toISOString(),
        source: 'Real Estate News'
      }
    ];
  }

  /**
   * Mock implementation for RERA validation
   */
  private async validateWithRERA(property: Property): Promise<any> {
    return {
      isValid: true,
      reraNumber: property.legalInfo.rera,
      status: 'active',
      developer: 'ABC Developers',
      completionDate: '2025-12-31'
    };
  }

  /**
   * Mock implementation for local authority validation
   */
  private async validateWithLocalAuthority(property: Property): Promise<any> {
    return {
      isValid: true,
      approvals: property.legalInfo.approvals,
      buildingPlan: 'approved',
      occupancyCertificate: 'issued'
    };
  }

  /**
   * Mock implementation for pricing validation
   */
  private async validatePricing(property: Property): Promise<any> {
    const marketRate = 8500; // Mock market rate per sq ft
    const propertyRate = property.price / property.area;
    const variance = ((propertyRate - marketRate) / marketRate) * 100;

    return {
      marketRate,
      propertyRate,
      variance,
      assessment: variance > 20 ? 'overpriced' : variance < -20 ? 'underpriced' : 'fair'
    };
  }

  /**
   * Check rate limits for API calls
   */
  private async checkRateLimit(service: string): Promise<void> {
    const limit = this.rateLimits.get(service);
    const now = Date.now();

    if (limit && limit.requests >= 100 && now < limit.resetTime) {
      throw new Error(`Rate limit exceeded for ${service}. Try again later.`);
    }

    if (!limit || now >= limit.resetTime) {
      this.rateLimits.set(service, { requests: 1, resetTime: now + 3600000 }); // 1 hour
    } else {
      limit.requests++;
    }
  }

  /**
   * Remove duplicate properties and sort by relevance
   */
  private deduplicateAndSort(properties: Property[], criteria: any): Property[] {
    // Simple deduplication based on title and address
    const unique = properties.filter((property, index, self) => 
      index === self.findIndex(p => 
        p.title === property.title && p.address === property.address
      )
    );

    // Sort by relevance (price match, locality match, etc.)
    return unique.sort((a, b) => {
      let scoreA = 0, scoreB = 0;

      if (criteria.locality && a.locality.toLowerCase().includes(criteria.locality.toLowerCase())) scoreA += 10;
      if (criteria.locality && b.locality.toLowerCase().includes(criteria.locality.toLowerCase())) scoreB += 10;

      if (criteria.propertyType && a.type === criteria.propertyType) scoreA += 5;
      if (criteria.propertyType && b.type === criteria.propertyType) scoreB += 5;

      return scoreB - scoreA;
    });
  }

  /**
   * Remove duplicate news articles
   */
  private deduplicateNews(news: any[]): any[] {
    return news.filter((article, index, self) => 
      index === self.findIndex(a => a.title === article.title)
    );
  }

  /**
   * Calculate validation score
   */
  private calculateValidationScore(results: any[]): number {
    let score = 0;
    let validResults = 0;

    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        validResults++;
        if (result.value.isValid) score += 33.33;
      }
    });

    return validResults > 0 ? Math.round(score) : 0;
  }

  /**
   * Load API keys from secure storage
   */
  private async loadApiKeys(): Promise<void> {
    // In a real implementation, this would load from secure storage
    // For now, we'll use environment variables or default values
    this.apiKeys.set('magicbricks', process.env.MAGICBRICKS_API_KEY || '');
    this.apiKeys.set('99acres', process.env.ACRES99_API_KEY || '');
    this.apiKeys.set('housing', process.env.HOUSING_API_KEY || '');
    this.apiKeys.set('news', process.env.NEWS_API_KEY || '');
    this.apiKeys.set('google', process.env.GOOGLE_API_KEY || '');
  }
}

// Export singleton instance
export const externalApiService = new ExternalApiService();
