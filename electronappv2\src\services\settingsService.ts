import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { AppSettings } from '@types/index';

class SettingsService {
  private settingsPath: string;
  private settings: AppSettings | null = null;

  constructor() {
    const userDataPath = app.getPath('userData');
    this.settingsPath = path.join(userDataPath, 'settings.json');
  }

  /**
   * Load settings from file
   */
  public async loadSettings(): Promise<AppSettings> {
    try {
      const data = await fs.readFile(this.settingsPath, 'utf-8');
      this.settings = JSON.parse(data);
      return this.settings!;
    } catch (error) {
      // If file doesn't exist or is corrupted, return default settings
      this.settings = this.getDefaultSettings();
      await this.saveSettings(this.settings);
      return this.settings;
    }
  }

  /**
   * Save settings to file
   */
  public async saveSettings(settings: AppSettings): Promise<void> {
    try {
      // Ensure directory exists
      const dir = path.dirname(this.settingsPath);
      await fs.mkdir(dir, { recursive: true });

      // Save settings
      await fs.writeFile(this.settingsPath, JSON.stringify(settings, null, 2));
      this.settings = settings;
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  }

  /**
   * Get current settings
   */
  public async getSettings(): Promise<AppSettings> {
    if (!this.settings) {
      return await this.loadSettings();
    }
    return this.settings;
  }

  /**
   * Update specific setting
   */
  public async updateSetting<K extends keyof AppSettings>(
    category: K,
    updates: Partial<AppSettings[K]>
  ): Promise<void> {
    const currentSettings = await this.getSettings();
    currentSettings[category] = { ...currentSettings[category], ...updates };
    await this.saveSettings(currentSettings);
  }

  /**
   * Get API key (encrypted storage would be better for production)
   */
  public async getApiKey(): Promise<string | null> {
    const settings = await this.getSettings();
    return (settings as any).apiKey || null;
  }

  /**
   * Set API key (encrypted storage would be better for production)
   */
  public async setApiKey(apiKey: string): Promise<void> {
    const settings = await this.getSettings();
    (settings as any).apiKey = apiKey;
    await this.saveSettings(settings);
  }

  /**
   * Remove API key
   */
  public async removeApiKey(): Promise<void> {
    const settings = await this.getSettings();
    delete (settings as any).apiKey;
    await this.saveSettings(settings);
  }

  /**
   * Reset settings to default
   */
  public async resetSettings(): Promise<AppSettings> {
    const defaultSettings = this.getDefaultSettings();
    await this.saveSettings(defaultSettings);
    return defaultSettings;
  }

  /**
   * Get default settings
   */
  public getDefaultSettings(): AppSettings {
    return {
      general: {
        language: 'en',
        theme: 'system',
        autoUpdate: true,
        startMinimized: false,
      },
      ai: {
        model: 'gemini-2.5-flash',
        temperature: 0.7,
        maxTokens: 8192,
        contextLength: 10,
      },
      privacy: {
        saveConversations: true,
        shareUsageData: false,
        autoDeleteOldChats: false,
        retentionDays: 30,
      },
      notifications: {
        priceAlerts: true,
        newListings: true,
        marketUpdates: true,
        systemUpdates: true,
      },
    };
  }

  /**
   * Validate settings structure
   */
  private validateSettings(settings: any): settings is AppSettings {
    return (
      settings &&
      typeof settings === 'object' &&
      settings.general &&
      settings.ai &&
      settings.privacy &&
      settings.notifications
    );
  }

  /**
   * Migrate settings from older versions
   */
  private migrateSettings(settings: any): AppSettings {
    const defaultSettings = this.getDefaultSettings();
    
    // Merge with defaults to ensure all properties exist
    return {
      general: { ...defaultSettings.general, ...settings.general },
      ai: { ...defaultSettings.ai, ...settings.ai },
      privacy: { ...defaultSettings.privacy, ...settings.privacy },
      notifications: { ...defaultSettings.notifications, ...settings.notifications },
    };
  }
}

// Export singleton instance
export const settingsService = new SettingsService();
export default settingsService;
