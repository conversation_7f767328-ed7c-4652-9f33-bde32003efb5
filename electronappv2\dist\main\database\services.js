"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchService = exports.localityService = exports.propertyService = exports.chatService = exports.userService = exports.SearchService = exports.LocalityService = exports.PropertyService = exports.ChatService = exports.UserService = void 0;
const database_1 = require("./database");
/**
 * User database operations
 */
class UserService {
    constructor() {
        this.db = null;
    }
    getDb() {
        if (!this.db) {
            this.db = (0, database_1.getDatabase)();
        }
        return this.db;
    }
    async createUser(name, email, preferences) {
        const db = this.getDb();
        const result = await db.run('INSERT INTO users (name, email, preferences) VALUES (?, ?, ?)', [name, email || null, JSON.stringify(preferences || {})]);
        const user = await db.get('SELECT * FROM users WHERE id = ?', [result.lastID]);
        return {
            ...user,
            preferences: JSON.parse(user.preferences || '{}')
        };
    }
    async getUserById(id) {
        const db = this.getDb();
        const user = await db.get('SELECT * FROM users WHERE id = ?', [id]);
        if (!user)
            return null;
        return {
            ...user,
            preferences: JSON.parse(user.preferences || '{}')
        };
    }
    async getUserByEmail(email) {
        const db = this.getDb();
        const user = await db.get('SELECT * FROM users WHERE email = ?', [email]);
        if (!user)
            return null;
        return {
            ...user,
            preferences: JSON.parse(user.preferences || '{}')
        };
    }
    async updateUserPreferences(userId, preferences) {
        const db = this.getDb();
        await db.run('UPDATE users SET preferences = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [JSON.stringify(preferences), userId]);
    }
    async updateUserApiKey(userId, apiKey) {
        const db = this.getDb();
        await db.run('UPDATE users SET api_key = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [apiKey, userId]);
    }
}
exports.UserService = UserService;
/**
 * Chat/Conversation database operations
 */
class ChatService {
    constructor() {
        this.db = null;
    }
    getDb() {
        if (!this.db) {
            this.db = (0, database_1.getDatabase)();
        }
        return this.db;
    }
    async saveMessage(userId, message, response, messageType = 'user', tokensUsed = 0) {
        const db = this.getDb();
        const result = await db.run('INSERT INTO conversations (user_id, message, response, message_type, tokens_used) VALUES (?, ?, ?, ?, ?)', [userId, message, response || null, messageType, tokensUsed]);
        return result.lastID;
    }
    async getChatHistory(userId, limit = 50) {
        const db = this.getDb();
        const rows = await db.all('SELECT * FROM conversations WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?', [userId, limit]);
        return rows.map(row => ({
            id: row.id.toString(),
            message: row.message,
            response: row.response,
            type: row.message_type,
            timestamp: row.timestamp,
            tokensUsed: row.tokens_used
        }));
    }
    async clearChatHistory(userId) {
        const db = this.getDb();
        await db.run('DELETE FROM conversations WHERE user_id = ?', [userId]);
    }
    async deleteMessage(messageId) {
        const db = this.getDb();
        await db.run('DELETE FROM conversations WHERE id = ?', [messageId]);
    }
    async getTokenUsage(userId, days = 30) {
        const db = this.getDb();
        const result = await db.get('SELECT SUM(tokens_used) as total FROM conversations WHERE user_id = ? AND timestamp >= datetime("now", "-" || ? || " days")', [userId, days]);
        return result?.total || 0;
    }
}
exports.ChatService = ChatService;
/**
 * Property database operations
 */
class PropertyService {
    constructor() {
        this.db = null;
    }
    getDb() {
        if (!this.db) {
            this.db = (0, database_1.getDatabase)();
        }
        return this.db;
    }
    async saveProperty(userId, propertyId, propertyData, notes) {
        const db = this.getDb();
        const result = await db.run('INSERT INTO saved_properties (user_id, property_id, property_data, notes) VALUES (?, ?, ?, ?)', [userId, propertyId, JSON.stringify(propertyData), notes || null]);
        return result.lastID;
    }
    async getSavedProperties(userId) {
        const db = this.getDb();
        const rows = await db.all('SELECT * FROM saved_properties WHERE user_id = ? ORDER BY saved_at DESC', [userId]);
        return rows.map(row => ({
            id: row.id,
            userId: row.user_id,
            propertyId: row.property_id,
            propertyData: JSON.parse(row.property_data),
            notes: row.notes,
            savedAt: row.saved_at
        }));
    }
    async removeProperty(userId, propertyId) {
        const db = this.getDb();
        await db.run('DELETE FROM saved_properties WHERE user_id = ? AND property_id = ?', [userId, propertyId]);
    }
    async updatePropertyNotes(userId, propertyId, notes) {
        const db = this.getDb();
        await db.run('UPDATE saved_properties SET notes = ? WHERE user_id = ? AND property_id = ?', [notes, userId, propertyId]);
    }
}
exports.PropertyService = PropertyService;
/**
 * Locality database operations
 */
class LocalityService {
    constructor() {
        this.db = null;
    }
    getDb() {
        if (!this.db) {
            this.db = (0, database_1.getDatabase)();
        }
        return this.db;
    }
    async searchLocalities(query) {
        const db = this.getDb();
        const rows = await db.all('SELECT * FROM localities WHERE name LIKE ? OR area LIKE ? OR description LIKE ? ORDER BY name', [`%${query}%`, `%${query}%`, `%${query}%`]);
        return rows;
    }
    async getLocalityByName(name) {
        const db = this.getDb();
        return await db.get('SELECT * FROM localities WHERE name = ?', [name]);
    }
    async getAllLocalities() {
        const db = this.getDb();
        return await db.all('SELECT * FROM localities ORDER BY name');
    }
    async getLocalitiesByZone(zone) {
        const db = this.getDb();
        return await db.all('SELECT * FROM localities WHERE zone = ? ORDER BY name', [zone]);
    }
    async updateLocalityPrice(name, avgPricePerSqft) {
        const db = this.getDb();
        await db.run('UPDATE localities SET avg_price_per_sqft = ? WHERE name = ?', [avgPricePerSqft, name]);
    }
}
exports.LocalityService = LocalityService;
/**
 * Search analytics database operations
 */
class SearchService {
    constructor() {
        this.db = null;
    }
    getDb() {
        if (!this.db) {
            this.db = (0, database_1.getDatabase)();
        }
        return this.db;
    }
    async logSearch(userId, query, filters, resultsCount) {
        const db = this.getDb();
        await db.run('INSERT INTO property_searches (user_id, search_query, filters, results_count) VALUES (?, ?, ?, ?)', [userId, query, JSON.stringify(filters), resultsCount]);
    }
    async getSearchHistory(userId, limit = 20) {
        const db = this.getDb();
        return await db.all('SELECT * FROM property_searches WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?', [userId, limit]);
    }
    async getPopularSearches(days = 7, limit = 10) {
        const db = this.getDb();
        return await db.all('SELECT search_query, COUNT(*) as count FROM property_searches WHERE timestamp >= datetime("now", "-" || ? || " days") GROUP BY search_query ORDER BY count DESC LIMIT ?', [days, limit]);
    }
}
exports.SearchService = SearchService;
// Export service instances
exports.userService = new UserService();
exports.chatService = new ChatService();
exports.propertyService = new PropertyService();
exports.localityService = new LocalityService();
exports.searchService = new SearchService();
//# sourceMappingURL=services.js.map