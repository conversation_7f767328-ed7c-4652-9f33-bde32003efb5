import { Database } from 'sqlite';
import * as sqlite3 from 'sqlite3';
import { getDatabase } from './database';
import { User, UserPreferences, ChatMessage, Property, SavedProperty } from '../types/index';

/**
 * User database operations
 */
export class UserService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  private getDb(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  async createUser(name: string, email?: string, preferences?: UserPreferences): Promise<User> {
    const db = this.getDb();
    const result = await db.run(
      'INSERT INTO users (name, email, preferences) VALUES (?, ?, ?)',
      [name, email || null, JSON.stringify(preferences || {})]
    );

    const user = await db.get('SELECT * FROM users WHERE id = ?', [result.lastID]);
    return {
      ...user,
      preferences: JSON.parse(user.preferences || '{}')
    };
  }

  async getUserById(id: number): Promise<User | null> {
    const db = this.getDb();
    const user = await db.get('SELECT * FROM users WHERE id = ?', [id]);
    if (!user) return null;

    return {
      ...user,
      preferences: JSON.parse(user.preferences || '{}')
    };
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const db = this.getDb();
    const user = await db.get('SELECT * FROM users WHERE email = ?', [email]);
    if (!user) return null;

    return {
      ...user,
      preferences: JSON.parse(user.preferences || '{}')
    };
  }

  async updateUserPreferences(userId: number, preferences: UserPreferences): Promise<void> {
    const db = this.getDb();
    await db.run(
      'UPDATE users SET preferences = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [JSON.stringify(preferences), userId]
    );
  }

  async updateUserApiKey(userId: number, apiKey: string): Promise<void> {
    const db = this.getDb();
    await db.run(
      'UPDATE users SET api_key = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [apiKey, userId]
    );
  }
}

/**
 * Chat/Conversation database operations
 */
export class ChatService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  private getDb(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  async saveMessage(
    userId: number,
    message: string,
    response?: string,
    messageType: string = 'user',
    tokensUsed: number = 0
  ): Promise<number> {
    const db = this.getDb();
    const result = await db.run(
      'INSERT INTO conversations (user_id, message, response, message_type, tokens_used) VALUES (?, ?, ?, ?, ?)',
      [userId, message, response || null, messageType, tokensUsed]
    );

    return result.lastID as number;
  }

  async getChatHistory(userId: number, limit: number = 50): Promise<ChatMessage[]> {
    const db = this.getDb();
    const rows = await db.all(
      'SELECT * FROM conversations WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
      [userId, limit]
    );

    return rows.map(row => ({
      id: row.id.toString(),
      message: row.message,
      response: row.response,
      type: row.message_type,
      timestamp: row.timestamp,
      tokensUsed: row.tokens_used
    }));
  }

  async clearChatHistory(userId: number): Promise<void> {
    const db = this.getDb();
    await db.run('DELETE FROM conversations WHERE user_id = ?', [userId]);
  }

  async deleteMessage(messageId: number): Promise<void> {
    const db = this.getDb();
    await db.run('DELETE FROM conversations WHERE id = ?', [messageId]);
  }

  async getTokenUsage(userId: number, days: number = 30): Promise<number> {
    const db = this.getDb();
    const result = await db.get(
      'SELECT SUM(tokens_used) as total FROM conversations WHERE user_id = ? AND timestamp >= datetime("now", "-" || ? || " days")',
      [userId, days]
    );

    return result?.total || 0;
  }
}

/**
 * Property database operations
 */
export class PropertyService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  private getDb(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  async saveProperty(userId: number, propertyId: string, propertyData: Property, notes?: string): Promise<number> {
    const db = this.getDb();
    const result = await db.run(
      'INSERT INTO saved_properties (user_id, property_id, property_data, notes) VALUES (?, ?, ?, ?)',
      [userId, propertyId, JSON.stringify(propertyData), notes || null]
    );

    return result.lastID as number;
  }

  async getSavedProperties(userId: number): Promise<SavedProperty[]> {
    const db = this.getDb();
    const rows = await db.all(
      'SELECT * FROM saved_properties WHERE user_id = ? ORDER BY saved_at DESC',
      [userId]
    );

    return rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      propertyId: row.property_id,
      propertyData: JSON.parse(row.property_data),
      notes: row.notes,
      savedAt: row.saved_at
    }));
  }

  async removeProperty(userId: number, propertyId: string): Promise<void> {
    const db = this.getDb();
    await db.run(
      'DELETE FROM saved_properties WHERE user_id = ? AND property_id = ?',
      [userId, propertyId]
    );
  }

  async updatePropertyNotes(userId: number, propertyId: string, notes: string): Promise<void> {
    const db = this.getDb();
    await db.run(
      'UPDATE saved_properties SET notes = ? WHERE user_id = ? AND property_id = ?',
      [notes, userId, propertyId]
    );
  }
}

/**
 * Locality database operations
 */
export class LocalityService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  private getDb(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  async searchLocalities(query: string): Promise<any[]> {
    const db = this.getDb();
    const rows = await db.all(
      'SELECT * FROM localities WHERE name LIKE ? OR area LIKE ? OR description LIKE ? ORDER BY name',
      [`%${query}%`, `%${query}%`, `%${query}%`]
    );

    return rows;
  }

  async getLocalityByName(name: string): Promise<any | null> {
    const db = this.getDb();
    return await db.get('SELECT * FROM localities WHERE name = ?', [name]);
  }

  async getAllLocalities(): Promise<any[]> {
    const db = this.getDb();
    return await db.all('SELECT * FROM localities ORDER BY name');
  }

  async getLocalitiesByZone(zone: string): Promise<any[]> {
    const db = this.getDb();
    return await db.all('SELECT * FROM localities WHERE zone = ? ORDER BY name', [zone]);
  }

  async updateLocalityPrice(name: string, avgPricePerSqft: number): Promise<void> {
    const db = this.getDb();
    await db.run(
      'UPDATE localities SET avg_price_per_sqft = ? WHERE name = ?',
      [avgPricePerSqft, name]
    );
  }
}

/**
 * Search analytics database operations
 */
export class SearchService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;

  private getDb(): Database<sqlite3.Database, sqlite3.Statement> {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  async logSearch(userId: number, query: string, filters: any, resultsCount: number): Promise<void> {
    const db = this.getDb();
    await db.run(
      'INSERT INTO property_searches (user_id, search_query, filters, results_count) VALUES (?, ?, ?, ?)',
      [userId, query, JSON.stringify(filters), resultsCount]
    );
  }

  async getSearchHistory(userId: number, limit: number = 20): Promise<any[]> {
    const db = this.getDb();
    return await db.all(
      'SELECT * FROM property_searches WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
      [userId, limit]
    );
  }

  async getPopularSearches(days: number = 7, limit: number = 10): Promise<any[]> {
    const db = this.getDb();
    return await db.all(
      'SELECT search_query, COUNT(*) as count FROM property_searches WHERE timestamp >= datetime("now", "-" || ? || " days") GROUP BY search_query ORDER BY count DESC LIMIT ?',
      [days, limit]
    );
  }
}

// Export service instances
export const userService = new UserService();
export const chatService = new ChatService();
export const propertyService = new PropertyService();
export const localityService = new LocalityService();
export const searchService = new SearchService();
